﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace OHMC.Core.Models
{
    public class Chapter : ModelBase
    {
        [Key]
        public int Id { get; set; }
        [Required, <PERSON><PERSON><PERSON><PERSON>(200), Disp<PERSON><PERSON><PERSON>("Chapter")]
        public string ChapterDescription { get; set; } = string.Empty;
        [ForeignKey("FormularyType"), DisplayName("Formulary Type")]
        public int FormularyTypeId { get; set; }
        public FormularyType FormularyType { get; set; }
        [DisplayName("Order number")]
        public int OrderNumber { get; set; }
        public int? PrintOrder { get; set; }
        [ForeignKey("PTC"), Di<PERSON><PERSON><PERSON><PERSON>("PTC")]
        public int? PTCTypeId { get; set; }
        public PTCType PTCType { get; set; }
    }
}
