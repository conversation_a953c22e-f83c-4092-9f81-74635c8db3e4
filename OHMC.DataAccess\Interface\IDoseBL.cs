﻿using OHMC.Core.Common.Response;
using OHMC.Core.Models;

namespace OHMC.DataAccess.Interface
{
    public interface IDoseBL
    {
        Task<Dose> GetDoseAsync(int id);
        Task<Dose> GetPlainDoseAsync(int id);
        Task<int> GetDoseCountAsync();
        Task<IEnumerable<Dose>> GetDosesAsync();
        Task<IEnumerable<Dose>> GetPagedDosesAsync(int pageNumber, int pageSize);
        Task<int> SaveAsync();
        Task<IEnumerable<Dose>> SearchDosesAsync(string desc);
        Task<int> AddDoseProductAsync(int doseId, DoseProduct product);
        Task<int> UpSert(Dose dose, int userId);
        Task<int> GetDoseProductNextOrderNumberAsync(int doseId);
        Task<DoseProduct> GetDoseProductAsync(int id);
        Task<int> RemoveDoseProductAsync(DoseProduct doseProduct);
        double GetDoseProductPercentage(Dose dose);
        double GetDoseProductTotal(Dose dose);
        Task<IEnumerable<Dose>> GetDosesByInnAsync(int id);
        Task<GeneralResponse> DeleteDose(int id, int userId);
    }
}