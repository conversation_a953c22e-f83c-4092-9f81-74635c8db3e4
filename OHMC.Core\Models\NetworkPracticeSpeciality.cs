﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace OHMC.Core.Models
{
    public class NetworkPracticeSpeciality : ModelBase
    {
        public int Id { get; set; }
        [ForeignKey("NetworkPractice")]
        public int NetworkPracticeId { get; set; }
        public NetworkPractice? NetworkPractice { get; set; }
        [MaxLength(200)]
        public string Speciality { get; set; }
    }
}

