﻿using OHMC.Core.Models;

namespace OHMC.DataAccess.Interface
{
    public interface IEvidenceRepository
    {
        Task<IEnumerable<Evidence>> GetAllEvidencesAsync();
        Task<IEnumerable<Evidence>> GetAllEvidencesPagedAsync(int pageNumber, int pageSize);
        Task<int> GetEvidenceCountAsync();
        Task<Evidence> GetEvidencesAsync(int id);
        Task<IEnumerable<Evidence>> SearchEvidenceAsync(string desc);
        void Add(Evidence evd);
        void Update(Evidence evd);
    }
}