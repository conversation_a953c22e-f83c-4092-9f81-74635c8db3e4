﻿using OHMC.Core.Models;

namespace OHMC.DataAccess.Interface
{
    public interface IDoseProductRepository
    {
        void AddProduct(DoseProduct doseProduct);
        Task<DoseProduct> GetDoseProductAsync(int id);
        Task<IEnumerable<DoseProduct>> GetDoseProductByProductAsync(int productId);
        Task<int> GetDoseProductCountAsync(int doseId);
        void RemoveDoseProduct(DoseProduct doseProduct);
        void Update(DoseProduct doseProduct);
    }
}