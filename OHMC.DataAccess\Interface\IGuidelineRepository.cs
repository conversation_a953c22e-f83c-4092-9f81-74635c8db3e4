﻿using OHMC.Core.Models;

namespace OHMC.DataAccess.Interface
{
    public interface IGuidelineRepository
    {
        void Add(Guideline guideline);
        Task<Guideline> GetGuidelineAsync(int id);
        Task<string> GetGuidelineDescriptionsAsync(int protocolId);
        Task<IEnumerable<Guideline>> GetGuidelinesAsync();
        Task<IEnumerable<Guideline>> GetOrganisationGuidelinesAsync(int organisationId);
        void Update(Guideline option);
    }
}