﻿using OHMC.Core.Models;

namespace OHMC.DataAccess.Interface
{
    public interface IFormularySchemeRuleBL
    {
        Task<FormularySchemeRule> GetFormularySchemeRuleAsync(int id);
        Task<IEnumerable<FormularySchemeRule>> GetFormularySchemeRulesAsync();
        Task<IEnumerable<FormularySchemeRule>> GetFormularySchemeRulesFullAsync();
        Task<int> SaveAsync();
        Task<int> UpSert(FormularySchemeRule rule, int userId);
    }
}