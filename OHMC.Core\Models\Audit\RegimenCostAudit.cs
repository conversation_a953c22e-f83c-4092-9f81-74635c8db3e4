﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace OHMC.Core.Models
{
    public class RegimenCostAudit : BaseAudit
    {
        [Key]
        public int RegimenCostAuditId { get; set; }
        public int RegimenCostId { get; set; }
        public int RegimenId { get; set; }
        public int TariffId { get; set; }
        public decimal Price { get; set; }
        public decimal Quantity { get; set; }
        public decimal Cost { get; set; }
        [MaxLength(100)]
        public string? Comment { get; set; }
    }
}
