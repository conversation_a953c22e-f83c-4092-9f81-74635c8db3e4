﻿using Microsoft.Extensions.Logging;
using OHMC.Core.Common.Const;
using OHMC.Core.Models;
using OHMC.DataAccess.Interface;

namespace OHMC.Application
{
    public class MedicalSchemeBL : IMedicalSchemeBL
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<MedicalSchemeBL> _logger;

        public MedicalSchemeBL(IUnitOfWork unitOfWork, ILogger<MedicalSchemeBL> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<IEnumerable<MedicalScheme>> GetMedicalSchemesAsync()
        {
            return await _unitOfWork.MedicalSchemeRepository.GetMedicalSchemesAsync();
        }

        public async Task<MedicalScheme> GetMedicalSchemeAsync(int id)
        {
            return await _unitOfWork.MedicalSchemeRepository.GetMedicalSchemeAsync(id);
        }

        public async Task<int> UpSert(MedicalScheme scheme, int userId)
        {
            scheme.UpdatedBy = userId;
            scheme.UpdatedDate = DateTime.Now;

            if (scheme.Id > 0)
            {
                _unitOfWork.MedicalSchemeRepository.Update(scheme);
                _logger.LogInformation($"{StrConst.LOG_TYPE_UPDATE}: {scheme.SchemeName}, USERID: {userId}");
            }
            else
            {
                scheme.CreateBy = userId;
                scheme.CreatedDate = DateTime.Now;

                _unitOfWork.MedicalSchemeRepository.Add(scheme);
                _logger.LogInformation($"{StrConst.LOG_TYPE_ADDING}: {scheme.SchemeName}, USERID: {userId}");
            }

            return await SaveAsync();
        }

        public async Task<int> SaveAsync()
        {
            return await _unitOfWork.SaveAsync();
        }
    }
}
