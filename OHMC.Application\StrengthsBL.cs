﻿using Microsoft.Extensions.Logging;
using OHMC.Core.Common.Const;
using OHMC.Core.Models;
using OHMC.DataAccess.Interface;

namespace OHMC.Application
{
    public class StrengthsBL : IStrengthsBL
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<StrengthsBL> _logger;

        public StrengthsBL(IUnitOfWork unitOfWork, ILogger<StrengthsBL> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<IEnumerable<Strength>> GetStrengthsAsync()
        {
            return await _unitOfWork.StrengthRepository.GetAllStrengthsAsync();
        }

        public async Task<IEnumerable<Strength>> SearchAsync(string desc)
        {
            return await _unitOfWork.StrengthRepository.SearchStrengthAsync(desc);
        }

        public async Task<Strength> GetStrengthAsync(int id)
        {
            return await _unitOfWork.StrengthRepository.GetStrengthAsync(id);
        }

        public async Task<int> UpSert(Strength strength, int userId)
        {
            strength.UpdatedBy = userId;
            strength.UpdatedDate = DateTime.Now;
            strength.StrengthDescription = strength.StrengthValue.ToString() +" "+ strength.StrengthUnit;

            if (strength.Id > 0)
            {
                _unitOfWork.StrengthRepository.Update(strength);
                _logger.LogInformation($"{StrConst.LOG_TYPE_UPDATE}: {strength.StrengthDescription}, USERID: {userId}");
            }
            else
            {
                strength.StrengthCode = await CalcNextCode(strength.StrengthValue.ToString().Substring(0, 1));
                strength.CreateBy = userId;
                strength.CreatedDate = DateTime.Now;

                _unitOfWork.StrengthRepository.Add(strength);
                _logger.LogInformation($"{StrConst.LOG_TYPE_ADDING}: {strength.StrengthDescription}, USERID: {userId}");
            }

            return await SaveAsync();
        }

        public async Task<int> RemoveStrength(int id, int userId)
        {
            var strength = await _unitOfWork.StrengthRepository.GetStrengthAsync(id);
            strength.UpdatedBy = userId;
            strength.UpdatedDate = DateTime.Now;
            strength.Deleted = true;
            strength.DeleteOn = DateTime.Now;
            _unitOfWork.StrengthRepository.Update(strength);
            _logger.LogInformation($"{StrConst.LOG_TYPE_REMOVING}: {strength.StrengthDescription}, USERID: {userId}");
            return await _unitOfWork.SaveAsync();
        }

        public async Task<int> SaveAsync()
        {
            return await _unitOfWork.SaveAsync();
        }

        private async Task<string> CalcNextCode(string prefix)
        {
            var result = string.Empty;
            int countStart = await _unitOfWork.StrengthRepository.GetCountStartingWithPrefix(prefix);
            return prefix + (countStart + 1).ToString() + "0";
        }
    }
}
