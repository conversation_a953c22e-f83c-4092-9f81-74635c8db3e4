﻿using Microsoft.Extensions.Options;
using Microsoft.Reporting.NETCore;
using OHMC.Core.Models.Settings;
using OHMC.DataAccess.Interface;

namespace OHMC.Application
{
    public class PrintServiceBL : IPrintServiceBL
    {
        private readonly IOptions<ReportConfig> _reportConfig;

        public PrintServiceBL(IOptions<ReportConfig> reportConfig)
        {
            _reportConfig = reportConfig;
        }

        public byte[] PrintDownload(string documentType, string reportName)
        {
            ServerReport report = new()
            {
                ReportServerUrl = new Uri(_reportConfig.Value.ReportServerUrl),
                ReportPath = $"{_reportConfig.Value.ReportServerPath}{reportName}"
            };
            byte[] documentReport = report.Render(documentType);
            return documentReport;
        }

        public byte[] PrintDownloadWithParameters(string documentType, string reportName, List<ReportParameterPair> reportParameters)
        {
            ServerReport report = new()
            {
                ReportServerUrl = new Uri(_reportConfig.Value.ReportServerUrl),
                ReportPath = $"{_reportConfig.Value.ReportServerPath}{reportName}"
            };

            List<ReportParameter> parameters = new();

            foreach (var item in reportParameters)
            {
                var parameter1 = new ReportParameter();
                parameter1.Name = item.ParameterName;
                parameter1.Values.Add(item.ParameterValue);
                parameters.Add(parameter1);
            }

            report.SetParameters(parameters.ToArray());
            byte[] documentReport = report.Render(documentType);
            return documentReport;
        }
    }
}
