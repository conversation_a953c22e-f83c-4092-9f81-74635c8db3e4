﻿using FoolProof.Core;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace OHMC.Core.Models
{
    public class Product : ModelBase
    {
        [Key]
        public int Id { get; set; }
        [Required]
        public decimal PackSize { get; set; }
        [Required, Max<PERSON>ength(50), Di<PERSON><PERSON><PERSON><PERSON>("Dispensing Unit")]
        public string DispensingUnit { get; set; }
        [MaxLength(20), DisplayName("Nappi")]
        [RequiredIf("Deleted", Operator.EqualTo, false, ErrorMessage = "Nappi required.")]
        public string? Nappi9 { get; set; } = string.Empty;
        [Required, MaxLength(500), DisplayName("Active Ingredient")]
        public string ActiveIngredient { get; set; } = string.Empty;
        [Required, MaxLength(500)]
        public string ProductName { get; set; } = string.Empty;
        [DisplayName("Route"), MaxLength(50)]
        public string PrimaryRoute { get; set; } = string.Empty;
        [MaxLength(50)]
        public string DosageFormType { get; set; }
        [MaxLength(50)]
        public string DosageForm { get; set; } 
        [Required]
        public decimal StrengthValue { get; set; } = decimal.Zero;         
        public string StrengthValueUnit { get; set; } = string.Empty;        
        public decimal? StrengthVolume { get; set; }
        [MaxLength(50)]
        public string? StrengthVolumeUnit { get; set; } = string.Empty;

        [Required, MaxLength(100)]
        public string StrengthDescription { get; set; } = string.Empty;
        public decimal? StandardPackSize {  get; set; }

        public decimal? SEPExclVat { get; set; } = decimal.Zero;
        [Required]
        public decimal? PricePerPack { get; set; } = decimal.Zero;
        public decimal? PricePerUnit { get; set; } = decimal.Zero;
        public decimal? PricePerDosingUnit { get; set; } = decimal.Zero;
        [MaxLength(500)]
        public string Level3Description { get; set;} = string.Empty;
        [MaxLength(500)]
        public string PresentationDescription { get; set; } = string.Empty;
        [MaxLength(30)]
        public string PacketIntake { get; set; } = string.Empty;
        [MaxLength(40)]
        public string? IsRegistered { get; set; } = string.Empty;
        [MaxLength(50)]
        public string SEPStatus {  get; set; } = string.Empty;
        public DateTime? SEPTempDate { get; set; }
        [MaxLength(50)]
        public string SEPPermanent { get; set; } = string.Empty;
        public bool IsAvailable { get; set; } = true;

        [MaxLength(200)]
        public string Manufacturer { get; set; } = string.Empty;
        public bool IsRequireDiluent { get; set; } = false;
        public bool IsDiluentIncluded { get; set; } = false;
        public bool IsBreakBulk { get; set; } = false;
        public bool IsSeries { get; set; } = false;
        [MaxLength(50)]
        public string AdministrationUnit { get; set; } = string.Empty;
        [MaxLength(20)]
        public string? DoseUnitName { get; set; }
        public List<ProductRoute>? ProductRoutes { get; set; }
        [ForeignKey("Formulation"), DisplayName("Formulation")]
        public int FormulationId { get; set; }
        public Formulation Formulation { get; set; }
        [RequiredIf("IsFDCCombo", true, ErrorMessage = "2nd Formulation Required.") ]
        public int? Formulation2Id { get; set; }
        [RequiredIf("SEPStatus", Operator.EqualTo, "Temporary", ErrorMessage = "Temporary SEP Required.")]
        public decimal? TempSEPExclVat { get; set; }
        [RequiredIf("SEPStatus", Operator.EqualTo, "Temporary", ErrorMessage = "Temporary SEP Required.")]
        public decimal? TempPricePerPack { get; set; }
        public decimal? TempPricePerUnit { get; set; }
        public decimal? TempPricePerDosingUnit { get; set; }
        public string? PriceChangeComment { get; set; }
        public bool IsFDCCombo { get; set; }
    }
}
