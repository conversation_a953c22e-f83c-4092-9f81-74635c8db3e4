﻿using OHMC.Core.Models;

namespace OHMC.DataAccess.Interface
{
    public interface IRegimenRepository
    {
        void Add(Regimen regimen);
        Task<int> GetNextRegimenCodeReference(string prefix);
        Task<IEnumerable<Regimen>> GetPagedRegimensAsync(int pageNumber, int pageSize);
        Task<Regimen> GetRegimenAsync(int id);
        Task<Regimen> GetRegimenWithChildrenAsync(int id);
        Task<IEnumerable<Regimen>> GetRegimenFormulationsAsync(int id);
        Task<int> GetRegimenCountAsync();
        Task<IEnumerable<Regimen>> GetRegimensAsync();
        Task<IEnumerable<Regimen>> SearchRegimensAsync(string desc);
        void Update(Regimen regimen);
        Task<bool> DoesCodeExists(string code, int regimenId);
    }
}