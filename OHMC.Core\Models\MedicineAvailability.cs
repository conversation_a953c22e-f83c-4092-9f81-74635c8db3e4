﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel;

namespace OHMC.Core.Models
{
    public class MedicineAvailability : ModelBase
    {
        [Key]
        public int Id { get; set; }
        [ForeignKey("Product")]
        public int ProductId { get; set; }
        public Product? Product { get; set; }
        public DateTime ReportDate { get; set; }
        [MaxLength(50)]
        public string SecurityOfSupply { get; set; } = string.Empty;
        [ForeignKey("OutOfStockSource")]
        public int? OutOfStockSourceId { get; set; }
        public OutOfStockSource? OutOfStockSource { get; set; }
        [MaxLength(100)]
        public string? SourceDetail { get; set; }
        [ForeignKey("OutOfStockReason")]
        public int? OutOfStockReasonId { get; set; }
        public OutOfStockReason? OutOfStockReason { get; set; }
        public bool IsVerified { get; set; } = false;
        [DisplayName("Date Out Of Stock")]
        public DateTime DateOutOfStock { get; set; } 
        public DateTime? DateAnticipatedAvailable { get; set; }
        [ForeignKey("OutOfStockInternationalSignal")]
        public int? OutOfStockInternationalSignalId { get; set; }
        public OutOfStockInternationalSignal? OutOfStockInternationalSignal { get; set; }
        [ForeignKey("Agency")]
        public int? AgencyId { get; set; }
        public Agency? Agency { get; set; }
        public DateTime? DateVerified { get; set; }
        [ForeignKey("Remedy")]
        public int? RemedyId { get; set; }
        public OutOfStockRemedy? Remedy { get; set; }
        public DateTime? DateBackInStock { get; set; }
        [MaxLength(100)]
        public string? Section21PermitNumber { get; set; } = string.Empty;
        [ForeignKey("Supplier")]
        public int? SupplierId { get; set; }
        public Supplier? Supplier { get; set; }
        public DateTime? PermitExpires { get; set; }
        public decimal? Price { get; set; }
        [MaxLength(200)]
        public string? Notes { get; set; }
        public DateTime? FollowUpDate { get; set; }
    }
}
