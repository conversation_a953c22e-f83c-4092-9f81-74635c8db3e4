﻿using Microsoft.Extensions.Logging;
using OHMC.Core.Common.Const;
using OHMC.Core.Models;
using OHMC.DataAccess.Interface;

namespace OHMC.Application
{
    public class SupplierBL : ISupplierBL
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<SupplierBL> _logger;

        public SupplierBL(IUnitOfWork unitOfWork, ILogger<SupplierBL> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<IEnumerable<Supplier>> GetSuppliersAsync()
        {
            return await _unitOfWork.SupplierRepository.GetSuppliersAsync();
        }

        public async Task<Supplier> GetSupplier(int id)
        {
            return await _unitOfWork.SupplierRepository.GetSupplierAsync(id);
        }

        public async Task<int> UpSert(Supplier supplier, int userId)
        {
            supplier.UpdatedBy = userId;
            supplier.UpdatedDate = DateTime.Now;

            if (supplier.Id > 0)
            {
                _unitOfWork.SupplierRepository.Update(supplier);
                _logger.LogInformation($"{StrConst.LOG_TYPE_UPDATE}: {supplier.Description}, USERID: {userId}");
            }
            else
            {
                supplier.CreateBy = userId;
                supplier.CreatedDate = DateTime.Now;

                _unitOfWork.SupplierRepository.Add(supplier);
                _logger.LogInformation($"{StrConst.LOG_TYPE_ADDING}: {supplier.Description}, USERID: {userId}");
            }

            return await SaveAsync();
        }

        public async Task<int> SaveAsync()
        {
            return await _unitOfWork.SaveAsync();
        }
    }
}
