﻿using OHMC.Core.Models;

namespace OHMC.Application
{
    public interface IMedPraxProductsBL
    {
        Task<IEnumerable<MedPraxProduct>> GetMedPraxProductsByInnAsync(string inn);
        Task<IEnumerable<MedPraxProduct>> GetMedPraxProductsByInnStrengthUnitAsync(string inn, string strength, string inn2=null, string strength2=null);
        Task<IEnumerable<MedPraxProduct>> SearchMedPraxProductsByProductNameAsync(string searchProductName);
        Task<MedPraxProduct> GetMedPraxProductByAsync(string nappi);
    }
}