﻿using Microsoft.VisualStudio.TestTools.UnitTesting;
using OpenQA.Selenium;
using OpenQA.Selenium.Chrome;

namespace OHMC.ApplicationUnitTests.FrontEndTests
{
    [TestClass]
    public class ProtocolsAppTests
    {
        private readonly string _baseUrl = "http://localhost:5091/";

        [TestInitialize]
        public void Initialize()
        {
        }

        [TestMethod]
        public void BasicLogon_Successful()
        {
            // given 
            IWebDriver driver = new ChromeDriver();
            driver.Navigate().GoToUrl(_baseUrl);
            driver.Navigate().GoToUrl($"{_baseUrl}Account/SignIn");
            IWebElement emailInput = driver.FindElement(By.Name("Email"));
            IWebElement pwdInput = driver.FindElement(By.Name("Password"));
            emailInput.Clear();
            pwdInput.Clear();
            string email = "<EMAIL>";
            string pwd = "987654";
            emailInput.SendKeys(email);
            pwdInput.SendKeys(pwd);
            IWebElement submitInput = driver.FindElement(By.Id("submit"));

            // when
            submitInput.Click();

            // then
            Assert.AreEqual(_baseUrl, driver.Url);
            driver.Close();
        }

        [TestMethod]
        public void BasicLogon_Fail()
        {
            // given
            IWebDriver driver = new ChromeDriver();
            driver.Navigate().GoToUrl(_baseUrl);
            driver.Navigate().GoToUrl($"{_baseUrl}Account/SignIn");
            string signInUrl = $"{_baseUrl}Account/SignIn";
            IWebElement emailInput = driver.FindElement(By.Name("Email"));
            IWebElement pwdInput = driver.FindElement(By.Name("Password"));
            emailInput.Clear();
            pwdInput.Clear();
            string email = "xxxxxx";
            string pwd = "xxxxxx";
            emailInput.SendKeys(email);
            pwdInput.SendKeys(pwd);
            IWebElement submitInput = driver.FindElement(By.Id("submit"));

            // when
            submitInput.Click();

            // then
            Assert.AreEqual(signInUrl, driver.Url);
            driver.Close();
        }

        [TestMethod]
        public void LogonAndSignOut_Successful()
        {
            // given 
            IWebDriver driver = new ChromeDriver();
            driver.Navigate().GoToUrl(_baseUrl);
            driver.Navigate().GoToUrl($"{_baseUrl}Account/SignIn");
            IWebElement emailInput = driver.FindElement(By.Name("Email"));
            IWebElement pwdInput = driver.FindElement(By.Name("Password"));
            emailInput.Clear();
            pwdInput.Clear();
            string email = "<EMAIL>";
            string pwd = "987654";
            emailInput.SendKeys(email);
            pwdInput.SendKeys(pwd);
            IWebElement submitInput = driver.FindElement(By.Id("submit"));

            // when
            submitInput.Click();

            IWebElement signOutButton = driver.FindElement(By.Id("signOutBtn"));
            signOutButton.Click();

            // then
            Assert.AreEqual(_baseUrl, driver.Url);
            driver.Close();
        }

        [TestMethod]
        public void LogonOnOpenProtocols()
        {
            // given
            IWebDriver driver = new ChromeDriver();
            driver.Navigate().GoToUrl(_baseUrl);
            driver.Navigate().GoToUrl($"{_baseUrl}Account/SignIn");
            IWebElement emailInput = driver.FindElement(By.Name("Email"));
            IWebElement pwdInput = driver.FindElement(By.Name("Password"));
            emailInput.Clear();
            pwdInput.Clear();
            string email = "<EMAIL>";
            string pwd = "987654";
            emailInput.SendKeys(email);
            pwdInput.SendKeys(pwd);
            IWebElement submitInput = driver.FindElement(By.Id("submit"));

            // when
            submitInput.Click();
            driver.Navigate().GoToUrl($"{_baseUrl}Protocols");

            // then 
            Assert.AreEqual($"{_baseUrl}Protocols", driver.Url);
            driver.Close();
        }

        [TestMethod]
        public void LogonOnOpenProtocolsCreate()
        {
            // given
            IWebDriver driver = new ChromeDriver();
            driver.Navigate().GoToUrl(_baseUrl);
            driver.Navigate().GoToUrl($"{_baseUrl}Account/SignIn");
            IWebElement emailInput = driver.FindElement(By.Name("Email"));
            IWebElement pwdInput = driver.FindElement(By.Name("Password"));
            emailInput.Clear();
            pwdInput.Clear();
            string email = "<EMAIL>";
            string pwd = "987654";
            emailInput.SendKeys(email);
            pwdInput.SendKeys(pwd);
            IWebElement submitInput = driver.FindElement(By.Id("submit"));

            // when
            submitInput.Click();
            driver.Navigate().GoToUrl($"{_baseUrl}Protocols");

            IWebElement createInput = driver.FindElement(By.Id("createProtocolButton"));
            createInput.Click();

            // then 
            Assert.AreEqual($"{_baseUrl}Protocols/Create", driver.Url);
            driver.Close();
        }
    }
}
