﻿using OHMC.Core.Models;

namespace OHMC.DataAccess.Interface
{
    public interface ICancerGroupRepository
    {
        void Add(CancerGroup group);
        Task<IEnumerable<CancerGroup>> GetAllCancerGroupsAsync();
        Task<CancerGroup> GetCancerGroupAsync(int id);
        Task<int> GetCancerGroupCountByPrefixAsync(string prefix);
        Task<IEnumerable<CancerGroup>> GetCancerGroupsByFormularyType(int formularyTypeId);
        Task<IEnumerable<CancerGroup>> GetCancerGroupsByPTCAsync(int ptcTypeId);
        Task<IEnumerable<CancerGroup>> SearchCancerGroupsAsync(string desc);
        void Update(CancerGroup group);
    }
}