﻿using OHMC.Core.Models;

namespace OHMC.DataAccess.Interface
{
    public interface ICancerGroupICD10Repository
    {
        void Add(CancerGroupICD10 icd10);
        void Remove(CancerGroupICD10 icd10);
        Task<CancerGroupICD10> GetCancerGroupICD10Async(int id);
        Task<IEnumerable<CancerGroupICD10>> GetCancerGroupICD10sByGroup(int cancerGroupId);
        Task<string> GetCancerGroupICD10sDescription(int cancerGroupId);
        Task<CancerGroupICD10> GetCancerGroupICD10Async(string icd10Code, int cancerGroupId);
        void Update(CancerGroupICD10 icd10);
    }
}