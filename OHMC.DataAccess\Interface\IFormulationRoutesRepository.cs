﻿using OHMC.Core.Models;

namespace OHMC.DataAccess.Interface
{
    public interface IFormulationRoutesRepository
    {
        void Add(FormulationRoute formulationRoute);
        Task<FormulationRoute> GetFormulationRouteAsync(int id);
        Task<IEnumerable<FormulationRoute>> GetFormulationRoutesByFormulationAsync(int formulationId);
        Task<IEnumerable<FormulationRoute>> GetFormulationRoutesByFormulationRouteAsync(int formulationId, string routeName);
        Task<int> GetMaxOrderNumberRouteByFormulationAsync(int id);
        void RemoveFormulationRoute(FormulationRoute formulationRoute);
        void Update(FormulationRoute formulation);
    }
}