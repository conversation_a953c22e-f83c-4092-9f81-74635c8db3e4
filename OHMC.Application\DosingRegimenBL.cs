﻿using Microsoft.Extensions.Logging;
using OHMC.Core.Common.Const;
using OHMC.Core.Common.Response;
using OHMC.Core.Common.Utils;
using OHMC.Core.Models;
using OHMC.DataAccess.Interface;

namespace OHMC.Application
{
    public class DosingRegimenBL : IDosingRegimenBL
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<DosingRegimenBL> _logger;
        private const int FILLER_COUNT = 10;
        public DosingRegimenBL(IUnitOfWork unitOfWork, ILogger<DosingRegimenBL> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<IEnumerable<DosingRegimen>> GetDosingRegimenAsync()
        {
            return await _unitOfWork.DosingRegimenRepository.GetDosingRegimenAsync();
        }

        public async Task<IEnumerable<DosingRegimen>> GetPagedDosingRegimenAsync(int pageNumber, int pageSize)
        {
            return await _unitOfWork.DosingRegimenRepository.GetPagedDosingRegimensAsync(pageNumber, pageSize);
        }

        public async Task<int> GetDosingRegimenCountAsync()
        {
            return await _unitOfWork.DosingRegimenRepository.GetDosingRegimenCountAsync();
        }

        public async Task<IEnumerable<DosingRegimen>> SearchDosingRegimenAsync(string desc)
        {
            return await _unitOfWork.DosingRegimenRepository.SearchDosingRegimensAsync(desc);
        }

        public async Task<DosingRegimen> GetDosingRegimenAsync(int id)
        {
            return await _unitOfWork.DosingRegimenRepository.GetDosingRegimenAsync(id);
        }

        public async Task<int> GetDosingRegimenProductCountAsync(int id)
        {
            var dosingRegimen = await _unitOfWork.DosingRegimenRepository.GetDosingRegimenAsync(id);
            if (dosingRegimen.DoseId == null)
            {
                return 0;
            }
            else
            {
                var dose = await _unitOfWork.DoseRepository.GetDoseAsync((int)dosingRegimen?.DoseId);
                return dose.DoseProducts == null ? 0 : dose.DoseProducts.Count();
            }
        }

        public async Task<int> UpSert(DosingRegimen dosingRegimen, int userId)
        {
            dosingRegimen.UpdatedBy = userId;
            dosingRegimen.UpdatedDate = DateTime.Now;
            dosingRegimen.DosingRegimenName = dosingRegimen.ModifiedCalculatedRegimenText;
            dosingRegimen.DoseDescriptionText = dosingRegimen.ModifiedCalculatedRegimenText;

            if (dosingRegimen.Id > 0)
            {
                _unitOfWork.DosingRegimenRepository.Update(dosingRegimen);
                _logger.LogInformation($"{StrConst.LOG_TYPE_UPDATE}: {dosingRegimen.DoseDescriptionText}, USERID: {userId}");
            }
            else
            {
                dosingRegimen.CreateBy = userId;
                dosingRegimen.CreatedDate = DateTime.Now;
                dosingRegimen.RegimenCode = await GetNextDosingRegimenCodeReference(dosingRegimen.DosingRegimenName.Substring(0, 1));

                _unitOfWork.DosingRegimenRepository.Add(dosingRegimen);
                _logger.LogInformation($"{StrConst.LOG_TYPE_ADDING}: {dosingRegimen.DoseDescriptionText}, USERID: {userId}");
            }

            await SaveAsync();
            return dosingRegimen.Id;
        }

        private async Task<string> GetNextDosingRegimenCodeReference(string prefix)
        {
            return StringHelper.AddDefaultPrefix(prefix, ((await _unitOfWork.DosingRegimenRepository.GetNextRegimenCodeReference(prefix) + 1) * 10).ToString(), FILLER_COUNT);
        }

        public async Task<RegimenDosingRegimen> GetRegimenDosingRegimen(int id)
        {
            return await _unitOfWork.RegimenDosingRegimenRepository.GetRegimenDosingRegimenAsync(id);
        }

        public async Task<int> SaveAsync()
        {
            return await _unitOfWork.SaveAsync();
        }

        public Task<int> UpSertRegimenDosingRegimen(RegimenDosingRegimen regimenDosingRegimen, int userId)
        {
            throw new NotImplementedException();
        }

        public async Task<CopyDoseRegimenResponse> CopyDoseRegimenAsync(int doseRegimenId, int userId)
        {
            var dosingRegimen = await _unitOfWork.DosingRegimenRepository.GetDosingRegimenAsync(doseRegimenId);

            var newDosingRegimen = new DosingRegimen()
            {
                DosingRegimenName = dosingRegimen.DosingRegimenName,
                RegimenCode = await GetNextDosingRegimenCodeReference(dosingRegimen.DosingRegimenName.Substring(0, 1)),
                CalculatedRegimenText = dosingRegimen.CalculatedRegimenText,
                Comment = dosingRegimen.Comment,
                CostPerRegimen = dosingRegimen.CostPerRegimen,
                DoseDescriptionText = dosingRegimen.DoseDescriptionText,
                DoseId = dosingRegimen.DoseId,
                DoseValue = dosingRegimen.DoseValue,
                DrugDays = dosingRegimen.DrugDays,
                INNId = dosingRegimen.INNId,
                ModifiedCalculatedRegimenText = dosingRegimen.ModifiedCalculatedRegimenText,
                NumberOfDaysPatientDosed = dosingRegimen.NumberOfDaysPatientDosed,
                Route = dosingRegimen.Route,
                UseAsCalc = dosingRegimen.UseAsCalc,                
                CreateBy = userId,
                CreatedDate = DateTime.Now,
                UpdatedBy = userId,
                UpdatedDate = DateTime.Now,
                Deleted = false,
            };

            _unitOfWork.DosingRegimenRepository.Add(newDosingRegimen);

            try
            {
                await SaveAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex.Message);

                return new CopyDoseRegimenResponse()
                {
                    ResponseCode = -1,
                    ResponseMessage = $"{StrConst.ACTION_COPY_FAILED}. Protocol Code {dosingRegimen.RegimenCode}",
                    NewCode = string.Empty,
                    NewId = -1,
                };
            }

            return new CopyDoseRegimenResponse()
            {
                ResponseCode = newDosingRegimen.Id,
                ResponseMessage = $"{StrConst.ACTION_COPY_SUCCESSFUL} ",
                NewCode = newDosingRegimen.RegimenCode,
                NewId = newDosingRegimen.Id
            };
        }
    }
}
