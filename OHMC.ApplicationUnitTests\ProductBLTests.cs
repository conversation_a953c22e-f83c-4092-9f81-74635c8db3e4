﻿using Bogus.Extensions;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using OHMC.Application;
using OHMC.Core.Models;
using OHMC.DataAccess.Interface;

namespace OHMC.ApplicationUnitTests
{
    [TestClass]
    public class ProductBLTests : BaseTest
    {
        private readonly Mock<IUnitOfWork> _unitOfWork = new();
        private readonly Mock<ILogger<ProductBL>> _logger = new();
        private ProductBL? _productBL;

        [TestInitialize]
        public void Initialize()
        {
            _productBL = new ProductBL(_unitOfWork.Object, _logger.Object);
        }

        [TestMethod]
        public async Task GetProduct_Should_Return_SingleProduct()
        {
            //given
            Product product = new Product()
            {
                FormulationId = 1,
                Nappi9 = _faker.Random.String2(20),
                ProductName = _faker.Random.String2(500),
                PackSize = _faker.Random.Decimal2(),
                SEPExclVat = _faker.Random.Decimal2(),
                SEPPermanent = _faker.Random.String2(20),
                PrimaryRoute = "PO",
                DosageForm = "Solid",
                DosageFormType = "Tablet",
                CreateBy = 1,
                CreatedDate = DateTime.Now,
                UpdatedBy = 1,
                UpdatedDate = DateTime.Now,
                Deleted = false
            };

            bool productUnitOfWorkRepositoryGetAsyncHssBeenCalled = false;
            _unitOfWork.Setup(x => x.ProductRepository.GetProductAsync(It.IsAny<int>()))
                .Callback(() => productUnitOfWorkRepositoryGetAsyncHssBeenCalled = true)
                .Returns(Task.FromResult(product));

            //when 
            var result = await _productBL.GetProductAsync(1);

            //then
            Assert.IsNotNull(result);
            productUnitOfWorkRepositoryGetAsyncHssBeenCalled.Should().BeTrue();
        } 
    }
}
