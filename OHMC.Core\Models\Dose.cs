﻿using FoolProof.Core;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace OHMC.Core.Models
{
    public class Dose : ModelBase
    {
        [Key]
        public int Id { get; set; }
        [MaxLength(15)]
        public string DoseCodeRef { get; set; } = string.Empty;

        [ForeignKey("INN"), DisplayName("Active Ingredient")]
        public int INNId { get; set; }
        public INN INN { get; set; }

        [DisplayName("2nd Ingredient")]
        public int? INN2Id { get; set; }
        public INN? INN2 { get; set; }

        [MaxLength(10), DisplayName("Route")]
        public string Route { get; set; } = string.Empty;
        public double DoseLow { get; set; }
        public double? DoseLow2 { get; set; }
        public double? DoseHigh { get; set; }
        [MaxLength(20)]
        public string DoseUnit { get; set; } = string.Empty;
        [MaxLength(20)]
        public string? DoseUnit2 { get; set; } = string.Empty;
        public bool IsInterval { get; set; } = false;
        [MaxLength(20)]
        [RequiredIf("IsInterval", Operator.EqualTo, true, ErrorMessage = "Interval Type required.")]
        public string? IntervalHourlyOrTimesADay { get; set; } = string.Empty;
        [RequiredIf("IntervalHourlyOrTimesADay", Operator.EqualTo, "Hourly", ErrorMessage = "Hourly required.")]
        public int? HourlyValue { get; set; }
        public int? TimesADayValue { get; set; }
        public int? ConfirmDosesInADay { get; set; }
        [MaxLength(20)]
        [RequiredIf("IntervalHourlyOrTimesADay", Operator.EqualTo, "Times a Day", ErrorMessage = "Abbreaviation required.")]
        public string? Abbreviation { get; set; }
        public string DoseDescriptionCalc { get; set; } = string.Empty;
        public bool IsDoseDescriptionCorrect { get; set; } = true;
        [DisplayName("Dose Description")]
        public string DoseDescriptionCalcEdited { get; set; } = string.Empty;
        public double? StdDoseCalc { get; set; }
        public double? DoseLowCalc { get; set; }
        public double? DoseHighCalc { get; set; }

        [MaxLength(20)]
        public string? RoundingMethod { get; set; }
        public int? CustomRoundingNumber { get; set; }

        public double? StdRoundedDose { get; set; }
        public double? DoseLowRounded { get; set; }
        public double? DoseHighRounded { get; set; }
        public List<DoseProduct>? DoseProducts { get; set; }
    }
}
