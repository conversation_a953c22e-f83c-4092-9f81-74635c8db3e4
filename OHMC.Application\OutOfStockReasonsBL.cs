﻿using Microsoft.Extensions.Logging;
using OHMC.Core.Common.Const;
using OHMC.Core.Models;
using OHMC.DataAccess.Interface;

namespace OHMC.Application
{
    public class OutOfStockReasonsBL : IOutOfStockReasonsBL
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<OutOfStockReasonsBL> _logger;

        public OutOfStockReasonsBL(IUnitOfWork unitOfWork, ILogger<OutOfStockReasonsBL> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<IEnumerable<OutOfStockReason>> GetOutOfStockReasonsAsync()
        {
            return await _unitOfWork.OutOfStockReasonRepository.GetOutOfStockReasonsAsync();
        }

        public async Task<OutOfStockReason> GetOutOfStockReason(int id)
        {
            return await _unitOfWork.OutOfStockReasonRepository.GetOutOfStockReasonAsync(id);
        }

        public async Task<int> UpSert(OutOfStockReason reason, int userId)
        {
            reason.UpdatedBy = userId;
            reason.UpdatedDate = DateTime.Now;

            if (reason.Id > 0)
            {
                _unitOfWork.OutOfStockReasonRepository.Update(reason);
                _logger.LogInformation($"{StrConst.LOG_TYPE_UPDATE}: {reason.Description}, USERID: {userId}");
            }
            else
            {
                reason.CreateBy = userId;
                reason.CreatedDate = DateTime.Now;

                _unitOfWork.OutOfStockReasonRepository.Add(reason);
                _logger.LogInformation($"{StrConst.LOG_TYPE_ADDING}: {reason.Description}, USERID: {userId}");
            }

            return await SaveAsync();
        }

        public async Task<int> SaveAsync()
        {
            return await _unitOfWork.SaveAsync();
        }
    }
}
