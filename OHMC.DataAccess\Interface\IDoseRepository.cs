﻿using OHMC.Core.Models;

namespace OHMC.DataAccess.Interface
{
    public interface IDoseRepository
    {
        void Add(Dose dose);
        Task<Dose> GetDoseAsync(int id);
        Task<Dose> GetPlainDoseAsync(int id);
        Task<int> GetDoseCountByPrefixAsync(string prefix);
        Task<int> GetDoseCounts();
        Task<IEnumerable<Dose>> GetDosesAsync();
        Task<IEnumerable<Dose>> GetPagedDosesAsync(int pageNumber, int pageSize);
        Task<IEnumerable<Dose>> SearchDosesAsync(string desc);
        void Update(Dose dose);
        Task<IEnumerable<Dose>> GetDosesByInnAsync(int id);
    }
}