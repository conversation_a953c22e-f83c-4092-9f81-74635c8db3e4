﻿using FluentAssertions;
using Microsoft.Extensions.Logging;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using OHMC.Application;
using OHMC.Core.Models;
using OHMC.DataAccess.Interface;

namespace OHMC.ApplicationUnitTests
{
    [TestClass]
    public class TariffBLTests : BaseTest
    {
        private readonly Mock<IUnitOfWork> _unitOfWork = new();
        private readonly Mock<ILogger<TariffBL>> _logger = new();
        private TariffBL _tariffBL;

        [TestInitialize]
        public void Initialize()
        {
            _tariffBL = new TariffBL(_unitOfWork.Object, _logger.Object);
        }

        [TestMethod]
        public async Task Add_New_Tariff_With_No_Duplicate_Tariff_Should_Save_Successfully()
        {
            //given 
            Tariff tariff = new Tariff()
            {
                Id = 0,
                Code = _faker.Random.String(20),
                ShortDescription = _faker.Random.String(100),
                LongDescription = _faker.Random.String(400),
                TariffTypeId = _faker.Random.Int(20),
                CreateBy = 1,
                CreatedDate = DateTime.Now,
                UpdatedBy = 1,
                UpdatedDate = DateTime.Now,
                Deleted = false
            };

            bool doTariffsExistByTypeAsyncHasBeenCalled = false;
            _unitOfWork.Setup(x => x.TariffRepository.DoTariffsExistByCodeAsync(It.IsAny<int>(), It.IsAny<string>()))
                .Callback(() => doTariffsExistByTypeAsyncHasBeenCalled = true)
                .Returns(Task.FromResult(false));


            bool tariffRepositoryAddHasBeenCalled = false;
            _unitOfWork.Setup(x => x.TariffRepository.Add(tariff))
                .Callback(() => tariffRepositoryAddHasBeenCalled = true);

            bool saveTariffHasBeenCalled = false;
            _unitOfWork.Setup(x => x.SaveAsync())
                .Callback(() => saveTariffHasBeenCalled = true)
                .Returns(Task.FromResult(1));

            //when 
            var result = await _tariffBL.UpSertTariffAsync(tariff, 1);

            //then
            Assert.IsNotNull(result);
            doTariffsExistByTypeAsyncHasBeenCalled.Should().BeTrue();
            tariffRepositoryAddHasBeenCalled.Should().BeTrue();
            saveTariffHasBeenCalled.Should().BeTrue();
        }

        [TestMethod]
        public async Task Update_Tariff_With_No_Duplicate_Tariff_Should_Save_Successfully()
        {
            //given 
            Tariff tariff = new Tariff()
            {
                Id = 1,
                Code = _faker.Random.String(20),
                ShortDescription = _faker.Random.String(100),
                LongDescription = _faker.Random.String(400),
                TariffTypeId = _faker.Random.Int(20),
                CreateBy = 1,
                CreatedDate = DateTime.Now,
                UpdatedBy = 1,
                UpdatedDate = DateTime.Now,
                Deleted = false
            };

            bool doTariffsExistByTypeAsyncHasBeenCalled = false;
            _unitOfWork.Setup(x => x.TariffRepository.DoTariffsExistByCodeAsync(It.IsAny<int>(), It.IsAny<string>()))
                .Callback(() => doTariffsExistByTypeAsyncHasBeenCalled = true)
                .Returns(Task.FromResult(false));


            bool tariffRepositoryUpdateHasBeenCalled = false;
            _unitOfWork.Setup(x => x.TariffRepository.Update(tariff))
                .Callback(() => tariffRepositoryUpdateHasBeenCalled = true);

            bool saveTariffHasBeenCalled = false;
            _unitOfWork.Setup(x => x.SaveAsync())
                .Callback(() => saveTariffHasBeenCalled = true)
                .Returns(Task.FromResult(1));

            //when 
            var result = await _tariffBL.UpSertTariffAsync(tariff, 1);

            //then
            Assert.IsNotNull(result);
            doTariffsExistByTypeAsyncHasBeenCalled.Should().BeTrue();
            tariffRepositoryUpdateHasBeenCalled.Should().BeTrue();
            saveTariffHasBeenCalled.Should().BeTrue();
        }

        [TestMethod]
        public async Task Add_New_Tariff_With_Duplicate_Tariff_Should_Fail()
        {
            //given
            Tariff tariff = new Tariff()
            {
                Id = 0,
                Code = _faker.Random.String(20),
                ShortDescription = _faker.Random.String(100),
                LongDescription = _faker.Random.String(400),
                TariffTypeId = _faker.Random.Int(20),
                CreateBy = 1,
                CreatedDate = DateTime.Now,
                UpdatedBy = 1,
                UpdatedDate = DateTime.Now,
                Deleted = false
            };

            bool doTariffsExistByTypeAsyncHasBeenCalled = false;
            _unitOfWork.Setup(x => x.TariffRepository.DoTariffsExistByCodeAsync(It.IsAny<int>(), It.IsAny<string>()))
                .Callback(() => doTariffsExistByTypeAsyncHasBeenCalled = true)
                .Returns(Task.FromResult(true));


            bool tariffRepositoryAddHasBeenCalled = false;
            _unitOfWork.Setup(x => x.TariffRepository.Add(tariff))
                .Callback(() => tariffRepositoryAddHasBeenCalled = false);

            bool saveTariffHasBeenCalled = false;
            _unitOfWork.Setup(x => x.SaveAsync())
                .Callback(() => saveTariffHasBeenCalled = false);

            //when 
            var result = await _tariffBL.UpSertTariffAsync(tariff, 1);

            //then
            Assert.IsNotNull(result);
            doTariffsExistByTypeAsyncHasBeenCalled.Should().BeTrue();
            tariffRepositoryAddHasBeenCalled.Should().BeFalse();
            saveTariffHasBeenCalled.Should().BeFalse();
            result.ResponseMessage.Should().Contain("exists");
        }

        [TestMethod]
        public async Task Remove_Tariff_Successfully()
        {
            //given
            Tariff originalTariff = new Tariff()
            {
                Id = 1,
                Code = _faker.Random.String(20),
                ShortDescription = _faker.Random.String(100),
                LongDescription = _faker.Random.String(400),
                TariffTypeId = _faker.Random.Int(20),
                CreateBy = 1,
                CreatedDate = DateTime.Now,
                UpdatedBy = 1,
                UpdatedDate = DateTime.Now,
                Deleted = false
            };

            bool getTariffAsyncHasBeenCalled = false;
            _unitOfWork.Setup(x => x.TariffRepository.GetTariffAsync(It.IsAny<int>()))
                .Callback(() => getTariffAsyncHasBeenCalled = true)
                .Returns(Task.FromResult(originalTariff));

            bool updateTariffHasBeenCalled = false;
            _unitOfWork.Setup(x => x.TariffRepository.Update(originalTariff))
                .Callback(() => updateTariffHasBeenCalled = true);

            bool saveTariffHasBeenCalled = false;
            _unitOfWork.Setup(x => x.SaveAsync())
                .Callback(() => saveTariffHasBeenCalled = true)
                .Returns(Task.FromResult(1));

            originalTariff.Deleted = true;
            originalTariff.DeleteOn = DateTime.Now;

            //when 
            var result = await _tariffBL.RemoveTariffAsync(originalTariff.Id, 1);

            //then
            Assert.IsNotNull(result);
            getTariffAsyncHasBeenCalled.Should().BeTrue();
            updateTariffHasBeenCalled.Should().BeTrue();
            saveTariffHasBeenCalled.Should().BeTrue();
            result.Should().BeGreaterThan(0);
        }
    }
}
