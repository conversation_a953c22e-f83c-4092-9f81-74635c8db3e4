﻿using Microsoft.Extensions.Logging;
using OHMC.Core.Common.Const;
using OHMC.Core.Models;
using OHMC.DataAccess.Interface;

namespace OHMC.Application
{
    public class ModalityBL : IModalityBL
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<ModalityBL> _logger;

        public ModalityBL(IUnitOfWork unitOfWork, ILogger<ModalityBL> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<IEnumerable<Modality>> GetModalitiesAsync()
        {
            return await _unitOfWork.ModalityRepository.GetModalitiesAsync();
        }

        public async Task<Modality> GetModalityAsync(int id)
        {
            return await _unitOfWork.ModalityRepository.GetModalityAsync(id);
        }

        public async Task<int> UpSert(Modality option, int userId)
        {
            option.UpdatedBy = userId;
            option.UpdatedDate = DateTime.Now;

            if (option.Id > 0)
            {
                _unitOfWork.ModalityRepository.Update(option);
                _logger.LogInformation($"{StrConst.LOG_TYPE_UPDATE}: {option.ModalityDescription}, USERID: {userId}");
            }
            else
            {
                option.CreateBy = userId;
                option.CreatedDate = DateTime.Now;

                _unitOfWork.ModalityRepository.Add(option);
                _logger.LogInformation($"{StrConst.LOG_TYPE_ADDING}: {option.ModalityDescription}, USERID: {userId}");
            }

            return await SaveAsync();
        }

        public async Task<int> RemoveModalityAsync(int id, int userId)
        {
            var option = await _unitOfWork.ModalityRepository.GetModalityAsync(id);
            option.UpdatedBy = userId;
            option.UpdatedDate = DateTime.Now;
            option.Deleted = true;
            option.DeleteOn = DateTime.Now;
            _unitOfWork.ModalityRepository.Update(option);
            _logger.LogInformation($"{StrConst.LOG_TYPE_REMOVING}: {option.ModalityDescription}, USERID: {userId}");
            return await _unitOfWork.SaveAsync();
        }

        public async Task<int> SaveAsync()
        {
            return await _unitOfWork.SaveAsync();
        }
    }
}
