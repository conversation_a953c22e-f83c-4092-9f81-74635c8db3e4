﻿var foolproof=function(){};foolproof.is=function(n,t,i,r){if(r){var u=function(n){return n==null||n==undefined||n==""},f=u(n),e=u(i);if(f&&!e||e&&!f)return!0}var o=function(n){return+n==n&&n.length>0},s=function(n){var t=new RegExp(/(?=\d)^(?:(?!(?:10\D(?:0?[5-9]|1[0-4])\D(?:1582))|(?:0?9\D(?:0?[3-9]|1[0-3])\D(?:1752)))((?:0?[13578]|1[02])|(?:0?[469]|11)(?!\/31)(?!-31)(?!\.31)|(?:0?2(?=.?(?:(?:29.(?!000[04]|(?:(?:1[^0-6]|[2468][^048]|[3579][^26])00))(?:(?:(?:\d\d)(?:[02468][048]|[13579][26])(?!\x20BC))|(?:00(?:42|3[0369]|2[147]|1[258]|09)\x20BC))))))|(?:0?2(?=.(?:(?:\d\D)|(?:[01]\d)|(?:2[0-8])))))([-.\/])(0?[1-9]|[12]\d|3[01])\2(?!0000)((?=(?:00(?:4[0-5]|[0-3]?\d)\x20BC)|(?:\d{4}(?!\x20BC)))\d{4}(?:\x20BC)?)(?:$|(?=\x20\d)\x20))?((?:(?:0?[1-9]|1[012])(?::[0-5]\d){0,2}(?:\x20[aApP][mM]))|(?:[01]\d|2[0-3])(?::[0-5]\d){1,2})?$/);return t.test(n)},h=function(n){return n===!0||n===!1||n==="true"||n==="false"};s(n)?(n=Date.parse(n),i=Date.parse(i)):h(n)?(n=="false"&&(n=!1),i=="false"&&(i=!1),n=!!n,i=!!i):o(n)&&(n=parseFloat(n),i=parseFloat(i));switch(t){case"EqualTo":if(n==i)return!0;break;case"NotEqualTo":if(n!=i)return!0;break;case"GreaterThan":if(n>i)return!0;break;case"LessThan":if(n<i)return!0;break;case"GreaterThanOrEqualTo":if(n>=i)return!0;break;case"LessThanOrEqualTo":if(n<=i)return!0;break;case"RegExMatch":return new RegExp(i).test(n);case"NotRegExMatch":return!new RegExp(i).test(n)}return!1};foolproof.getId=function(n,t){var i=n.id.lastIndexOf("_")+1;return n.id.substr(0,i)+t.replace(/\./g,"_")};foolproof.getName=function(n,t){var i=n.name.lastIndexOf(".")+1;return n.name.substr(0,i)+t},function(){jQuery.validator.addMethod("is",function(n,t,i){var r=foolproof.getId(t,i.dependentproperty),u=i.operator,f=i.passonnull,e=document.getElementById(r).value;return foolproof.is(n,u,e,f)?!0:!1});jQuery.validator.addMethod("requiredif",function(n,t,i){var o=foolproof.getName(t,i.dependentproperty),s=i.dependentvalue,h=i.operator,e=i.pattern,r=document.getElementsByName(o),u=null,f;if(r.length>1){for(f=0;f!=r.length;f++)if(r[f].checked){u=r[f].value;break}u==null&&(u=!1)}else r.length&&(u=r[0].value);if(u&&foolproof.is(u,h,s))if(e==null){if(n!=null&&n.toString().replace(/^\s\s*/,"").replace(/\s\s*$/,"")!="")return!0}else return new RegExp(e).test(n);else return!0;return!1});jQuery.validator.addMethod("requiredifempty",function(n,t,i){var u=foolproof.getId(t,i.dependentproperty),r=document.getElementById(u).value;if(r==null||r.toString().replace(/^\s\s*/,"").replace(/\s\s*$/,"")==""){if(n!=null&&n.toString().replace(/^\s\s*/,"").replace(/\s\s*$/,"")!="")return!0}else return!0;return!1});jQuery.validator.addMethod("requiredifnotempty",function(n,t,i){var u=foolproof.getId(t,i.dependentproperty),r=document.getElementById(u).value;if(r!=null&&r.toString().replace(/^\s\s*/,"").replace(/\s\s*$/,"")!=""){if(n!=null&&n.toString().replace(/^\s\s*/,"").replace(/\s\s*$/,"")!="")return!0}else return!0;return!1});var n=function(n,t,i){n.rules[t]=i;n.message&&(n.messages[t]=n.message)},t=$.validator.unobtrusive;t.adapters.add("requiredif",["dependentproperty","dependentvalue","operator","pattern"],function(t){var i={dependentproperty:t.params.dependentproperty,dependentvalue:t.params.dependentvalue,operator:t.params.operator,pattern:t.params.pattern};n(t,"requiredif",i)});t.adapters.add("is",["dependentproperty","operator","passonnull"],function(t){n(t,"is",{dependentproperty:t.params.dependentproperty,operator:t.params.operator,passonnull:t.params.passonnull})});t.adapters.add("requiredifempty",["dependentproperty"],function(t){n(t,"requiredifempty",{dependentproperty:t.params.dependentproperty})});t.adapters.add("requiredifnotempty",["dependentproperty"],function(t){n(t,"requiredifnotempty",{dependentproperty:t.params.dependentproperty})})}();
//# sourceMappingURL=mvcfoolproof.unobtrusive.min.js.map
