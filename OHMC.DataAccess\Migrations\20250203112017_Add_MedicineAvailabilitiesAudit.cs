﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace OHMC.DataAccess.Migrations
{
    public partial class Add_MedicineAvailabilitiesAudit : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "MedicineAvailabilitiesAudit",
                columns: table => new
                {
                    MedicineAvailabilityAuditId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    MedicineAvailabilityId = table.Column<int>(type: "int", nullable: false),
                    ProductId = table.Column<int>(type: "int", nullable: false),
                    ReportDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    SecurityOfSupply = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    OutOfStockSourceId = table.Column<int>(type: "int", nullable: true),
                    SourceDetail = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    OutOfStockReasonId = table.Column<int>(type: "int", nullable: true),
                    IsVerified = table.Column<bool>(type: "bit", nullable: false),
                    DateOutOfStock = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DateAnticipatedAvailable = table.Column<DateTime>(type: "datetime2", nullable: true),
                    OutOfStockInternationalSignalId = table.Column<int>(type: "int", nullable: true),
                    AgencyId = table.Column<int>(type: "int", nullable: true),
                    DateVerified = table.Column<DateTime>(type: "datetime2", nullable: true),
                    RemedyId = table.Column<int>(type: "int", nullable: true),
                    DateBackInStock = table.Column<DateTime>(type: "datetime2", nullable: true),
                    Section21PermitNumber = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    SupplierId = table.Column<int>(type: "int", nullable: true),
                    PermitExpires = table.Column<DateTime>(type: "datetime2", nullable: true),
                    Price = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    Notes = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreateBy = table.Column<int>(type: "int", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedBy = table.Column<int>(type: "int", nullable: false),
                    Deleted = table.Column<bool>(type: "bit", nullable: false),
                    DeleteOn = table.Column<DateTime>(type: "datetime2", nullable: true),
                    ActionType = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    ActionDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    ActionBy = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MedicineAvailabilitiesAudit", x => x.MedicineAvailabilityAuditId);
                });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "MedicineAvailabilitiesAudit");
        }
    }
}
