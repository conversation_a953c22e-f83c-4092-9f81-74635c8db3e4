﻿using OHMC.Core.Models;

namespace OHMC.DataAccess.Interface
{
    public interface INotificationRepository
    {
        void Add(Notification notification);
        Task<Notification> GetNotificationAsync(int id);
        Task<IEnumerable<Notification>> GetNotificationsAsync();
        Task<IEnumerable<Notification>> GetNotificationsByTypeAsync(string notificationType);
        void Update(Notification notification);
    }
}