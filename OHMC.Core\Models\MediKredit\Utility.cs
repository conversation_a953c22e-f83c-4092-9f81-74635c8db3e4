﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace OHMC.Core.Models.MediKredit
{
    public static class Utility
    {
        public static int PropertyMaxLength(Type type, string myProperty)
        {
            int maxLength = type
                .GetProperty(myProperty)
                .GetCustomAttributes<StringLengthAttribute>()
                .FirstOrDefault()
                .MaximumLength;

            return maxLength;
        }

        public static ProductFileTrailer GetProductFileTrailer(string line)            
        {           
            return new ProductFileTrailer()
            {
                SequenceNumber = line.Substring(0, 7),
                RecordTypeIdentifier = line.Substring(7, 3),
                TotalNumberOfRecords = line.Substring(10, 9),
                NumberOfType201 = line.Substring(19, 6),
                NumberOfType202 = line.Substring(25, 6),
                NumberOfType203 = line.Substring(31, 6),
                NumberOfType204 = line.Substring(37, 6),
                NumberOfType205 = line.Substring(43, 6),
                NumberOfType206 = line.Substring(49, 6),
                NumberOfType209 = line.Substring(55, 9),
            };
        }

        public static ProductPrice GetProductPrice(string line) 
        {
            return new ProductPrice()
            {
                SequenceNumber =  line.Substring(0, 7),
                RecordTypeIdentifier = line.Substring(7, 3),
                NAPPIProductCode = line.Substring(10, 7),
                NAPPISuffix = line.Substring(17, 6),
                ProductPackSize = line.Substring(20, 9),
                WholeSalePrice = line.Substring(29, 9),
                RetailPrice = line.Substring(38, 8),
                EANProductCode = line.Substring(46, 14),
                PriceEffectiveDate = line.Substring(60, 8),
                PriceTerminationDate = line.Substring(68, 8),
                SchedulePrefix = line.Substring(76, 1),
                Schedule = line.Substring(77, 2),
                CountryCode = line.Substring(79, 2),
                SectorID = line.Substring(81, 2),
                Filler1 = line.Substring(83, 81),
                FullWholesalePrice = line.Substring(164, 9),
                FullRetailPrice = line.Substring(173, 9),
                FullProductName = line.Substring(182, 60),
                PriceUpdateDate = line.Substring(242, 8),
                PriceUpdateTime = line.Substring(250, 6)
            };
        }
    }
}
