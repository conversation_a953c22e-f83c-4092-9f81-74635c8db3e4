﻿using Microsoft.Extensions.Logging;
using OHMC.Core.Common.Const;
using OHMC.Core.Models;
using OHMC.DataAccess.Interface;

namespace OHMC.Application
{
    public class FormTypeBL : IFormTypeBL
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<FormTypeBL> _logger;

        public FormTypeBL(IUnitOfWork unitOfWork, ILogger<FormTypeBL> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<IEnumerable<FormType>> GetFormTypesAsync()
        {
            return await _unitOfWork.FormTypeRepository.GetFormTypesAsync();
        }

        public async Task<IEnumerable<FormType>> SearchFormTypeAsync(string desc)
        {
            return await _unitOfWork.FormTypeRepository.SearchFormTypeAsync(desc);
        }

        public async Task<FormType> GetFormTypeAsync(int id)
        {
            return await _unitOfWork.FormTypeRepository.GetFormTypeAsync(id);
        }

        public async Task<int> UpSert(FormType formType, int userId)
        {
            formType.UpdatedBy = userId;
            formType.UpdatedDate = DateTime.Now;

            if (formType.Id > 0)
            {
                _unitOfWork.FormTypeRepository.Update(formType);
                _logger.LogInformation($"{StrConst.LOG_TYPE_UPDATE}: {formType.FormTypeName}, USERID: {userId}");
            }
            else
            {
                formType.CreateBy = userId;
                formType.CreatedDate = DateTime.Now;

                _unitOfWork.FormTypeRepository.Add(formType);
                _logger.LogInformation($"{StrConst.LOG_TYPE_ADDING}: {formType.FormTypeName}, USERID: {userId}");
            }

            return await SaveAsync();
        }

        public async Task<int> RemoveFormType(int id, int userId)
        {
            var formType = await _unitOfWork.FormTypeRepository.GetFormTypeAsync(id);
            formType.UpdatedBy = userId;
            formType.UpdatedDate = DateTime.Now;
            formType.Deleted = true;
            formType.DeleteOn = DateTime.Now;
            _unitOfWork.FormTypeRepository.Update(formType);
            _logger.LogInformation($"{StrConst.LOG_TYPE_REMOVING}: {formType.FormTypeName}, USERID: {userId}");
            return await _unitOfWork.SaveAsync();
        }

        public async Task<int> SaveAsync()
        {
            return await _unitOfWork.SaveAsync();
        }
    }
}
