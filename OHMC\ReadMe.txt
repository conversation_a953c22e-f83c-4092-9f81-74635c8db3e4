﻿
Add-migration "InitialUserTables"
update-database

INSERT INTO [dbo].[Roles]
           ([Name]
           ,[CreatedDate]
           ,[CreateBy]
           ,[UpdatedDate]
           ,[UpdatedBy]
           ,[Deleted]
           ,[DeleteOn])
     VALUES
           ('Admin'
           ,GetDate()
           ,1
           ,GetDate()
           ,1
           ,0
           ,null)

INSERT INTO [dbo].[Roles]
           ([Name]
           ,[CreatedDate]
           ,[CreateBy]
           ,[UpdatedDate]
           ,[UpdatedBy]
           ,[Deleted]
           ,[DeleteOn])
     VALUES
           ('General'
           ,GetDate()
           ,1
           ,GetDate()
           ,1
           ,0
           ,null)

GO

RabbitMQ Setup
docker pull rabbitmq:3-management
docker run --rm -it -p 15672:15672 -p 5672:5672 rabbitmq:3-management
default user name and pwd guest guest

Microsoft SQL Server Docker Setup
docker pull mcr.microsoft.com/mssql/server:2022-latest
docker run -e 

DevExpress License
Add new devextreme-license.js file in resources like in wwwroot\js\ folder. 
Copy license key form devexpress website (download section) by logging <NAME_EMAIL>
Contents of file : DevExpress.config({ licenseKey: 'DEVELOPER LICENCE KEY' });