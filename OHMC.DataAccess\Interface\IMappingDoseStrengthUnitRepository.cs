﻿using OHMC.Core.Models;

namespace OHMC.DataAccess.Interface
{
    public interface IMappingDoseStrengthUnitRepository
    {
        void Add(MappingDoseStrengthUnit map);
        Task<MappingDoseStrengthUnit> GetMappingDoseStrengthUnitAsync(int id);
        Task<MappingDoseStrengthUnit> GetMappingDoseStrengthUnitByDoseAsync(string doseUnit);
        Task<IEnumerable<MappingDoseStrengthUnit>> GetMappingDoseStrengthUnitsAsync();
        void Update(MappingDoseStrengthUnit map);
    }
}