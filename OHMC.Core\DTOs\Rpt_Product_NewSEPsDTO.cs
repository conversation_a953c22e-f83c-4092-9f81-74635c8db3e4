﻿namespace OHMC.Core.DTOs
{
    public class Rpt_Product_NewSEPsDTO
    {
        public int Id { get; set; }
        public string Formulation { get; set; }
        public string Nappi9 { get; set; }
        public string ActiveIngredient { get; set; }
        public string ProductName { get; set; }
        public string StrengthDescription { get; set; }
        public decimal StandardPackSize { get; set; }
        public decimal OldPricePerPack { get; set; }
        public double NewPricePerPack { get; set; }
        public double Change { get; set; }
        public string? SEPStartDate { get; set; }
        public string? SEPEndDate { get; set; }
        public string? DateSpan { get; set; }
        public string? ManufacturerName { get; set; }
    }
}
