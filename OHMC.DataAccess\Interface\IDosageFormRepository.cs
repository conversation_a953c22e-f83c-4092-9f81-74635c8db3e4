﻿using OHMC.Core.Models;

namespace OHMC.DataAccess.Interface
{
    public interface IDosageFormRepository
    {
        void Add(DosageForm dosageForm);
        Task<IEnumerable<DosageForm>> GetDosageFormsAsync();
        Task<DosageForm> GetDosageFormAsync(int id);
        Task<IEnumerable<DosageForm>> SearchDosageFormsAsync(string desc);
        void Update(DosageForm dosageForm);
        Task<IEnumerable<DosageForm>> GetDosageFormByFormTypeAsync(string formType);
    }
}