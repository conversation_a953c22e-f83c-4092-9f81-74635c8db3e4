﻿using System.ComponentModel.DataAnnotations;

namespace OHMC.Core.Models
{
    public class MedicalSchemeBenefitPlanAudit : BaseAudit
    {
        [Key]
        public int MedicalSchemeBenefitPlansAuditId { get; set; }
        public int MedicalSchemeBenefitPlanId { get; set; }
        [MaxLength(200)]
        public string BenefitName { get; set; } = string.Empty;
        public int BenefitYear { get; set; }
        public int MedicalSchemeId { get; set; }
    }
}
