﻿using OHMC.Core.Models;

namespace OHMC.DataAccess.Interface
{ 
    public interface IINNBL
    {
        Task<INN> GetINN(int id);
        Task<IEnumerable<INN>> GetINNsAsync();
        Task<IEnumerable<INN>> GetPagedAsync(int pageNumber, int pageSize);
        Task<int> GetINNCountsAsync();
        Task<int> RemoveINN(int id, int userId);
        Task<int> SaveAsync();
        Task<IEnumerable<INN>> SearchAsync(string desc);
        Task<int> UpSert(INN inn, int userId);
        Task<IEnumerable<INN>> GetINNsWithProductsAsync();
        Task<IEnumerable<INN>> SearchINNAsync(string desc);
    }
}