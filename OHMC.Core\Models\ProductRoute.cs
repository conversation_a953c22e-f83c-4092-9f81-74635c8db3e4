﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace OHMC.Core.Models
{
    public class ProductRoute : ModelBase
    {
        [Key]
        public int Id { get; set; }
        [Required, MaxLength(10)]
        public string RouteName {  get; set; } = string.Empty;
        [Required]
        public int OrderNumber { get; set; } = 1;
        [ForeignKey("Product")]
        public int ProductId { get; set; }
        public Product Product { get; set; }
    }
}
