﻿using FluentAssertions;
using Microsoft.Extensions.Logging;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using OHMC.Application;
using OHMC.Core.Models;
using OHMC.DataAccess.Interface;

namespace OHMC.ApplicationUnitTests
{
    [TestClass]
    public class FormulationBLTests : BaseTest
    {
        private readonly Mock<IUnitOfWork> _unitOfWork = new();
        private readonly Mock<ILogger<FormulationBL>> _logger = new();
        private FormulationBL? _formulationBL;

        [TestInitialize]
        public void Initialize()
        {
            _formulationBL = new FormulationBL(_unitOfWork.Object, _logger.Object);
        }

        [TestMethod]
        public async Task GetFormulation_Should_Return_SingleFormulation()
        {
            //given 
            INN inn = new INN()
            {
                Id = 1,
                Code = "T001",
                INNDescription = "Tamoxifen",
                CreateBy = 1,
                CreatedDate = DateTime.Now,
                UpdatedBy = 1,
                UpdatedDate = DateTime.Now,
                Deleted = false
            };

            Formulation formulation = new Formulation()
            {
                INNId = 1,
                PrimaryRoute = "PO",
                DosageForm = "Solid",
                DosageFormType = "Tablet",
                CanBeAdministeredDivisibleUnits = false,
                DivisiblePerUnitVol = "",
                StrengthDose = 500,
                StrengthUnits = "mg",
                Strength = "500 mg",
                DosageFormDescriptionCalc = " 500 mg",
                IsTherapeuticClass = false,
                IsTender = "NO",                
                CreateBy = 1,
                CreatedDate = DateTime.Now,
                UpdatedBy = 1,
                UpdatedDate = DateTime.Now,
                Deleted = false
            };

            bool formulationUnitOfWorkRepositoryGetAsyncHssBeenCalled = false;
            _unitOfWork.Setup(x => x.FormulationRepository.GetFormulationAsync(It.IsAny<int>()))
                .Callback(() => formulationUnitOfWorkRepositoryGetAsyncHssBeenCalled = true)
                .Returns(Task.FromResult(formulation));

            //when 
            var result = await _formulationBL.GetFormulationAsync(1);

            //then
            Assert.IsNotNull(result);
            formulationUnitOfWorkRepositoryGetAsyncHssBeenCalled.Should().BeTrue();
        }
    }
}
