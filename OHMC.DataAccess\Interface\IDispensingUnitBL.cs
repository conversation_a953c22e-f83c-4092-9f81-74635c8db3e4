﻿using OHMC.Core.Models;

namespace OHMC.DataAccess.Interface
{
    public interface IDispensingUnitBL
    {
        Task<DispensingUnit> GetDispensingUnitAsync(int id);
        Task<IEnumerable<DispensingUnit>> GetDispensingUnitsAsync();
        Task<int> RemoveDispensingUnit(int id, int userId);
        Task<int> SaveAsync();
        Task<IEnumerable<DispensingUnit>> SearchAsync(string desc);
        Task<int> UpSert(DispensingUnit dispensingUnit, int userId);
    }
}