﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace OHMC.Core.Models
{
    public class ProtocolGuideline : ModelBase
    {
        [Key]
        public int Id { get; set; }
        [Required, ForeignKey("Protocol")]
        public int ProtocolId { get; set; }
        public Protocol Protocol { get; set; }
        [Required, ForeignKey("Guideline")]
        public int GuidelineId { get; set; }
        public Guideline Guideline { get; set; }
        public bool? IsModified { get; set; }
    }
}
