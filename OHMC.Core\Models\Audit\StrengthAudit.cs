﻿using System.ComponentModel.DataAnnotations;

namespace OHMC.Core.Models
{
    public class StrengthAudit : BaseAudit
    {
        [Key]
        public int StrengthAuditId { get; set; }
        public int StrengthId { get; set; }
        [MaxLength(20)]
        public string StrengthCode { get; set; } = string.Empty;
        public decimal StrengthValue { get; set; }
        [MaxLength(20)]
        public string StrengthUnit { get; set; } = string.Empty;
        [Required]
        public string StrengthDescription { get; set; } = string.Empty;
    }
}
