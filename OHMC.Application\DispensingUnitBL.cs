﻿using Microsoft.Extensions.Logging;
using OHMC.Core.Common.Const;
using OHMC.Core.Models;
using OHMC.DataAccess.Interface;

namespace OHMC.Application
{
    public class DispensingUnitBL : IDispensingUnitBL
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<DispensingUnitBL> _logger;

        public DispensingUnitBL(IUnitOfWork unitOfWork, ILogger<DispensingUnitBL> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<IEnumerable<DispensingUnit>> GetDispensingUnitsAsync()
        {
            return await _unitOfWork.DispensingUnitRepository.GetDispensingUnitsAsync();
        }

        public async Task<IEnumerable<DispensingUnit>> SearchAsync(string desc)
        {
            return await _unitOfWork.DispensingUnitRepository.SearchDispensingUnitAsync(desc);
        }

        public async Task<DispensingUnit> GetDispensingUnitAsync(int id)
        {
            return await _unitOfWork.DispensingUnitRepository.GetDispensingUnitAsync(id);
        }

        public async Task<int> UpSert(DispensingUnit dispensingUnit, int userId)
        {
            dispensingUnit.UpdatedBy = userId;
            dispensingUnit.UpdatedDate = DateTime.Now;

            if (dispensingUnit.Id > 0)
            {
                _unitOfWork.DispensingUnitRepository.Update(dispensingUnit);
                _logger.LogInformation($"{StrConst.LOG_TYPE_UPDATE}: {dispensingUnit.AdminUnit}, USERID: {userId}");
            }
            else
            {
                dispensingUnit.CreateBy = userId;
                dispensingUnit.CreatedDate = DateTime.Now;

                _unitOfWork.DispensingUnitRepository.Add(dispensingUnit);
                _logger.LogInformation($"{StrConst.LOG_TYPE_ADDING}: {dispensingUnit.AdminUnit}, USERID: {userId}");
            }

            return await SaveAsync();
        }

        public async Task<int> RemoveDispensingUnit(int id, int userId)
        {
            var strength = await _unitOfWork.DispensingUnitRepository.GetDispensingUnitAsync(id);
            strength.UpdatedBy = userId;
            strength.UpdatedDate = DateTime.Now;
            strength.Deleted = true;
            strength.DeleteOn = DateTime.Now;
            _unitOfWork.DispensingUnitRepository.Update(strength);
            _logger.LogInformation($"{StrConst.LOG_TYPE_REMOVING}  : : {strength.AdminUnit}, USERID: {userId}");
            return await _unitOfWork.SaveAsync();
        }

        public async Task<int> SaveAsync()
        {
            return await _unitOfWork.SaveAsync();
        }
    }
}
