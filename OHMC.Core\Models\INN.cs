﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace OHMC.Core.Models
{
    public class INN : ModelBase
    {
        [Key]
        public int Id { get; set; }
        [MaxLength(10), Display<PERSON><PERSON>("Code")]
        public string Code { get; set; } = string.Empty;
        [Required, MaxLength(500), Disp<PERSON><PERSON><PERSON>("INN")]
        public string INNDescription { get; set; } = string.Empty;
    }
}
