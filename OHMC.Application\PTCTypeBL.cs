﻿using Microsoft.Extensions.Logging;
using OHMC.Core.Common.Const;
using OHMC.Core.Models;
using OHMC.DataAccess.Interface;

namespace OHMC.Application
{
    public class PTCTypeBL : IPTCTypeBL
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<PTCTypeBL> _logger;
        public PTCTypeBL(IUnitOfWork unitOfWork, ILogger<PTCTypeBL> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<IEnumerable<PTCType>> GetPTCTypesAsync()
        {
            return await _unitOfWork.PTCTypeRepository.GetPTCTypesAsync();
        }

        public async Task<PTCType> GetPTCType(int id)
        {
            return await _unitOfWork.PTCTypeRepository.GetPTCTypeAsync(id);
        }

        public async Task<PTCType> GetPTCType(string ptcType)
        {
            return await _unitOfWork.PTCTypeRepository.GetPTCTypeAsync(ptcType);
        }

        public async Task<int> UpSert(PTCType ptc, int userId)
        {
            ptc.UpdatedBy = userId;
            ptc.UpdatedDate = DateTime.Now;

            if (ptc.Id > 0)
            {
                _unitOfWork.PTCTypeRepository.Update(ptc);
                _logger.LogInformation($"{StrConst.LOG_TYPE_UPDATE}: {ptc.PTCName}, USERID: {userId}");
            }
            else
            {
                ptc.CreateBy = userId;
                ptc.CreatedDate = DateTime.Now;

                _unitOfWork.PTCTypeRepository.Add(ptc);
                _logger.LogInformation($"{StrConst.LOG_TYPE_ADDING}: {ptc.PTCName}, USERID: {userId}");
            }

            return await SaveAsync();
        }

        public async Task<int> SaveAsync()
        {
            return await _unitOfWork.SaveAsync();
        }
    }
}
