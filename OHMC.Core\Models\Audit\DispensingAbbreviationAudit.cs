﻿
using System.ComponentModel.DataAnnotations;

namespace OHMC.Core.Models.Audit
{
    public class DispensingAbbreviationAudit : BaseAudit
    {
        [Key]
        public int DispensingAbbreviationAuditId { get; set; }
        public int DispensingAbbreviationId { get; set; }
        [MaxLength(100)]
        public string FullText { get; set; } = string.Empty;
        [MaxLength(20)]
        public string Abbreviation { get; set; } = string.Empty;
        public int Hourly { get; set; }
        public int DailyDoses { get; set; }
    }
}
