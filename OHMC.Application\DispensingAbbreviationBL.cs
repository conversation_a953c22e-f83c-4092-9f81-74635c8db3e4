﻿using Microsoft.Extensions.Logging;
using OHMC.Core.Common.Const;
using OHMC.Core.Models;
using OHMC.DataAccess.Interface;

namespace OHMC.Application
{
    public class DispensingAbbreviationBL : IDispensingAbbreviationBL
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<DispensingAbbreviationBL> _logger;

        public DispensingAbbreviationBL(IUnitOfWork unitOfWork, ILogger<DispensingAbbreviationBL> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<IEnumerable<DispensingAbbreviation>> GetDispensingAbbreviationsAsync()
        {
            return await _unitOfWork.DispensingAbbreviationRepository.GetDispensingAbbreviationsAsync();
        }

        public async Task<DispensingAbbreviation> GetDispensingAbbreviationAsync(int id)
        {
            return await _unitOfWork.DispensingAbbreviationRepository.GetDispensingAbbreviationAsync(id);
        }

        public async Task<DispensingAbbreviation> GetDispensingAbbreviationByAbbreviationAsync(string abbreviation)
        {
            return await _unitOfWork.DispensingAbbreviationRepository.GetDispensingAbbreviationByAbbreviationAsync(abbreviation);
        }

        public async Task<int> UpSert(DispensingAbbreviation abbreviation, int userId)
        {
            abbreviation.UpdatedBy = userId;
            abbreviation.UpdatedDate = DateTime.Now;

            if (abbreviation.Id > 0)
            {
                _unitOfWork.DispensingAbbreviationRepository.Update(abbreviation);
                _logger.LogInformation($"{StrConst.LOG_TYPE_UPDATE}: {abbreviation.FullText}, USERID: {userId}");
            }
            else
            {
                abbreviation.CreateBy = userId;
                abbreviation.CreatedDate = DateTime.Now;

                _unitOfWork.DispensingAbbreviationRepository.Add(abbreviation);
                _logger.LogInformation($"{StrConst.LOG_TYPE_ADDING}: {abbreviation.FullText}, USERID: {userId}");
            }

            return await SaveAsync();
        }

        public async Task<int> SaveAsync()
        {
            return await _unitOfWork.SaveAsync();
        }
    }
}
