﻿using OHMC.Core.Models;
using OHMC.DataAccess.Repository;

namespace OHMC.DataAccess.Interface
{
    public interface INetworkMemberBL
    {
        Task<NetworkMember> GetNetworkMemberAsync(int id);
        Task<IEnumerable<NetworkMember>> GetNetworkMembersAsync();
        Task<NetworkMemberSpeciality> GetNetworkMemberSpecialityAsync(int id);
        Task<IEnumerable<NetworkMemberSpeciality>> GetNetworkMemberSpecialitiesAsync(int networkMemberId);
        Task<string> GetSpecialitiesDescription(int networkMemberId);
        Task<int> SaveAsync();
        Task<int> UpSert(NetworkMember member, int userId);
        Task<int> UpSertSpeciality(NetworkMemberSpeciality speciality, int userId);
    }
}