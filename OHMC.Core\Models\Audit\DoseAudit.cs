﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace OHMC.Core.Models
{
    public class DoseAudit : BaseAudit
    {
        [Key]
        public int DoseAuditId { get; set; }
        public int DoseId { get; set; }
        [MaxLength(15)]
        public string DoseCodeRef { get; set; } = string.Empty;
        public int INNId { get; set; }
        [DisplayName("2nd Ingredient")]
        public int? INN2Id { get; set; }
        [MaxLength(10)]
        public string Route { get; set; } = string.Empty;
        public double DoseLow { get; set; }
        public double? DoseLow2 { get; set; }
        public double? DoseHigh { get; set; }
        [MaxLength(20)]
        public string DoseUnit { get; set; } = string.Empty;
        [MaxLength(20)]
        public string? DoseUnit2 { get; set; } = string.Empty;
        public bool IsInterval { get; set; } = false;
        [MaxLength(20)]
        public string? IntervalHourlyOrTimesADay { get; set; } = string.Empty;
        public int? HourlyValue { get; set; }
        public int? TimesADayValue { get; set; }
        public int? ConfirmDosesInADay { get; set; }
        [MaxLength(20)]
        public string? Abbreviation { get; set; }
        public string DoseDescriptionCalc { get; set; } = string.Empty;
        public bool IsDoseDescriptionCorrect { get; set; } = true;
        public string DoseDescriptionCalcEdited { get; set; } = string.Empty;
        public double? StdDoseCalc { get; set; }
        public double? DoseLowCalc { get; set; }
        public double? DoseHighCalc { get; set; }
        [MaxLength(20)]
        public string? RoundingMethod { get; set; }
        public int? CustomRoundingNumber { get; set; }
        public double? StdRoundedDose { get; set; }
        public double? DoseLowRounded { get; set; }
        public double? DoseHighRounded { get; set; }
    }
}
