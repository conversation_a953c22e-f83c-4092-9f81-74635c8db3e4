﻿using OHMC.Core.Common.Response;
using OHMC.Core.Models;

namespace OHMC.DataAccess.Interface
{
    public interface IDosingRegimenBL
    {
        Task<IEnumerable<DosingRegimen>> GetDosingRegimenAsync();
        Task<DosingRegimen> GetDosingRegimenAsync(int id);
        Task<int> GetDosingRegimenCountAsync();
        Task<IEnumerable<DosingRegimen>> GetPagedDosingRegimenAsync(int pageNumber, int pageSize);
        Task<int> SaveAsync();
        Task<IEnumerable<DosingRegimen>> SearchDosingRegimenAsync(string desc);

        Task<RegimenDosingRegimen> GetRegimenDosingRegimen(int id);
        Task<int> UpSertRegimenDosingRegimen(RegimenDosingRegimen regimenDosingRegimen, int userId);
        Task<int> UpSert(DosingRegimen dosingRegimen, int userId);
        Task<int> GetDosingRegimenProductCountAsync(int id);
        Task<CopyDoseRegimenResponse> CopyDoseRegimenAsync(int doseRegimenId, int userId);
    }
}