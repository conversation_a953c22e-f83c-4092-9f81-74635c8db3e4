﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace OHMC.Core.Models
{
    public class ProtocolStatus : ModelBase
    {
        [Key]
        public int Id { get; set; }
        [ForeignKey("Protocol")]
        public int ProtocolId { get; set; }
        public Protocol Protocol { get; set; }
        [MaxLength(100)]
        public string Status { get; set; } = string.Empty;
        public DateTime? IncludedDate { get; set; }
        public DateTime? PhasedOutDate { get; set; }
        public DateTime? EndingDate { get; set; }
        [MaxLength(200)]
        public string? Comment { get; set; }
    }
}
