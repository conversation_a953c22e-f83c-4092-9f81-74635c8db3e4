﻿using System.ComponentModel.DataAnnotations;

namespace OHMC.Core.Models.Audit
{
    public class OutcomeTypeAudit : BaseAudit
    {
        [Key]
        public int OutcomeTypeAuditId { get; set; }
        public int OutcomeTypeId { get; set; }
        [MaxLength(300)]
        public string OutcomeName { get; set; } = string.Empty;
        [MaxLength(300)]
        public string? OutcomeDescription { get; set; } = string.Empty;
    }
}
