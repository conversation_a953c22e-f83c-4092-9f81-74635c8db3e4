﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace OHMC.Core.Models
{
    public class CancerGroupAudit : BaseAudit
    {
        [Key]
        public int CancerGroupAuditId { get; set; }
        public int CancerGroupId { get; set; }
        [MaxLength(20)]
        public string GroupCode { get; set; } = string.Empty;
        [MaxLength(100)]
        public string GroupName { get; set; } = string.Empty;
        public int? PTCTypeId { get; set; }
        public int? PrintOrder { get; set; }
        public int FormularyTypeId { get; set; }
    }
}
