﻿using OHMC.Core.Models;
using OHMC.DataAccess.Repository;

namespace OHMC.DataAccess.Interface
{
    public interface IUnitOfWork
    {
        IINNRepository INNRepository { get; set; }
        ICancerGroupRepository CancerGroupRepository { get; set; }
        ICancerGroupTypeRepository CancerGroupTypeRepository { get; set; }
        IRouteRepository RouteRepository { get; set; }
        IEvidenceRepository EvidenceRepository { get; set; }
        IStrengthRepository StrengthRepository { get; set; }
        IDispensingUnitRepository DispensingUnitRepository { get; set; }
        IFixedSettingsRepository FixedSettingsRepository { get; set; }
        IDoseUnitRepository DoseUnitRepository { get; set; }
        IFormTypeRepository FormTypeRepository { get; set; }
        IDosageFormRepository DosageFormRepository { get; set; }
        IFormulationRepository FormulationRepository { get; set; }
        IFormulationRoutesRepository FormulationRoutesRepository { get; set; }        
        IProductRepository ProductRepository { get; set; }
        IProductRoutesRepository ProductRoutesRepository { get; set; }
        IDispensingAbbreviationRepository DispensingAbbreviationRepository { get; set; }
        IMedPraxProductRepository MedPraxProductRepository { get; set; }
        IDoseRepository DoseRepository { get; set; }
        IDoseProductRepository DoseProductRepository { get; set; }
        IDosingRegimenRepository DosingRegimenRepository { get; set; }
        IRegimenRepository RegimenRepository { get; set; }
        IRegimenDosingRegimenRepository RegimenDosingRegimenRepository { get; set; }
        IProtocolRepository ProtocolRepository { get; set; }
        IProtocolStatusRepository ProtocolStatusRepository { get; set; }
        IProtocolEvidenceRepository ProtocolEvidenceRepository { get; set; }
        IProtocolTreatmentIntentRepository ProtocolTreatmentIntentRepository { get; set; }
        IProtocolICD10Repository ProtocolICD10Repository { get; set; }
        IProtocolUserReviewRepository ProtocolUserReviewRepository { get; set; }
        IModalityRepository ModalityRepository { get; set; }
        IChapterRepository ChapterRepository { get; set; }
        IProductDocumentRepository ProductDocumentRepository { get; set; }
        INDODProductRepository NDODProductRepository { get; set; }
        ITherapeuticRepository TherapeuticRepository { get; set; }
        IMappingDoseStrengthUnitRepository MappingDoseStrengthUnitRepository { get; set; }
        IRegimenStatusRepository RegimenStatusRepository { get; set; }
        INotificationRepository NotificationRepository { get; set; }
        IFormularyTypeRepository FormularyTypeRepository { get; set; }
        IPTCTypeRepository PTCTypeRepository { get; set; }
        IPhaseTypeRepository PhaseTypeRepository { get; set; }
        IBlindingTypeRepository BlindingTypeRepository { get; set; }
        IEvidenceTypeRepository EvidenceTypeRepository { get; set; }
        IOutcomeTypeRepository OutcomeTypeRepository { get; set; }
        IMedicalSchemeRepository MedicalSchemeRepository { get; set; }
        IMedicalSchemeBenefitPlanRepository MedicalSchemeBenefitPlanRepository { get; set; }
        IFormularySchemeRuleRepository FormularySchemeRuleRepository { get; set; }
        IChapterForwardRepository ChapterForwardRepository { get; set; }
        ITariffRepository TariffRepository { get; set; }
        ITariffPeriodRepository TariffPeriodRepository { get; set; }
        ITariffTypeRepository TariffTypeRepository { get; set; }
        IRegimenCostRepository RegimenCostRepository { get; set; }
        IICD10Repository ICD10Repository { get; set; }
        IOutOfStockReasonRepository OutOfStockReasonRepository { get; set; }
        IOutOfStockSourceRepository OutOfStockSourceRepository { get; set; }
        IStockStatusRepository StockStatusRepository { get; set; }
        IMedicineAvailabilityRepository MedicineAvailabilityRepository { get; set; }
        IOutOfStockRemedyRepository OutOfStockRemedyRepository { get; set; }
        IOutOfStockInternationalSignalRepository OutOfStockInternationalSignalRepository { get; set; }
        ISupplierRepository SupplierRepository { get; set; }
        IAgencyRepository AgencyRepository { get; set; }
        INetworkMemberRepository NetworkMemberRepository { get; set; }
        INetworkMemberSpecialityRepository NetworkMemberSpecialityRepository { get; set; }
        INetworkPracticeRepository NetworkPracticeRepository { get; set; }
        INetworkPracticeSpecialityRepository NetworkPracticeSpecialityRepository { get; set; }
        IOrganisationRepository OrganisationRepository { get; set; }
        IGuidelineRepository GuidelineRepository { get; set; }
        IProtocolGuidelineRepository ProtocolGuidelineRepository { get; set; }
        IProtocolHeaderRepository ProtocolHeaderRepository { get; set; }
        ICancerGroupICD10Repository CancerGroupICD10Repository { get; set; }
        IQueryRepository QueryRepository { get; set; }
        Task<int> SaveAsync();
    }
}