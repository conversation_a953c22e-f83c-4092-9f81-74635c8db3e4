﻿using Microsoft.Extensions.Logging;
using OHMC.Core.Common.Const;
using OHMC.Core.Models;
using OHMC.DataAccess.Interface;
namespace OHMC.Application
{
    public class EvidenceBL : IEvidenceBL
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<EvidenceBL> _logger;

        public EvidenceBL(IUnitOfWork unitOfWork, ILogger<EvidenceBL> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<IEnumerable<Evidence>> GetAllEvidencesAsync()
        {
            return await _unitOfWork.EvidenceRepository.GetAllEvidencesAsync();
        }

        public async Task<IEnumerable<Evidence>> GetAllEvidencesPagedAsync(int pageNumber, int pageSize)
        {
            return await _unitOfWork.EvidenceRepository.GetAllEvidencesPagedAsync(pageNumber, pageSize);
        }

        public async Task<int> GetEvidenceCountAsync()
        {
            return await _unitOfWork.EvidenceRepository.GetEvidenceCountAsync();
        }

        public async Task<Evidence> GetEvidencesAsync(int id)
        {
            return await _unitOfWork.EvidenceRepository.GetEvidencesAsync(id);
        }

        public async Task<int> UpSert(Evidence evidences, int userId)
        {
            evidences.UpdatedBy = userId;
            evidences.UpdatedDate = DateTime.Now;

            if (evidences.Id > 0)
            {
                _unitOfWork.EvidenceRepository.Update(evidences);
                _logger.LogInformation($"{StrConst.LOG_TYPE_UPDATE}: {evidences.EvidenceDescription}, USERID: {userId}");
            }
            else
            {
                evidences.CreateBy = userId;
                evidences.CreatedDate = DateTime.Now;

                _unitOfWork.EvidenceRepository.Add(evidences);
                _logger.LogInformation($"{StrConst.LOG_TYPE_ADDING}: {evidences.EvidenceDescription}, USERID: {userId}");
            }

            return await SaveAsync();
        }

        public async Task<int> SaveAsync()
        {
            return await _unitOfWork.SaveAsync();
        }
    }
}
