﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace OHMC.Core.Models
{
    public class NetworkMemberSpeciality : ModelBase
    {
        public int Id { get; set; }
        [ForeignKey("NetworkMember")]
        public int NetworkMemberId { get; set; }
        public NetworkMember? NetworkMember { get; set; }
        [MaxLength(200)]
        public string Speciality { get; set; }
    }
}
