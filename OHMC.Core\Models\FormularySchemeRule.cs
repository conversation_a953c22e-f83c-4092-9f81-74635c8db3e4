﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace OHMC.Core.Models
{
    public class FormularySchemeRule : ModelBase
    {
        [Key]
        public int Id { get; set; }
        [MaxLength(30)]
        public string FormularyType { get; set; } = string.Empty;
        [MaxLength(100)]
        public string ModelType { get; set; } = string.Empty;
        public decimal ModelParameter { get; set; }

        [ForeignKey("MedicalScheme")]
        public int MedicalSchemeId { get; set; }
        public MedicalScheme MedicalScheme { get; set; }

        [ForeignKey("MedicalSchemeBenefitPlan")]
        public int MedicalSchemeBenefitPlanId { get; set; }
        public MedicalSchemeBenefitPlan MedicalSchemeBenefitPlan { get; set; }

        [MaxLength(20)]
        public string LevelOfCare { get; set; } = string.Empty;
        public bool IsActive { get; set; }
    }
}
