﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace OHMC.Core.Models
{
    public class Guideline : ModelBase
    {
        [Key]
        public int Id { get; set; }
        [MaxLength(200), Display<PERSON><PERSON>("Guideline Name")]
        public string GuidelineName { get; set; } = string.Empty;

        [ForeignKey("Organisation")]
        public int OrganisationId { get; set; }
        public Organisation? Organisation { get; set; }
        [MaxLength(250)]
        public string? GuidelineDisplayName { get; set; }
        [DisplayName("Year Reviewed")]
        public int YearLastReviewed { get; set; }
        [MaxLength(300)]
        public string? Comment { get; set; } = string.Empty;
        [MaxLength(200), DisplayName("Version")]
        public string? Version { get; set; }
        public string? ReferenceEvidence { get; set; }           
    }
}
