﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace OHMC.DataAccess.Migrations
{
    public partial class Products_AddTempSEPExclVatTempPricePerPack : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<decimal>(
                name: "TempPricePerDosingUnit",
                table: "ProductsAudit",
                type: "decimal(18,2)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "TempPricePerPack",
                table: "ProductsAudit",
                type: "decimal(18,2)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "TempPricePerUnit",
                table: "ProductsAudit",
                type: "decimal(18,2)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "TempSEPExclVat",
                table: "ProductsAudit",
                type: "decimal(18,2)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "TempPricePerDosingUnit",
                table: "Products",
                type: "decimal(18,2)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "TempPricePerPack",
                table: "Products",
                type: "decimal(18,2)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "TempPricePerUnit",
                table: "Products",
                type: "decimal(18,2)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "TempSEPExclVat",
                table: "Products",
                type: "decimal(18,2)",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "TempPricePerDosingUnit",
                table: "ProductsAudit");

            migrationBuilder.DropColumn(
                name: "TempPricePerPack",
                table: "ProductsAudit");

            migrationBuilder.DropColumn(
                name: "TempPricePerUnit",
                table: "ProductsAudit");

            migrationBuilder.DropColumn(
                name: "TempSEPExclVat",
                table: "ProductsAudit");

            migrationBuilder.DropColumn(
                name: "TempPricePerDosingUnit",
                table: "Products");

            migrationBuilder.DropColumn(
                name: "TempPricePerPack",
                table: "Products");

            migrationBuilder.DropColumn(
                name: "TempPricePerUnit",
                table: "Products");

            migrationBuilder.DropColumn(
                name: "TempSEPExclVat",
                table: "Products");
        }
    }
}
