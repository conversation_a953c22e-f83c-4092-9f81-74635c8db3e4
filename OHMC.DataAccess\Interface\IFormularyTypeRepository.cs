﻿using OHMC.Core.Models;

namespace OHMC.DataAccess.Interface
{
    public interface IFormularyTypeRepository
    {
        void Add(FormularyType formulary);
        Task<FormularyType> GetFormularyTypeAsync(int id);
        Task<FormularyType> GetFormularyTypeAsync(string formularyType);
        Task<IEnumerable<FormularyType>> GetFormularyTypesAsync();
        void Update(FormularyType formulary);
    }
}