﻿using System.ComponentModel.DataAnnotations;

namespace OHMC.Core.Models
{
    public class TariffPeriodAudit : BaseAudit
    {
        [Key]
        public int TariffPeriodAuditId { get; set; }
        public int TariffPeriodId { get; set;  }
        public int TariffId { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public bool IsActive { get; set; }
        public decimal TariffAmount { get; set; }
    }
}
