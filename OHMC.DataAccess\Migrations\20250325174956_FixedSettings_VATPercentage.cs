﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace OHMC.DataAccess.Migrations
{
    public partial class FixedSettings_VATPercentage : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<decimal>(
                name: "VATPercentage",
                table: "FixedSettingsAudit",
                type: "decimal(18,2)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "VATPercentage",
                table: "FixedSettings",
                type: "decimal(18,2)",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "VATPercentage",
                table: "FixedSettingsAudit");

            migrationBuilder.DropColumn(
                name: "VATPercentage",
                table: "FixedSettings");
        }
    }
}
