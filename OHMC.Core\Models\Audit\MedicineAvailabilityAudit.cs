﻿using System.ComponentModel.DataAnnotations;

namespace OHMC.Core.Models.Audit
{
    public class MedicineAvailabilityAudit : BaseAudit
    {
        [Key]
        public int MedicineAvailabilityAuditId { get; set; }
        public int MedicineAvailabilityId { get; set; }
        public int ProductId { get; set; }
        public DateTime ReportDate { get; set; }
        [MaxLength(50)]
        public string? SecurityOfSupply { get; set; }
        public int? OutOfStockSourceId { get; set; }
        [MaxLength(100)]
        public string? SourceDetail { get; set; }
        public int? OutOfStockReasonId { get; set; }
        public bool IsVerified { get; set; } = false;
        public DateTime DateOutOfStock { get; set; }
        public DateTime? DateAnticipatedAvailable { get; set; }
        public int? OutOfStockInternationalSignalId { get; set; }
        public int? AgencyId { get; set; }
        public DateTime? DateVerified { get; set; }
        public int? RemedyId { get; set; }
        public DateTime? DateBackInStock { get; set; }
        [MaxLength(100)]
        public string? Section21PermitNumber { get; set; } = string.Empty;
        public int? SupplierId { get; set; }
        public DateTime? PermitExpires { get; set; }
        public decimal? Price { get; set; }
        [MaxLength(200)]
        public string? Notes { get; set; }
        public DateTime? FollowUpDate { get; set; }
    }
}
