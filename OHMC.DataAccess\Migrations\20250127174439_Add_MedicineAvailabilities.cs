﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace OHMC.DataAccess.Migrations
{
    public partial class Add_MedicineAvailabilities : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "MedicineAvailabilities",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    ProductId = table.Column<int>(type: "int", nullable: false),
                    OutOfStockSourceId = table.Column<int>(type: "int", nullable: false),
                    OutOfStockReasonId = table.Column<int>(type: "int", nullable: false),
                    IsAvailable = table.Column<bool>(type: "bit", nullable: false),
                    IsVerified = table.Column<bool>(type: "bit", nullable: false),
                    DateOutOfStock = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DateAnticipatedAvailable = table.Column<DateTime>(type: "datetime2", nullable: true),
                    InternationalSignals = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Agency = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    DateAccessed = table.Column<DateTime>(type: "datetime2", nullable: true),
                    Remedy = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    DateBackInStock = table.Column<DateTime>(type: "datetime2", nullable: true),
                    Manufacturer = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Section21PermitNumber = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Supplier = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    DateExpired = table.Column<DateTime>(type: "datetime2", nullable: true),
                    Price = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreateBy = table.Column<int>(type: "int", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedBy = table.Column<int>(type: "int", nullable: false),
                    Deleted = table.Column<bool>(type: "bit", nullable: false),
                    DeleteOn = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MedicineAvailabilities", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MedicineAvailabilities_OutOfStockReasons_OutOfStockReasonId",
                        column: x => x.OutOfStockReasonId,
                        principalTable: "OutOfStockReasons",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_MedicineAvailabilities_OutOfStockSources_OutOfStockSourceId",
                        column: x => x.OutOfStockSourceId,
                        principalTable: "OutOfStockSources",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_MedicineAvailabilities_Products_ProductId",
                        column: x => x.ProductId,
                        principalTable: "Products",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_MedicineAvailabilities_OutOfStockReasonId",
                table: "MedicineAvailabilities",
                column: "OutOfStockReasonId");

            migrationBuilder.CreateIndex(
                name: "IX_MedicineAvailabilities_OutOfStockSourceId",
                table: "MedicineAvailabilities",
                column: "OutOfStockSourceId");

            migrationBuilder.CreateIndex(
                name: "IX_MedicineAvailabilities_ProductId",
                table: "MedicineAvailabilities",
                column: "ProductId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "MedicineAvailabilities");
        }
    }
}
