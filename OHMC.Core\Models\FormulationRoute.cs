﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace OHMC.Core.Models
{
    public class FormulationRoute : ModelBase
    {
        [Key]
        public int Id { get; set; }
        [Required, MaxLength(10)]
        public string RouteName {  get; set; } = string.Empty;
        [Required]
        public int OrderNumber { get; set; } = 1;
        [ForeignKey("Formualtion")]
        public int FormulationId { get; set; }
        public Formulation Formulation { get; set; }
    }
}
