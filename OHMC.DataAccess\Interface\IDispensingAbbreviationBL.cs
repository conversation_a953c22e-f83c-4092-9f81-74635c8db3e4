﻿using OHMC.Core.Models;

namespace OHMC.DataAccess.Interface
{
    public interface IDispensingAbbreviationBL
    {
        Task<DispensingAbbreviation> GetDispensingAbbreviationAsync(int id);
        Task<DispensingAbbreviation> GetDispensingAbbreviationByAbbreviationAsync(string abbreviation);
        Task<IEnumerable<DispensingAbbreviation>> GetDispensingAbbreviationsAsync();
        Task<int> SaveAsync();
        Task<int> UpSert(DispensingAbbreviation abbreviation, int userId);
    }
}