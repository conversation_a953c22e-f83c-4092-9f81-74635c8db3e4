﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace OHMC.Core.Models
{
    public class Route : ModelBase
    {
        [Key]
        public int Id { get; set; }
        [Required, <PERSON><PERSON><PERSON><PERSON>(10), Display<PERSON><PERSON>("Route Name")]
        public string RouteName { get; set; } = string.Empty;
        [Required, <PERSON><PERSON><PERSON><PERSON>(50), Display<PERSON><PERSON>("Description")]
        public string RouteDescription { get; set; } = string.Empty;
    }
}
