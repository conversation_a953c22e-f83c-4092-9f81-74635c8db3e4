﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace OHMC.Core.Models
{
    public class RegimenStatus : ModelBase
    {
        [Key]
        public int Id { get; set; }
        [ForeignKey("Regimen")]
        public int RegimenId { get; set; }
        public Regimen Regimen { get; set; }
        [MaxLength(100)]
        public string Status { get; set; } = string.Empty;
        [MaxLength(200)]
        public string? Comment { get; set; }
    }
}
