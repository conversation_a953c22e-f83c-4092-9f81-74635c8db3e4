﻿using Microsoft.Extensions.Logging;
using OHMC.Core.Common.Const;
using OHMC.Core.Models;
using OHMC.DataAccess.Interface;

namespace OHMC.Application
{
    public class AgencyBL : IAgencyBL
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<AgencyBL> _logger;

        public AgencyBL(IUnitOfWork unitOfWork, ILogger<AgencyBL> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<IEnumerable<Agency>> GetAgenciesAsync()
        {
            return await _unitOfWork.AgencyRepository.GetAgenciesAsync();
        }

        public async Task<Agency> GetAgency(int id)
        {
            return await _unitOfWork.AgencyRepository.GetAgencyAsync(id);
        }

        public async Task<int> UpSert(Agency agency, int userId)
        {
            agency.UpdatedBy = userId;
            agency.UpdatedDate = DateTime.Now;

            if (agency.Id > 0)
            {
                _unitOfWork.AgencyRepository.Update(agency);
                _logger.LogInformation($"{StrConst.LOG_TYPE_UPDATE}: {agency.AgencyName}, USERID: {userId}");
            }
            else
            {
                agency.CreateBy = userId;
                agency.CreatedDate = DateTime.Now;

                _unitOfWork.AgencyRepository.Add(agency);
                _logger.LogInformation($"{StrConst.LOG_TYPE_ADDING}: {agency.AgencyName}, USERID: {userId}");
            }

            return await SaveAsync();
        }

        public async Task<int> SaveAsync()
        {
            return await _unitOfWork.SaveAsync();
        }
    }
}
