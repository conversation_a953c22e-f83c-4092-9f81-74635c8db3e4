﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace OHMC.DataAccess.Migrations
{
    public partial class MedicineAvailabilities_MaxLengths : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_MedicineAvailabilities_OutOfStockReasons_OutOfStockReasonId",
                table: "MedicineAvailabilities");

            migrationBuilder.DropForeignKey(
                name: "FK_MedicineAvailabilities_OutOfStockSources_OutOfStockSourceId",
                table: "MedicineAvailabilities");

            migrationBuilder.AlterColumn<string>(
                name: "Supplier",
                table: "MedicineAvailabilities",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)");

            migrationBuilder.AlterColumn<string>(
                name: "Section21PermitNumber",
                table: "MedicineAvailabilities",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)");

            migrationBuilder.AlterColumn<string>(
                name: "<PERSON><PERSON><PERSON>",
                table: "MedicineAvailabilities",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)");

            migrationBuilder.AlterColumn<int>(
                name: "OutOfStockSourceId",
                table: "MedicineAvailabilities",
                type: "int",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AlterColumn<int>(
                name: "OutOfStockReasonId",
                table: "MedicineAvailabilities",
                type: "int",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AlterColumn<string>(
                name: "Manufacturer",
                table: "MedicineAvailabilities",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)");

            migrationBuilder.AlterColumn<string>(
                name: "InternationalSignals",
                table: "MedicineAvailabilities",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)");

            migrationBuilder.AlterColumn<string>(
                name: "Agency",
                table: "MedicineAvailabilities",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)");

            migrationBuilder.AddForeignKey(
                name: "FK_MedicineAvailabilities_OutOfStockReasons_OutOfStockReasonId",
                table: "MedicineAvailabilities",
                column: "OutOfStockReasonId",
                principalTable: "OutOfStockReasons",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_MedicineAvailabilities_OutOfStockSources_OutOfStockSourceId",
                table: "MedicineAvailabilities",
                column: "OutOfStockSourceId",
                principalTable: "OutOfStockSources",
                principalColumn: "Id");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_MedicineAvailabilities_OutOfStockReasons_OutOfStockReasonId",
                table: "MedicineAvailabilities");

            migrationBuilder.DropForeignKey(
                name: "FK_MedicineAvailabilities_OutOfStockSources_OutOfStockSourceId",
                table: "MedicineAvailabilities");

            migrationBuilder.AlterColumn<string>(
                name: "Supplier",
                table: "MedicineAvailabilities",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(100)",
                oldMaxLength: 100,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "Section21PermitNumber",
                table: "MedicineAvailabilities",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(100)",
                oldMaxLength: 100,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "Remedy",
                table: "MedicineAvailabilities",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(200)",
                oldMaxLength: 200,
                oldNullable: true);

            migrationBuilder.AlterColumn<int>(
                name: "OutOfStockSourceId",
                table: "MedicineAvailabilities",
                type: "int",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true);

            migrationBuilder.AlterColumn<int>(
                name: "OutOfStockReasonId",
                table: "MedicineAvailabilities",
                type: "int",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "Manufacturer",
                table: "MedicineAvailabilities",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(100)",
                oldMaxLength: 100,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "InternationalSignals",
                table: "MedicineAvailabilities",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(200)",
                oldMaxLength: 200,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "Agency",
                table: "MedicineAvailabilities",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(200)",
                oldMaxLength: 200,
                oldNullable: true);

            migrationBuilder.AddForeignKey(
                name: "FK_MedicineAvailabilities_OutOfStockReasons_OutOfStockReasonId",
                table: "MedicineAvailabilities",
                column: "OutOfStockReasonId",
                principalTable: "OutOfStockReasons",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_MedicineAvailabilities_OutOfStockSources_OutOfStockSourceId",
                table: "MedicineAvailabilities",
                column: "OutOfStockSourceId",
                principalTable: "OutOfStockSources",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
