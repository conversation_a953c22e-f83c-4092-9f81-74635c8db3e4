﻿using System.ComponentModel.DataAnnotations;

namespace OHMC.Core.Models.Audit
{
    public class OrganisationAudit : BaseAudit
    {
        [Key]
        public int OrganisationAuditId { get; set; }
        public int OrganisationId { get; set; }
        [MaxLength(20)]
        public string Abbreviation { get; set; } = string.Empty;
        [MaxLength(100)]
        public string Description { get; set; } = string.Empty;
        [MaxLength(100)]
        public string WebAddress { get; set; } = string.Empty;
        public bool Local { get; set; }
    }
}
