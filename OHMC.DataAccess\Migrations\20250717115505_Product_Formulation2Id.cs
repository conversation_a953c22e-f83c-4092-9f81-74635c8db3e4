﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace OHMC.DataAccess.Migrations
{
    public partial class Product_Formulation2Id : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "Formulation2Id",
                table: "ProductsAudit",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "Formulation2Id",
                table: "Products",
                type: "int",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Formulation2Id",
                table: "ProductsAudit");

            migrationBuilder.DropColumn(
                name: "Formulation2Id",
                table: "Products");
        }
    }
}
