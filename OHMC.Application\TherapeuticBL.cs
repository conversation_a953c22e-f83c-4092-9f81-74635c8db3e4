﻿using Microsoft.Extensions.Logging;
using OHMC.Core.Common.Const;
using OHMC.Core.Models;
using OHMC.DataAccess.Interface;

namespace OHMC.Application
{
    public class TherapeuticBL : ITherapeuticBL
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<TherapeuticBL> _logger;

        public TherapeuticBL(IUnitOfWork unitOfWork, ILogger<TherapeuticBL> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<IEnumerable<Therapeutic>> GetTherapeuticsAsync()
        {
            return await _unitOfWork.TherapeuticRepository.GetTherapeuticsAsync();
        }

        public async Task<Therapeutic> GetTherapeuticAsync(int id)
        {
            return await _unitOfWork.TherapeuticRepository.GetTherapeuticAsync(id);
        }

        public async Task<int> UpSert(Therapeutic therapeutic, int userId)
        {
            therapeutic.UpdatedBy = userId;
            therapeutic.UpdatedDate = DateTime.Now;

            if (therapeutic.Id > 0)
            {
                _unitOfWork.TherapeuticRepository.Update(therapeutic);
                _logger.LogInformation($"{StrConst.LOG_TYPE_UPDATE}: {therapeutic.TherapeuticName}, USERID: {userId}");
            }
            else
            {
                therapeutic.CreateBy = userId;
                therapeutic.CreatedDate = DateTime.Now;

                _unitOfWork.TherapeuticRepository.Add(therapeutic);
                _logger.LogInformation($"{StrConst.LOG_TYPE_ADDING}: {therapeutic.TherapeuticName}, USERID: {userId}");
            }

            return await SaveAsync();
        }

        public async Task<int> RemoveTherapeuticAsync(int id, int userId)
        {
            var option = await _unitOfWork.TherapeuticRepository.GetTherapeuticAsync(id);
            option.UpdatedBy = userId;
            option.UpdatedDate = DateTime.Now;
            option.Deleted = true;
            option.DeleteOn = DateTime.Now;
            _unitOfWork.TherapeuticRepository.Update(option);
            _logger.LogInformation($"{StrConst.LOG_TYPE_REMOVING}: {option.TherapeuticName}, USERID: {userId}");
            return await _unitOfWork.SaveAsync();
        }

        public async Task<int> SaveAsync()
        {
            return await _unitOfWork.SaveAsync();
        }
    }
}
