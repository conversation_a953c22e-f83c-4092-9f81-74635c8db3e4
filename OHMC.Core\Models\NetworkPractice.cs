﻿using System.ComponentModel.DataAnnotations;

namespace OHMC.Core.Models
{
    public class NetworkPractice : ModelBase
    {
        [Key]
        public int Id { get; set; }
        [MaxLength(200)]
        public string PracticeName { get; set; } = string.Empty;
        [MaxLength(20)]
        public string? PracticeNo { get; set; }
        [MaxLength(200)]
        public string? Email { get; set; }
        [MaxLength(20)]
        public string? Telephone1 { get; set; }
        [MaxLength(20)]
        public string? Telephone2 { get; set; }
        [MaxLength(500)]
        public string? Address { get; set; }
        [MaxLength(200)]
        public string? Town { get; set; }
        [MaxLength(20)]
        public string? Province { get; set; }
    }
}
