﻿using Microsoft.Extensions.Logging;
using OHMC.Core.Common.Const;
using OHMC.Core.Models;
using OHMC.DataAccess.Interface;

namespace OHMC.Application
{
    public class OutOfStockSourcesBL : IOutOfStockSourcesBL
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<OutOfStockSourcesBL> _logger;

        public OutOfStockSourcesBL(IUnitOfWork unitOfWork, ILogger<OutOfStockSourcesBL> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<IEnumerable<OutOfStockSource>> GetOutOfStockSourcesAsync()
        {
            return await _unitOfWork.OutOfStockSourceRepository.GetOutOfStockSourcesAsync();
        }

        public async Task<OutOfStockSource> GetOutOfStockSourceAsync(int id)
        {
            return await _unitOfWork.OutOfStockSourceRepository.GetOutOfStockSourceAsync(id);
        }

        public async Task<int> UpSert(OutOfStockSource source, int userId)
        {
            source.UpdatedBy = userId;
            source.UpdatedDate = DateTime.Now;

            if (source.Id > 0)
            {
                _unitOfWork.OutOfStockSourceRepository.Update(source);
                _logger.LogInformation($"{StrConst.LOG_TYPE_UPDATE}: {source.Description}, USERID: {userId}");
            }
            else
            {
                source.CreateBy = userId;
                source.CreatedDate = DateTime.Now;

                _unitOfWork.OutOfStockSourceRepository.Add(source);
                _logger.LogInformation($"{StrConst.LOG_TYPE_ADDING}: {source.Description}, USERID: {userId}");
            }

            return await SaveAsync();
        }

        public async Task<int> SaveAsync()
        {
            return await _unitOfWork.SaveAsync();
        }
    }
}
