﻿using FoolProof.Core;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace OHMC.Core.Models
{
    public class Formulation : ModelBase
    {
        [Key]
        public int Id { get; set; }
        [Required, Max<PERSON>ength(10), Display<PERSON><PERSON>("Primary Route")]
        public string PrimaryRoute { get; set; } = string.Empty;

        [Required, ForeignKey("INN"), DisplayName("INN")]
        public int INNId { get; set; }
        public INN INN { get; set; }

        [ForeignKey("DoseUnit"), DisplayName("Dose Unit")]
        public int DoseUnitId { get; set; }
        public DoseUnit DoseUnit { get; set; }

        [Required, MaxLength(30)]
        public string DosageFormType { get; set; } = string.Empty;
        [Required, MaxLength(50)]
        public string DosageForm { get; set; } = string.Empty;
        public bool CanBeAdministeredDivisibleUnits { get; set; } = false;

        [MaxLength(20)]
        public string DivisiblePerUnitVol {  get; set; } = string.Empty;
        [Range(double.Epsilon, double.MaxValue, ErrorMessage = "Enter a valid strength")]
        public decimal StrengthDose { get; set; }
        [MaxLength(20), DisplayName("Strength Unit")]
        public string StrengthUnits { get; set; } = string.Empty;
        public decimal? StrengthVolume { get; set; }
        [MaxLength(20)]
        public string? StrengthVolumeUnits { get; set; }
        [Required, MaxLength(500)]
        public string Strength { get; set; } = string.Empty;
        [Required]
        public string DosageFormDescriptionCalc { get; set; } = string.Empty;
        public bool IsTherapeuticClass { get; set; }
        [MaxLength(100)]
        [RequiredIf("IsTherapeuticClass", Operator.EqualTo, true, ErrorMessage = "Therapeutic Class text required.")]
        public string? TherapeuticClass { get; set; }
        public int? TherapeuticId { get; set; }
        [MaxLength(10)]
        public string? IsTender { get; set; }
        [MaxLength(20), DisplayName("Tender Number")]
        [RequiredIf("IsTender", Operator.EqualTo, "YES", ErrorMessage = "Tender number required.")]
        public string? TenderNumber { get; set; }
        [DisplayName("Tender Start Date")]
        public DateTime? TenderStartDate { get; set; }
        [DisplayName("Tender End Date")]
        public DateTime? TenderEndDate { get; set; }
        public bool IsTenderAwarded { get; set; }
        public decimal? TenderPrice { get; set; }
        [MaxLength(20), DisplayName("National Stock Number")]
        public string? NSN { get; set; }
        public decimal? TotalVolume { get; set; }
        [MaxLength(20)]
        public string? IndexNappi { get; set; } = string.Empty;
        public bool IsFixedDoseCombination { get; set; }
        public bool IsCombinationPack { get; set; }
        public List<FormulationRoute>? FormulationRoutes { get; set; }
    }
}
