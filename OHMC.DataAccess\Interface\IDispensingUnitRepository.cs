﻿using OHMC.Core.Models;

namespace OHMC.DataAccess.Interface
{
    public interface IDispensingUnitRepository
    {
        void Add(DispensingUnit dispenginUnit);
        Task<DispensingUnit> GetDispensingUnitAsync(int id);
        Task<IEnumerable<DispensingUnit>> GetDispensingUnitsAsync();
        Task<IEnumerable<DispensingUnit>> SearchDispensingUnitAsync(string desc);
        void Update(DispensingUnit dispensingUnit);
    }
}