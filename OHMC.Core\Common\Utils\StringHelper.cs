﻿using OHMC.Core.Common.Const;

namespace OHMC.Core.Common.Utils
{
    public static class StringHelper
    {
        public static string AddDefaultPrefix(string prefix, string suffix)
        {
            return prefix + suffix;
        }

        public static string AddDefaultPrefix(string prefix, string suffix, int repeatPrefix)
        {
            while((prefix.Length + suffix.Length) < repeatPrefix)
            {
                prefix = prefix + StrConst.REFERENCE_FILLER;
            }

            return prefix + suffix;
        }

        public static string RepeatFiller(string filler, int repeatFiller) 
        {
            while ((filler.Length) < repeatFiller)
            {
                filler += filler;
            }

            return filler;
        }
    }
}
