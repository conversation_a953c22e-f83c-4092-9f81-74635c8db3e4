﻿using Microsoft.Extensions.Logging;
using OHMC.Core.Common.Const;
using OHMC.Core.Models;
using OHMC.DataAccess.Interface;

namespace OHMC.Application
{
    public class MedicalSchemeBenefitPlanBL : IMedicalSchemeBenefitPlanBL
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<MedicalSchemeBenefitPlanBL> _logger;

        public MedicalSchemeBenefitPlanBL(IUnitOfWork unitOfWork, ILogger<MedicalSchemeBenefitPlanBL> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<IEnumerable<MedicalSchemeBenefitPlan>> GetMedicalSchemeBenefitPlansAsync()
        {
            return await _unitOfWork.MedicalSchemeBenefitPlanRepository.GetMedicalSchemeBenefitPlansAsync();
        }

        public async Task<IEnumerable<MedicalSchemeBenefitPlan>> GetMedicalSchemeBenefitPlansAsync(int medicalSchemeId)
        {
            return await _unitOfWork.MedicalSchemeBenefitPlanRepository.GetMedicalSchemeBenefitPlansAsync(medicalSchemeId);
        }

        public async Task<MedicalSchemeBenefitPlan> GetMedicalSchemeBenefitPlanAsync(int id)
        {
            return await _unitOfWork.MedicalSchemeBenefitPlanRepository.GetMedicalSchemeBenefitPlanAsync(id);
        }

        public async Task<int> UpSert(MedicalSchemeBenefitPlan plan, int userId)
        {
            plan.UpdatedBy = userId;
            plan.UpdatedDate = DateTime.Now;

            if (plan.Id > 0)
            {
                _unitOfWork.MedicalSchemeBenefitPlanRepository.Update(plan);
                _logger.LogInformation($"{StrConst.LOG_TYPE_UPDATE}: {plan.BenefitName}, USERID: {userId}");
            }
            else
            {
                plan.CreateBy = userId;
                plan.CreatedDate = DateTime.Now;

                _unitOfWork.MedicalSchemeBenefitPlanRepository.Add(plan);
                _logger.LogInformation($"{StrConst.LOG_TYPE_ADDING}: {plan.BenefitName}, USERID: {userId}");
            }

            return await SaveAsync();
        }

        public async Task<int> SaveAsync()
        {
            return await _unitOfWork.SaveAsync();
        }
    }
}
