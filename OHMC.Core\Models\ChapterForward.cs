﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations.Schema;

namespace OHMC.Core.Models
{
    public class ChapterForward : ModelBase
    {
        [Key]
        public int Id { get; set; }
        [Required, <PERSON><PERSON>ength(500), Display<PERSON>ame("Forward Text")]
        public string ChapterForwardText { get; set; } = string.Empty;
        [ForeignKey("Chapter")]
        public int ChapterId { get; set; }
        public Chapter Chapter { get; set; }
    }
}
