﻿using OHMC.Core.Models;

namespace OHMC.DataAccess.Interface
{
    public interface IRegimenDosingRegimenRepository
    {
        void Add(RegimenDosingRegimen regimenDosingRegimen);
        Task<string> GetAllRegimeDosingRegimenUsedDescription(int regimenId);
        Task<IEnumerable<RegimenDosingRegimen>> GetDosingRegimenAsync();
        Task<IEnumerable<RegimenDosingRegimen>> GetPagedDosingRegimensAsync(int pageNumber, int pageSize);
        Task<RegimenDosingRegimen> GetRegimenDosingRegimenAsync(int id);
        Task<int> GetRegimenDosingRegimenCountAsync(int regimenId);
        Task<IEnumerable<RegimenDosingRegimen>> GetRegimenDosingRegimenByRegimenAsync(int regimenId);
        void RemoveRegimenDosingRegimenAsync(RegimenDosingRegimen regimenDosingRegimen);
        void Update(RegimenDosingRegimen regimenDosingRegimen);
        Task<IEnumerable<RegimenDosingRegimen>> GetRegimenDosingRegimenByDosingRegimenAsync(int dosingRegimenId);
    }
}