﻿using OHMC.Core.Models;

namespace OHMC.DataAccess.Interface
{
    public interface IMedicineAvailabilityRepository
    {
        void Add(MedicineAvailability availability);
        Task<IEnumerable<MedicineAvailability>> GetMedicineAvailabilitiesAsync(int productId);
        Task<MedicineAvailability> GetMedicineAvailabilityAsync(int id);
        Task<MedicineAvailability> GetLatestMedicineAvailabilityAsync(int productId);
        void Update(MedicineAvailability availability);
    }
}