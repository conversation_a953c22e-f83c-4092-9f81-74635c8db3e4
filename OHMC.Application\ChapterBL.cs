﻿using Microsoft.Extensions.Logging;
using OHMC.Core.Common.Const;
using OHMC.Core.Models;
using OHMC.DataAccess.Interface;

namespace OHMC.Application
{
    public class ChapterBL : IChapterBL
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<ChapterBL> _logger;

        public ChapterBL(IUnitOfWork unitOfWork, ILogger<ChapterBL> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<IEnumerable<Chapter>> GetChapters()
        {
            return await _unitOfWork.ChapterRepository.GetChaptersAsync();
        }

        public async Task<IEnumerable<Chapter>> GetChaptersByPTCType(int ptcTypeId)
        {
            return await _unitOfWork.ChapterRepository.GetChaptersByPTCType(ptcTypeId);
        }

        public async Task<IEnumerable<Chapter>> GetChaptersByFormularyType(int formularyTypeId)
        {
            return await _unitOfWork.ChapterRepository.GetChaptersByFormularyType(formularyTypeId);
        }

        public async Task<Chapter> GetChapterAsync(int id)
        {
            return await _unitOfWork.ChapterRepository.GetChapterAsync(id);
        }

        public async Task<int> UpSert(Chapter chapter, int userId)
        {
            chapter.UpdatedBy = userId;
            chapter.UpdatedDate = DateTime.Now;

            if (chapter.Id > 0)
            {
                _unitOfWork.ChapterRepository.Update(chapter);
                _logger.LogInformation($"{StrConst.LOG_TYPE_UPDATE}: {chapter.ChapterDescription}, USERID: {userId}");
            }
            else
            {
                chapter.CreateBy = userId;
                chapter.CreatedDate = DateTime.Now;

                _unitOfWork.ChapterRepository.Add(chapter);
                _logger.LogInformation($"{StrConst.LOG_TYPE_ADDING}: {chapter.ChapterDescription}, USERID: {userId}");
            }

            return await SaveAsync();
        }

        public async Task<int> RemoveChapter(int id, int userId)
        {
            var chapter = await _unitOfWork.ChapterRepository.GetChapterAsync(id);
            chapter.UpdatedBy = userId;
            chapter.UpdatedDate = DateTime.Now;
            chapter.Deleted = true;
            chapter.DeleteOn = DateTime.Now;
            _unitOfWork.ChapterRepository.Update(chapter);
            _logger.LogInformation($"{StrConst.LOG_TYPE_REMOVING}: {chapter.ChapterDescription}, USERID: {userId}");
            return await _unitOfWork.SaveAsync();
        }

        public async Task<int> SaveAsync()
        {
            return await _unitOfWork.SaveAsync();
        }
    }
}
