﻿namespace OHMC.Core.DTOs
{
    public class Rpt_ProductOutOfStockDTO
    {
        public int Id { get; set; }
        public string ActiveIngredient { get; set; }
        public string ProductName { get; set; }
        public string? Reason { get; set; }
        public string? Source { get; set; }
        public string? Remedy { get; set; }
        public DateTime? DateOutOfStock { get; set; }
        public DateTime? ReportDate { get; set; }
        public DateTime? DateAnticipatedAvailable { get; set; }
        public DateTime? DateBackInStock { get; set; }
        public string? SecurityOfSupply { get; set; }
        public DateTime? FollowUpDate { get; set; }
        public string? Notes { get; set; }
    }
}
