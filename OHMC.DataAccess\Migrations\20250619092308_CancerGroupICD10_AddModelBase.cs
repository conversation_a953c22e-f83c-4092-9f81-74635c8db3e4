﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace OHMC.DataAccess.Migrations
{
    public partial class CancerGroupICD10_AddModelBase : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "CreateBy",
                table: "CancerGroupICD10s",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<DateTime>(
                name: "CreatedDate",
                table: "CancerGroupICD10s",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<DateTime>(
                name: "DeleteOn",
                table: "CancerGroupICD10s",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "Deleted",
                table: "CancerGroupICD10s",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<int>(
                name: "UpdatedBy",
                table: "CancerGroupICD10s",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<DateTime>(
                name: "UpdatedDate",
                table: "CancerGroupICD10s",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CreateBy",
                table: "CancerGroupICD10s");

            migrationBuilder.DropColumn(
                name: "CreatedDate",
                table: "CancerGroupICD10s");

            migrationBuilder.DropColumn(
                name: "DeleteOn",
                table: "CancerGroupICD10s");

            migrationBuilder.DropColumn(
                name: "Deleted",
                table: "CancerGroupICD10s");

            migrationBuilder.DropColumn(
                name: "UpdatedBy",
                table: "CancerGroupICD10s");

            migrationBuilder.DropColumn(
                name: "UpdatedDate",
                table: "CancerGroupICD10s");
        }
    }
}
