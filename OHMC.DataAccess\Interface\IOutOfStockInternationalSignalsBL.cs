﻿using OHMC.Core.Models;

namespace OHMC.DataAccess.Interface
{
    public interface IOutOfStockInternationalSignalsBL
    {
        Task<OutOfStockInternationalSignal> GetOutOfStockInternationalSignal(int id);
        Task<IEnumerable<OutOfStockInternationalSignal>> GetOutOfStockInternationalSignalsAsync();
        Task<int> SaveAsync();
        Task<int> UpSert(OutOfStockInternationalSignal signal, int userId);
    }
}