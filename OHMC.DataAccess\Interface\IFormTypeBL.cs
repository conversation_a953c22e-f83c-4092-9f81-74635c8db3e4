﻿using OHMC.Core.Models;

namespace OHMC.DataAccess.Interface
{
    public interface IFormTypeBL
    {
        Task<FormType> GetFormTypeAsync(int id);
        Task<IEnumerable<FormType>> GetFormTypesAsync();
        Task<int> RemoveFormType(int id, int userId);
        Task<int> SaveAsync();
        Task<IEnumerable<FormType>> SearchFormTypeAsync(string desc);
        Task<int> UpSert(FormType formType, int userId);
    }
}