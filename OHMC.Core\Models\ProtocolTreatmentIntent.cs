﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace OHMC.Core.Models
{
    public class ProtocolTreatmentIntent : ModelBase
    {
        [Key]
        public int Id { get; set; }
        [ForeignKey("Protocol")]
        public int ProtocolId { get; set; }
        public Protocol Protocol { get; set; }
        public int OrderNumber { get; set; }
        [Required, <PERSON><PERSON><PERSON><PERSON>(50), Display<PERSON><PERSON>("Treatment Intent")]
        public string TreatmentIntent { get; set; } = string.Empty;
        [MaxLength(50), Display<PERSON><PERSON>("Treatment Detail")]
        public string? TreatmentIntentDetail { get; set; } = string.Empty;
    }
}
