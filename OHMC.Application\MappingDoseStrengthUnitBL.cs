﻿using Microsoft.Extensions.Logging;
using OHMC.Core.Common.Const;
using OHMC.Core.Models;
using OHMC.DataAccess.Interface;

namespace OHMC.Application
{
    public class MappingDoseStrengthUnitBL : IMappingDoseStrengthUnitBL
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<MappingDoseStrengthUnitBL> _logger;

        public MappingDoseStrengthUnitBL(IUnitOfWork unitOfWork, ILogger<MappingDoseStrengthUnitBL> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<IEnumerable<MappingDoseStrengthUnit>> GetMappingDoseStrengthUnitsAsync()
        {
            return await _unitOfWork.MappingDoseStrengthUnitRepository.GetMappingDoseStrengthUnitsAsync();
        }

        public async Task<MappingDoseStrengthUnit> GetMappingDoseStrengthUnitAsync(int id)
        {
            return await _unitOfWork.MappingDoseStrengthUnitRepository.GetMappingDoseStrengthUnitAsync(id);
        }

        public async Task<MappingDoseStrengthUnit> GetMappingDoseStrengthUnitByDoseAsync(string doseUnit)
        {
            return await _unitOfWork.MappingDoseStrengthUnitRepository.GetMappingDoseStrengthUnitByDoseAsync(doseUnit);
        }

        public async Task<int> UpSert(MappingDoseStrengthUnit mapping, int userId)
        {
            mapping.UpdatedBy = userId;
            mapping.UpdatedDate = DateTime.Now;

            if (mapping.Id > 0)
            {
                _unitOfWork.MappingDoseStrengthUnitRepository.Update(mapping);
                _logger.LogInformation($"{StrConst.LOG_TYPE_UPDATE}: {mapping.DoseUnit}, USERID: {userId}");
            }
            else
            {
                mapping.CreateBy = userId;
                mapping.CreatedDate = DateTime.Now;

                _unitOfWork.MappingDoseStrengthUnitRepository.Add(mapping);
                _logger.LogInformation($"{StrConst.LOG_TYPE_ADDING}: {mapping.DoseUnit}, USERID: {userId}");
            }

            return await SaveAsync();
        }

        public async Task<int> SaveAsync()
        {
            return await _unitOfWork.SaveAsync();
        }
    }
}
