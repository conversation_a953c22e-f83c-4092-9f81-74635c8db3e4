﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace OHMC.DataAccess.Migrations
{
    public partial class Add_CancerGroupICD10 : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "CancerGroupICD10s",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    CancerGroupId = table.Column<int>(type: "int", nullable: false),
                    ICD10Code = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CancerGroupICD10s", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CancerGroupICD10s_CancerGroups_CancerGroupId",
                        column: x => x.CancerGroupId,
                        principalTable: "CancerGroups",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_CancerGroupICD10s_CancerGroupId",
                table: "CancerGroupICD10s",
                column: "CancerGroupId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "CancerGroupICD10s");
        }
    }
}
