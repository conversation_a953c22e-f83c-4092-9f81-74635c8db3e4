﻿using FoolProof.Core;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace OHMC.Core.Models
{
    public class ProtocolUserReview : ModelBase
    {
        [Key]
        public int Id { get; set; }
        [ForeignKey("Protocol")]
        public int ProtocolId { get; set; }
        public Protocol? Protocol { get; set; }
        public int OrderNumber { get; set; }
        [MaxLength(20)]
        public string? UserAction { get; set; }
        [MaxLength(400),RequiredIf("UserAction", Operator.NotEqualTo, "Approve", ErrorMessage = "Comment required.")]
        public string? Comment { get; set; }
        public bool IsResolved { get; set; }
        [MaxLength(100)]
        public string? UserName { get; set; } = string.Empty;
    }
}
