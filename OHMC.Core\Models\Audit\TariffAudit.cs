﻿using System.ComponentModel.DataAnnotations;

namespace OHMC.Core.Models
{
    public class TariffAudit : BaseAudit
    {
        [Key]
        public int TariffAuditId { get; set; }
        public int TariffId { get; set; }
        [MaxLength(20)]
        public string Code { get; set; } = string.Empty;
        public string ShortDescription { get; set; } = string.Empty;
        public string? LongDescription { get; set; } = string.Empty;
        public int TariffTypeId { get; set; }
        public string? Notes { get; set; } = string.Empty;
    }
}
