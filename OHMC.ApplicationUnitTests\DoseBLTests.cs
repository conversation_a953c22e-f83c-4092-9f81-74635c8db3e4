﻿using FluentAssertions;
using Microsoft.Extensions.Logging;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using OHMC.Application;
using OHMC.Core.Models;
using OHMC.DataAccess.Interface;

namespace OHMC.ApplicationUnitTests
{
    [TestClass]
    public class DoseBLTests : BaseTest
    {
        private readonly Mock<IUnitOfWork> _unitOfWork = new();
        private readonly Mock<ILogger<DoseBL>> _logger = new();
        private DoseBL? _doseBL;

        [TestInitialize]
        public void Initialize()
        {
            _doseBL = new DoseBL(_unitOfWork.Object, _logger.Object);
        }

        [TestMethod]
        public async Task GetProduct_Should_Return_SingleDose()
        {
            //given 
            Dose dose = new Dose()
            {
                Id = 1,
                INNId = 1,
                Route = "PO",
                CreateBy = 1,
                CreatedDate = DateTime.Now,
                UpdatedBy = 1,
                UpdatedDate = DateTime.Now,
                Deleted = false
            };

            bool doseUnitOfWorkRepositoryGetAsyncHasBeenCalled = false;
            _unitOfWork.Setup(x => x.DoseRepository.GetDoseAsync(It.IsAny<int>()))
                .Callback(() => doseUnitOfWorkRepositoryGetAsyncHasBeenCalled = true)
                .Returns(Task.FromResult(dose));

            //when 
            var result = await _doseBL.GetDoseAsync(1);

            //then
            Assert.IsNotNull(result);
            doseUnitOfWorkRepositoryGetAsyncHasBeenCalled.Should().BeTrue();
        }

        [TestMethod]
        public async Task GetProducts_Should_Return_Doses()
        {
            //given 
            Dose dose1 = new Dose()
            {
                Id = 1,
                INNId = 1,
                Route = "PO",
                CreateBy = 1,
                CreatedDate = DateTime.Now,
                UpdatedBy = 1,
                UpdatedDate = DateTime.Now,
                Deleted = false
            };

            Dose dose2 = new Dose()
            {
                Id = 1,
                INNId = 1,
                Route = "PO",
                CreateBy = 1,
                CreatedDate = DateTime.Now,
                UpdatedBy = 1,
                UpdatedDate = DateTime.Now,
                Deleted = false
            };

            IEnumerable<Dose> doseList = new List<Dose>() { dose1, dose2 };            

            bool doseUnitOfWorkRepositoryGetDosesAsyncHasBeenCalled = false;
            _unitOfWork.Setup(x => x.DoseRepository.GetDosesAsync())
                .Callback(() => doseUnitOfWorkRepositoryGetDosesAsyncHasBeenCalled = true)
                .Returns(Task.FromResult(doseList));

            //when 
            var result = await _doseBL.GetDosesAsync();

            //then
            Assert.IsNotNull(result);
            doseUnitOfWorkRepositoryGetDosesAsyncHasBeenCalled.Should().BeTrue();
            result.Count().Should().Be(2);
        }
    }
}
