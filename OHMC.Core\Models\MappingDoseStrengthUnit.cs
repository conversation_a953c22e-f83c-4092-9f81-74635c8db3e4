﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace OHMC.Core.Models
{
    public class MappingDoseStrengthUnit : ModelBase
    {
        [Key]
        public int Id { get; set; }
        [Required, <PERSON><PERSON><PERSON><PERSON>(20), <PERSON><PERSON><PERSON><PERSON><PERSON>("Dose Unit")]
        public string DoseUnit { get; set; } = string.Empty;
        [Required, <PERSON><PERSON><PERSON><PERSON>(20), <PERSON><PERSON><PERSON><PERSON><PERSON>("Strength Unit")]
        public string StrengthUnit { get; set; } = string.Empty;
    }
}
