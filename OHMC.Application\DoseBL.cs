﻿using Microsoft.Extensions.Logging;
using OHMC.Core.Common.Const;
using OHMC.Core.Common.Response;
using OHMC.Core.Common.Utils;
using OHMC.Core.Models;
using OHMC.DataAccess.Interface;

namespace OHMC.Application
{
    public class DoseBL : IDoseBL
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<DoseBL> _logger;
        private const int FILLER_COUNT = 10;
        public DoseBL(IUnitOfWork unitOfWork, ILogger<DoseBL> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<IEnumerable<Dose>> GetDosesAsync()
        {
            return await _unitOfWork.DoseRepository.GetDosesAsync();
        }

        public async Task<IEnumerable<Dose>> GetPagedDosesAsync(int pageNumber, int pageSize)
        {
            return await _unitOfWork.DoseRepository.GetPagedDosesAsync(pageNumber, pageSize);
        }

        public async Task<int> GetDoseCountAsync()
        {
            return await _unitOfWork.DoseRepository.GetDoseCounts();
        }

        public async Task<IEnumerable<Dose>> SearchDosesAsync(string desc)
        {
            return await _unitOfWork.DoseRepository.SearchDosesAsync(desc);
        }

        public async Task<Dose> GetDoseAsync(int id)
        {
            return await _unitOfWork.DoseRepository.GetDoseAsync(id);
        }

        public async Task<int> UpSert(Dose dose, int userId)
        {
            dose.UpdatedBy = userId;
            dose.UpdatedDate = DateTime.Now;

            if (dose.Id > 0)
            {
                _unitOfWork.DoseRepository.Update(dose);
                _logger.LogInformation($"{StrConst.LOG_TYPE_UPDATE}: {dose.DoseDescriptionCalcEdited}, USERID: {userId}");
            }
            else
            {
                dose.CreateBy = userId;
                dose.CreatedDate = DateTime.Now;
                dose.DoseCodeRef = await GetNextDoseCodeReference(dose.DoseDescriptionCalcEdited.Substring(0, 1));

                _unitOfWork.DoseRepository.Add(dose);
                _logger.LogInformation($"{StrConst.LOG_TYPE_ADDING}: {dose.DoseDescriptionCalcEdited}, USERID: {userId}");
            }

            await SaveAsync();
            return dose.Id;
        }

        public async Task<DoseProduct> GetDoseProductAsync(int id)
        {
            return await _unitOfWork.DoseProductRepository.GetDoseProductAsync(id);
        }

        public async Task<int> AddDoseProductAsync(int doseId, DoseProduct doseProduct)
        {
            _unitOfWork.DoseProductRepository.AddProduct(doseProduct);                
            return await _unitOfWork.SaveAsync();
        }
        
        public async Task<int> RemoveDoseProductAsync(DoseProduct doseProduct)
        {
            _unitOfWork.DoseProductRepository.RemoveDoseProduct(doseProduct);
            return await _unitOfWork.SaveAsync();
        }

        public async Task<int> GetDoseProductNextOrderNumberAsync(int doseId)
        {
            return (await _unitOfWork.DoseProductRepository.GetDoseProductCountAsync(doseId)) + 1;                
        }

        public async Task<string> GetNextDoseCodeReference(string prefix)
        {
            return StringHelper.AddDefaultPrefix(prefix, ((await _unitOfWork.DoseRepository.GetDoseCountByPrefixAsync(prefix) + 1) * 5).ToString(), FILLER_COUNT);
        }

        public double GetDoseProductPercentage(Dose dose)
        {
            var totalDose = dose?.DoseProducts == null ? 0 : dose?.DoseProducts?.Select(a=>a.StrengthValue).Sum();

            return (double)(totalDose / dose.StdDoseCalc) * 100;
        }

        public double GetDoseProductTotal(Dose dose)
        {
            var totalDose = dose?.DoseProducts == null ? 0 : dose?.DoseProducts?.Select(a => a.StrengthValue).Sum();

            return (double)totalDose;
        }

        public async Task<int> SaveAsync()
        {
            return await _unitOfWork.SaveAsync();
        }

        public async Task<Dose> GetPlainDoseAsync(int id)
        {
            return await _unitOfWork.DoseRepository.GetPlainDoseAsync(id);
        }

        public async Task<IEnumerable<Dose>> GetDosesByInnAsync(int id)
        {
            return await _unitOfWork.DoseRepository.GetDosesByInnAsync(id);
        }

        public async Task<GeneralResponse> DeleteDose(int id, int userId)
        {
            var dose = await _unitOfWork.DoseRepository.GetDoseAsync(id);

            if (dose != null)
            {
                dose.Deleted = true;
                dose.DeleteOn = DateTime.Now;
                dose.UpdatedBy = userId;
                dose.UpdatedDate = DateTime.Now;

                _unitOfWork.DoseRepository.Update(dose);
                _logger.LogInformation($"{StrConst.LOG_TYPE_REMOVING}: {dose.DoseDescriptionCalcEdited}, USERID: {userId}");
            }
            else
            {
                return new GeneralResponse()
                {
                    ResponseCode = -1,
                    ResponseMessage = StrConst.ACTION_DELETE_FAILED
                };
            }

            return new GeneralResponse()
            {
                ResponseCode = await SaveAsync(),
                ResponseMessage = StrConst.ACTION_DELETE_SUCCESSFUL
            };
        }
    }
}
