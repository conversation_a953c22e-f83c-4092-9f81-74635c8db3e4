﻿using OHMC.Core.Models;

namespace OHMC.DataAccess.Interface
{
    public interface IOutOfStockInternationalSignalRepository
    {
        void Add(OutOfStockInternationalSignal signal);
        Task<IEnumerable<OutOfStockInternationalSignal>> GetOutOfStockInternationalSignalsAsync();
        Task<OutOfStockInternationalSignal> GetOutOfStockInternationalSignalAsync(int id);
        void Update(OutOfStockInternationalSignal signal);
    }
}