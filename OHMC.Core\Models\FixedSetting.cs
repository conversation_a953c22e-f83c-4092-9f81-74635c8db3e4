﻿using System.ComponentModel.DataAnnotations;

namespace OHMC.Core.Models
{
    public class FixedSetting : ModelBase
    {
        [Key]
        public int Id { get; set; }
        [Required, Range(0.0, 5, ErrorMessage = "The field {0} must be greater than {1}.")]
        public decimal BSA { get; set; } = 0;
        [Required, Range(10, 150, ErrorMessage = "The field {0} must be greater than {1}.")]
        public decimal Weight { get; set; } = 0;
        public decimal VATPercentage { get; set; }
        public int? TariffYearUpdate { get; set; }
    }
}
