﻿<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>f036e45b-2c75-4e44-9adc-c39b9c435c90</UserSecretsId>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="bootstrap" Version="5.3.3" />
    <PackageReference Include="DevExpress.AspNetCore.Reporting" Version="24.1.4" />
    <PackageReference Include="jQuery" Version="3.7.1" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="6.0.30">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="6.0.30">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Serilog.AspNetCore" Version="6.1.0" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\OHMC.Application\OHMC.Application.csproj" />
    <ProjectReference Include="..\OHMC.DataAccess\OHMC.DataAccess.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Models\Network\" />
    <Folder Include="wwwroot\attachments\" />
    <Folder Include="wwwroot\lib\microsoft\" />
    <PackageReference Include="DevExtreme.AspNet.Core" Version="24.1.4" />
    <PackageReference Include="DevExtreme.AspNet.Data" Version="4.0.0" />
  </ItemGroup>
</Project>