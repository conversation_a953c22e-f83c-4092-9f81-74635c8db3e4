/*!
 * CLDR JavaScript Library v0.5.4 2020-10-22T15:56Z MIT license © Rafael Xavier
 * http://git.io/h4lmVg
 */
!function(e,t){"function"==typeof define&&define.amd?define(t):"object"==typeof module&&"object"==typeof module.exports?module.exports=t():e.Cldr=t()}(this,(function(){var e,t=Array.isArray||function(e){return"[object Array]"===Object.prototype.toString.call(e)},n=function(e,n){if(t(e)&&(e=e.join("/")),"string"!=typeof e)throw new Error('invalid path "'+e+'"');return(e=(e=e.replace(/^\//,"").replace(/^cldr\//,"")).replace(/{[a-zA-Z]+}/g,(function(e){return e=e.replace(/^{([^}]*)}$/,"$1"),n[e]}))).split("/")},r=function(e,t){var n,r;if(e.some)return e.some(t);for(n=0,r=e.length;n<r;n++)if(t(e[n],n,e))return!0;return!1},o=function(e,t,n,o){var a,i=n[0],l=n[1],u=e.localeSep,c=n[2],f=n.slice(3,4);return o=o||{},"und"!==i&&"Zzzz"!==l&&"ZZ"!==c?[i,l,c].concat(f):void 0!==t.get("supplemental/likelySubtags")?r([[i,l,c],[i,c],[i,l],[i],["und",l]],(function(e){return a=!/\b(Zzzz|ZZ)\b/.test(e.join(u))&&t.get(["supplemental/likelySubtags",e.join(u)])}))?(a=a.split(u),["und"!==i?i:a[0],"Zzzz"!==l?l:a[1],"ZZ"!==c?c:a[2]].concat(f)):o.force?t.get("supplemental/likelySubtags/und").split(u):void 0:void 0},a=function(e,t,n){var a,i=n[0],l=n[1],u=n[2],c=n[3];return r([[[i,"Zzzz","ZZ"],[i]],[[i,"Zzzz",u],[i,u]],[[i,l,"ZZ"],[i,l]]],(function(r){var i=o(e,t,r[0]);return a=r[1],i&&i[0]===n[0]&&i[1]===n[1]&&i[2]===n[2]}))?(c&&a.push(c),a):n},i=function(e){var t,n=[];return(t=(e=e.replace(/_/,"-")).split("-u-"))[1]&&(t[1]=t[1].split("-t-"),e=t[0]+(t[1][1]?"-t-"+t[1][1]:""),n[4]=t[1][0]),null===(t=e.split("-t-")[0].match(/^(([a-z]{2,3})(-([A-Z][a-z]{3}))?(-([A-Z]{2}|[0-9]{3}))?)((-([a-zA-Z0-9]{5,8}|[0-9][a-zA-Z0-9]{3}))*)$|^(root)$/))?["und","Zzzz","ZZ"]:(n[0]=t[10]||t[2]||"und",n[1]=t[4]||"Zzzz",n[2]=t[6]||"ZZ",t[7]&&t[7].length&&(n[3]=t[7].slice(1)),n)},l=function(e,t){var n,r;if(e.forEach)return e.forEach(t);for(n=0,r=e.length;n<r;n++)t(e[n],n,e)},u=function(e,t,n){var r=e._availableBundleMap,u=e._availableBundleMapQueue;return u.length&&(l(u,(function(n,l){var c,f,p,s;if(s=i(n),void 0===(f=o(e,t,s)))throw u.splice(l,1),new Error("Could not find likelySubtags for "+n);p=(p=a(e,t,f)).join(e.localeSep),(c=r[p])&&c.length<n.length||(r[p]=n)})),e._availableBundleMapQueue=[]),r[n]||null},c=function(e,t){var n,r;return r=e+(t&&JSON?": "+JSON.stringify(t):""),(n=new Error(r)).code=e,l(function(e){var t,n=[];if(Object.keys)return Object.keys(e);for(t in e)n.push(t);return n}(t),(function(e){n[e]=t[e]})),n},f=function(e,t,n){if(!t)throw c(e,n)},p=function(e,t){f("E_MISSING_PARAMETER",void 0!==e,{name:t})},s=function(e,t,n,r){f("E_INVALID_PAR_TYPE",n,{expected:r,name:t,value:e})},d=function(e,n){s(e,n,"string"==typeof e||t(e),"String or Array")},v=function(e,t){var n;s(e,t,void 0===e||null!==(n=e)&&""+n=="[object Object]","Plain Object")},h=function(e,t){var n,r=e,o=t.length;for(n=0;n<o-1;n++)if(!(r=r[t[n]]))return;return r[t[n]]},g=function(e,t){var n,r=e._availableBundleMapQueue,o=h(t,["main"]);if(o)for(n in o)o.hasOwnProperty(n)&&"root"!==n&&-1===r.indexOf(n)&&r.push(n)},z=function(e){return t(e)?e:[e]},b=(e=function(){var n={},r=[].slice.call(arguments,0);return l(r,(function(r){var o;for(o in r)o in n&&"object"==typeof n[o]&&!t(n[o])?n[o]=e(n[o],r[o]):n[o]=r[o]})),n},e),y=function(e,t,n){var r,o,a;for(p(n[0],"json"),r=0;r<n.length;r++)for(a=z(n[r]),o=0;o<a.length;o++)v(a[o],"json"),t=b(t,a[o]),g(e,a[o]);return t},_=function(e,t,r){var o=n(t,r);return h(e._resolved,o)},Z=function(e){this.init(e)};return Z._alwaysArray=z,Z._coreLoad=y,Z._createError=c,Z._itemGetResolved=_,Z._jsonMerge=b,Z._pathNormalize=n,Z._resourceGet=h,Z._validatePresence=p,Z._validateType=s,Z._validateTypePath=d,Z._validateTypePlainObject=v,Z._availableBundleMap={},Z._availableBundleMapQueue=[],Z._resolved={},Z.localeSep="-",Z.load=function(){Z._resolved=y(Z,Z._resolved,arguments)},Z.prototype.init=function(e){var t,n,r,l,c,f,d,v,h,g,z=Z.localeSep,b="";p(e,"locale"),s(g=e,"locale","string"==typeof g,"a string"),5===(f=i(e)).length&&(b=z+"u"+z+(v=f.pop()),f[3]||f.pop()),h=f[3],n=(r=o(Z,this,f,{force:!0})||f)[0],c=r[1],d=r[2],l=a(Z,this,r).join(z),this.attributes=t={bundle:u(Z,this,l),minLanguageId:l+b,maxLanguageId:r.join(z)+b,language:n,script:c,territory:d,region:d,variant:h},v&&("-"+v).replace(/-[a-z]{3,8}|(-[a-z]{2})-([a-z]{3,8})/g,(function(e,n,r){n?t["u"+n]=r:t["u"+e]=!0})),this.locale=e},Z.prototype.get=function(e){return p(e,"path"),d(e,"path"),_(Z,e,this.attributes)},Z.prototype.main=function(e){return p(e,"path"),d(e,"path"),f("E_MISSING_BUNDLE",null!==this.attributes.bundle,{locale:this.locale}),e=z(e),this.get(["main/{bundle}"].concat(e))},Z}));
