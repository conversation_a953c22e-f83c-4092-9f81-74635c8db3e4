﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace OHMC.Core.Models
{
    public class Strength : ModelBase
    {
        [Key]
        public int Id { get; set; }
        [Required, <PERSON><PERSON><PERSON><PERSON>(10), Display<PERSON><PERSON>("Code")]
        public string StrengthCode { get; set; } = string.Empty;
        [Required, Display<PERSON><PERSON>("Strength Value")]
        public decimal StrengthValue { get; set; }
        [Required, <PERSON><PERSON><PERSON><PERSON>(20), <PERSON><PERSON><PERSON><PERSON><PERSON>("Strength Unit")]
        public string StrengthUnit { get; set; } = string.Empty;
        [Required, DisplayName("Description")]
        public string StrengthDescription { get; set; } = string.Empty;
    }
}
