﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace OHMC.DataAccess.Migrations
{
    public partial class Regimens_IsUntilRegression : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "IsUntilProgression",
                table: "RegimensAudit",
                type: "bit",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsUntilProgression",
                table: "Regimens",
                type: "bit",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsUntilProgression",
                table: "RegimensAudit");

            migrationBuilder.DropColumn(
                name: "IsUntilProgression",
                table: "Regimens");
        }
    }
}
