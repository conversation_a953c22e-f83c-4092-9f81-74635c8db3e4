﻿using Microsoft.Extensions.Logging;
using OHMC.Core.Common.Const;
using OHMC.Core.Models;
using OHMC.DataAccess.Interface;

namespace OHMC.Application
{
    public class DoseUnitsBL : IDoseUnitsBL
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<DoseUnitsBL> _logger;

        public DoseUnitsBL(IUnitOfWork unitOfWork, ILogger<DoseUnitsBL> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<IEnumerable<DoseUnit>> GetDoseUnitsAsync()
        {
            return await _unitOfWork.DoseUnitRepository.GetDoseUnitsAsync();
        }

        public async Task<DoseUnit> GetDoseUnitAsync(int id)
        {
            return await _unitOfWork.DoseUnitRepository.GetDoseUnitAsync(id);
        }

        public async Task<int> UpSert(DoseUnit doseUnit, int userId)
        {
            doseUnit.UpdatedBy = userId;
            doseUnit.UpdatedDate = DateTime.Now;

            if (doseUnit.Id > 0)
            {
                _unitOfWork.DoseUnitRepository.Update(doseUnit);
                _logger.LogInformation($"{StrConst.LOG_TYPE_UPDATE}: {doseUnit.DoseUnitName}, USERID: {userId}");
            }
            else
            {
                doseUnit.CreateBy = userId;
                doseUnit.CreatedDate = DateTime.Now;

                _unitOfWork.DoseUnitRepository.Add(doseUnit);
                _logger.LogInformation($"{StrConst.LOG_TYPE_ADDING}: {doseUnit.DoseUnitName}, USERID: {userId}");
            }

            return await SaveAsync();
        }

        public async Task<int> SaveAsync()
        {
            return await _unitOfWork.SaveAsync();
        }
    }
}
