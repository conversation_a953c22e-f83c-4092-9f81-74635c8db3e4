﻿using Microsoft.Extensions.Logging;
using OHMC.Core.Common.Const;
using OHMC.Core.Common.Response;
using OHMC.Core.Models;
using OHMC.DataAccess.Interface;

namespace OHMC.Application
{
    public class ProductBL : IProductBL
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<ProductBL> _logger;

        public ProductBL(IUnitOfWork unitOfWork, ILogger<ProductBL> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<IEnumerable<Product>> GetProductsFullAsync()
        {
            return await _unitOfWork.ProductRepository.GetProductsAsync();
        }

        public async Task<IEnumerable<Product>> GetProductsFullWithFormulationAsync()
        {
            return await _unitOfWork.ProductRepository.GetProductsFullAsync();
        }

        public async Task<IEnumerable<Product>> GetPagedProductsAsync(int pageNumber, int pageSize)
        {
            return await _unitOfWork.ProductRepository.GetPagedProductsAsync(pageNumber, pageSize);
        }

        public async Task<int> GetProductsCountAsync()
        {
            return await _unitOfWork.ProductRepository.GetProductsCountAsync();
        }

        public async Task<IEnumerable<Product>> SearchProductsAsync(string desc)
        {
            return await _unitOfWork.ProductRepository.SearchProductsAsync(desc);
        }

        public async Task<IEnumerable<Product>> GetProductsAsync()
        {
            return await _unitOfWork.ProductRepository.GetProductsAsync();
        }

        public async Task<Product> GetProductAsync(int id)
        {
            return await _unitOfWork.ProductRepository.GetProductAsync(id);
        }

        public async Task<int> UpSert(Product product, int userId)
        {            
            bool updatingProduct = false;
            var selectedFormulation = await _unitOfWork.FormulationRepository.GetFormulationOnlyAsync(product.FormulationId);
            product.StrengthDescription = selectedFormulation.Strength;

            //ProductName - Inn + strength  +Dose Units +  Route
            //product.ProductName = product.ActiveIngredient + " " 
            //    + selectedFormulation.Strength + " " 
            //    + product.AdministrationUnit + " " 
            //    + product.PrimaryRoute;
            product.StrengthValue = selectedFormulation.StrengthDose;
            product.StrengthValueUnit = selectedFormulation.StrengthUnits;
            product.StrengthVolume = selectedFormulation.StrengthVolume;
            product.StrengthVolumeUnit = selectedFormulation.StrengthVolumeUnits;
            product.DosageForm = selectedFormulation.DosageForm;
            product.UpdatedBy = userId;
            product.UpdatedDate = DateTime.Now;

            if (product.Id > 0)
            {
                product.IsAvailable = await CheckProductAvailability(product.Id);
                _unitOfWork.ProductRepository.Update(product);
                _logger.LogInformation($"{StrConst.LOG_TYPE_UPDATE}: {product.ProductName}, USERID: {userId}");     
                updatingProduct = true;
            }
            else
            {
                product.CreateBy = userId;
                product.CreatedDate = DateTime.Now;

                _unitOfWork.ProductRepository.Add(product);
                _logger.LogInformation($"{StrConst.LOG_TYPE_ADDING}: {product.ProductName}, USERID: {userId}");
            }

            var saveProductResponse = await SaveAsync();

            if (updatingProduct)
            {
                //var doseIds = await UpdateDoseProductPrices(product.Id);
                ////->Dose->Dose Product
                ////    ->Dosing Regimen
                ////        ->Regimen
                ////            ->Protocols

                //var f = UpdateDoseRegimenPrices(doseIds);
            }

            return saveProductResponse;
        }

        /// <summary>
        /// TODO:- Test this methods
        /// </summary>
        /// <param name="productId"></param>
        private async Task<List<int>> UpdateDoseProductPrices(int productId)
        {
            var product = await _unitOfWork.ProductRepository.GetProductSimpleAsync(productId);
            var doseProducts = await _unitOfWork.DoseProductRepository.GetDoseProductByProductAsync(productId);
            var doseIds = new List<int>();

            foreach (var doseProduct in doseProducts) 
            {
                doseProduct.Price = (double)product.PricePerPack;
                _unitOfWork.DoseProductRepository.Update(doseProduct);     
                doseIds.Add(doseProduct.DoseId);
            }       
                
            var updateDoseProductResponse = _unitOfWork.SaveAsync();

            return doseIds;
        }

        /// <summary>
        /// TODO:- Test this methods
        /// </summary>
        /// <param name="productId"></param>
        private async Task<List<int>> UpdateDoseRegimenPrices(List<int> doseIds)
        {
            var regimenList = new List<int>();

            foreach (var doseId in doseIds)
            {            
                var dose = await _unitOfWork.DoseRepository.GetDoseAsync(doseId);
                var dosingRegimens = await _unitOfWork.DosingRegimenRepository.GetDosingRegimenByDoseAsync(doseId);

                foreach (var dosingRegimen in dosingRegimens)
                {
                    //find regimen and update regimen costs
                    int dailyDoseCount = dose?.ConfirmDosesInADay == null ? 1 : (int)dose?.ConfirmDosesInADay;
                    var productProductCost = (decimal)dose.DoseProducts.Select(a => a.Product.PricePerUnit * (decimal)a.DispensingUnitsNumber).Sum() * dailyDoseCount;

                    dosingRegimen.CostPerRegimen = (double?)(productProductCost * dosingRegimen.NumberOfDaysPatientDosed);

                    _unitOfWork.DosingRegimenRepository.Update(dosingRegimen);

                    var Regimen = (await _unitOfWork.RegimenDosingRegimenRepository.GetRegimenDosingRegimenByDosingRegimenAsync(dosingRegimen.Id))
                        .Select(x => x.RegimenId).ToList();
                    
                    regimenList.AddRange(Regimen);
                }

                var updateDosingRegimenResponse = _unitOfWork.SaveAsync();
            }

            return regimenList;
        }

        public void AddProductDocument(ProductDocument document)
        {
            _unitOfWork.ProductDocumentRepository.Add(document);
        }

        public async Task<IEnumerable<ProductDocument>> GetProductDocumentsAsync(int productId)
        {
            return await _unitOfWork.ProductDocumentRepository.GetProductDocumentsAsync(productId);
        }

        public async Task<int> SaveAsync()
        {
            return await _unitOfWork.SaveAsync();
        }

        public async Task<CopyProductResponse> CopyProductAsync(int id, int userId)
        {
            var product = await _unitOfWork.ProductRepository.GetProductAsync(id);

            var newProduct = new Product()
            {
                ActiveIngredient = product.ActiveIngredient,
                AdministrationUnit = product.AdministrationUnit,
                DispensingUnit = product.DispensingUnit,
                DosageForm = product.DosageForm,
                DosageFormType = product.DosageFormType,
                DoseUnitName = product.DoseUnitName,
                FormulationId = product.FormulationId,
                IsAvailable = product.IsAvailable,
                IsBreakBulk = product.IsBreakBulk,
                IsDiluentIncluded = product.IsDiluentIncluded,
                IsRequireDiluent = product.IsRequireDiluent,
                IsRegistered = product.IsRegistered,
                IsSeries = product.IsSeries,
                Level3Description = product.Level3Description,
                Manufacturer = product.Manufacturer,
                Nappi9 = product.Nappi9,
                PacketIntake = product.PacketIntake,
                PackSize = product.PackSize,
                PresentationDescription = product.PresentationDescription,
                PricePerDosingUnit = product.PricePerDosingUnit,
                PricePerPack = product.PricePerPack,
                PricePerUnit = product.PricePerUnit,
                PrimaryRoute = product.PrimaryRoute,
                ProductName = product.ProductName,
                SEPExclVat = product.SEPExclVat,
                SEPPermanent = product.SEPPermanent,
                SEPStatus = StrConst.PRODUCT_STATUS_PERMANENT,
                SEPTempDate = null,
                StandardPackSize = product.StandardPackSize,
                StrengthDescription = product.StrengthDescription,
                StrengthValue = product.StrengthValue,
                StrengthValueUnit = product.StrengthValueUnit,
                StrengthVolume = product.StrengthVolume,
                StrengthVolumeUnit = product.StrengthVolumeUnit,                
                CreateBy = userId,
                CreatedDate = DateTime.Now,
                UpdatedBy = userId,
                UpdatedDate = DateTime.Now,
                Deleted = false,
            };

            _unitOfWork.ProductRepository.Add(newProduct);

            try
            {
                await SaveAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex.Message);

                return new CopyProductResponse()
                {
                    ResponseCode = -1,
                    ResponseMessage = $"{StrConst.ACTION_COPY_FAILED}. Formulation {product.ProductName}",
                    NewCode = string.Empty,
                    NewId = -1,
                };
            }

            return new CopyProductResponse()
            {
                ResponseCode = newProduct.Id,
                ResponseMessage = $"{StrConst.ACTION_COPY_SUCCESSFUL} ",
                NewCode = newProduct.ProductName,
                NewId = newProduct.Id
            };
        }

        public async Task<IEnumerable<MedicineAvailability>> GetMedicineAvailabilitiesAsync(int productId)
        {
            return await _unitOfWork.MedicineAvailabilityRepository.GetMedicineAvailabilitiesAsync(productId);
        }

        public async Task<MedicineAvailability> GetMedicineAvailabilityAsync(int id)
        {
            return await _unitOfWork.MedicineAvailabilityRepository.GetMedicineAvailabilityAsync(id);
        }

        public async Task<int> UpSertMedicineAvailability(MedicineAvailability availability, int userId)
        {
            availability.UpdatedBy = userId;
            availability.UpdatedDate = DateTime.Now;

            if (availability.Id > 0)
            {
                _unitOfWork.MedicineAvailabilityRepository.Update(availability);
                _logger.LogInformation($"{StrConst.LOG_TYPE_UPDATE}: {availability.Id}, USERID: {userId}");
            }
            else
            {
                availability.CreateBy = userId;
                availability.CreatedDate = DateTime.Now;

                _unitOfWork.MedicineAvailabilityRepository.Add(availability);
                _logger.LogInformation($"{StrConst.LOG_TYPE_ADDING}: {availability.Id}, USERID: {userId}");
            }

            var saveProductResponse = await SaveAsync();

            return saveProductResponse;
        }

        public async Task<bool> CheckProductAvailability(int productId)
        {
            var product = await _unitOfWork.ProductRepository.GetProductAsync(productId);
            var latestAvailability = await GetLatestMedicineAvailabilityAsync(productId);
            bool isAvailable = true;

            if(latestAvailability != null) 
            {                    
                if((latestAvailability.DateBackInStock == null) && (latestAvailability.SecurityOfSupply == StrConst.MEDICINE_OUT_OF_STOCK))
                {                         
                    isAvailable = false;
                }                  
            }
            else
            {
                isAvailable = true;
            }

            return isAvailable;
        }

        public Task<MedicineAvailability> GetLatestMedicineAvailabilityAsync(int productId)
        {
            return _unitOfWork.MedicineAvailabilityRepository.GetLatestMedicineAvailabilityAsync(productId);
        }

        public async Task<IEnumerable<ProductSEPOverride>> GetProductSEPOverridesAsync(int productId)
        {
            return await _unitOfWork.ProductRepository.GetProductSEPOverridesAsync(productId);
        }
    }
}