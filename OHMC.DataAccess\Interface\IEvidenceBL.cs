﻿using OHMC.Core.Models;

namespace OHMC.DataAccess.Interface
{
    public interface IEvidenceBL
    {
        Task<IEnumerable<Evidence>> GetAllEvidencesAsync();
        Task<IEnumerable<Evidence>> GetAllEvidencesPagedAsync(int pageNumber, int pageSize);
        Task<int> GetEvidenceCountAsync();
        Task<Evidence> GetEvidencesAsync(int id);
        Task<int> UpSert(Evidence evidences, int userId);
        Task<int> SaveAsync();
    }
}