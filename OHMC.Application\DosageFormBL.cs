﻿using Microsoft.Extensions.Logging;
using OHMC.Core.Common.Const;
using OHMC.Core.Models;
using OHMC.DataAccess.Interface;

namespace OHMC.Application
{
    public class DosageFormBL : IDosageFormBL
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<DosageFormBL> _logger;

        public DosageFormBL(IUnitOfWork unitOfWork, ILogger<DosageFormBL> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<IEnumerable<DosageForm>> GetDosageFormsAsync()
        {
            return await _unitOfWork.DosageFormRepository.GetDosageFormsAsync();
        }

        public async Task<IEnumerable<DosageForm>> SearchFormTypeAsync(string desc)
        {
            return await _unitOfWork.DosageFormRepository.SearchDosageFormsAsync(desc);
        }

        public async Task<DosageForm> GetDosageFormAsync(int id)
        {
            return await _unitOfWork.DosageFormRepository.GetDosageFormAsync(id);
        }

        public async Task<IEnumerable<DosageForm>> GetDosageFormsByFormTypeAsync(string formType)
        {
            return await _unitOfWork.DosageFormRepository.GetDosageFormByFormTypeAsync(formType);
        }

        public async Task<int> UpSert(DosageForm dosageForm, int userId)
        {
            dosageForm.UpdatedBy = userId;
            dosageForm.UpdatedDate = DateTime.Now;

            if (dosageForm.Id > 0)
            {
                _unitOfWork.DosageFormRepository.Update(dosageForm);
                _logger.LogInformation($"{StrConst.LOG_TYPE_UPDATE}: {dosageForm.FormDescription}, USERID: {userId}");
            }
            else
            {
                dosageForm.CreateBy = userId;
                dosageForm.CreatedDate = DateTime.Now;

                _unitOfWork.DosageFormRepository.Add(dosageForm);
                _logger.LogInformation($"{StrConst.LOG_TYPE_ADDING}: {dosageForm.FormDescription}, USERID: {userId}");
            }

            return await SaveAsync();
        }

        public async Task<int> RemoveFormType(int id, int userId)
        {
            var dosageForm = await _unitOfWork.DosageFormRepository.GetDosageFormAsync(id);
            dosageForm.UpdatedBy = userId;
            dosageForm.UpdatedDate = DateTime.Now;
            dosageForm.Deleted = true;
            dosageForm.DeleteOn = DateTime.Now;
            _unitOfWork.DosageFormRepository.Update(dosageForm);
            _logger.LogInformation($"{StrConst.LOG_TYPE_REMOVING}: {dosageForm.FormTypeName}, USERID: {userId}");
            return await _unitOfWork.SaveAsync();
        }

        public async Task<int> SaveAsync()
        {
            return await _unitOfWork.SaveAsync();
        }
    }
}
