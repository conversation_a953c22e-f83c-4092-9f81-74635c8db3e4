﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace OHMC.Core.Models
{
    public class ProductDocument : ModelBase
    {
        [Key]
        public int Id { get; set; }
        [MaxLength(200)]
        public string Description { get; set; } = string.Empty;
        [MaxLength(200)]
        public string FileName { get; set; } = string.Empty;
        [MaxLength(30)]
        public string DocumentType { get; set; } = string.Empty;
        [MaxLength(200)]
        public string? FileLink { get; set; }
        [ForeignKey("Product")]
        public int ProductId { get; set; }
        public Product Product { get; set; }
    }
}
