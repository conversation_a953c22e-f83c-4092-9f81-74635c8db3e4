﻿using OHMC.Core.Models;

namespace OHMC.DataAccess.Interface
{
    public interface INetworkMemberRepository
    {
        void Add(NetworkMember member);
        Task<NetworkMember> GetNetworkMemberAsync(int id);
        Task<IEnumerable<NetworkMember>> GetNetworkMembersAsync();
        Task<IEnumerable<NetworkMember>> GetNetworkPracticeMembersAsync(int networkPracticeId);
        void Update(NetworkMember member);
    }
}