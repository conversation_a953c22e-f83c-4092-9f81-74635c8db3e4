﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace OHMC.Core.Models
{
    public class MedicalSchemeBenefitPlan : ModelBase
    {
        [Key]
        public int Id { get; set; }
        [Required, MaxLength(200)]
        public string BenefitName { get; set; } = string.Empty;
        [Range(1999, 2099)]
        public int BenefitYear { get; set; }
        [ForeignKey("MedicalScheme")]
        public int MedicalSchemeId { get; set; }
        public MedicalScheme MedicalScheme { get; set; }
    }
}
