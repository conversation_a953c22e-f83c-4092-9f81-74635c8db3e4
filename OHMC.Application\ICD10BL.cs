﻿using OHMC.Core.Models;
using OHMC.DataAccess.Interface;

namespace OHMC.Application
{
    public class ICD10BL : IICD10BL
    {
        private readonly IUnitOfWork _unitOfWork;

        public ICD10BL(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<IEnumerable<ICD10>> GetICD10sAsync()
        {
            return await _unitOfWork.ICD10Repository.GetICD10sAsync();
        }

        public async Task<IEnumerable<ICD10>> GetICD10sAsync(string code)
        {
            return await _unitOfWork.ICD10Repository.GetICD10sAsync(code);
        }
    }
}
