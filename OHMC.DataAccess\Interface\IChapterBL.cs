﻿using OHMC.Core.Models;

namespace OHMC.DataAccess.Interface
{
    public interface IChapterBL
    {
        Task<Chapter> GetChapterAsync(int id);
        Task<IEnumerable<Chapter>> GetChapters();
        Task<IEnumerable<Chapter>> GetChaptersByFormularyType(int formularyTypeId);
        Task<IEnumerable<Chapter>> GetChaptersByPTCType(int ptcType);
        Task<int> RemoveChapter(int id, int userId);
        Task<int> SaveAsync();
        Task<int> UpSert(Chapter chapter, int userId);
    }
}