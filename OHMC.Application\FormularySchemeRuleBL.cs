﻿using Microsoft.Extensions.Logging;
using OHMC.Core.Common.Const;
using OHMC.Core.Models;
using OHMC.DataAccess.Interface;

namespace OHMC.Application
{
    public class FormularySchemeRuleBL : IFormularySchemeRuleBL
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<FormularySchemeRuleBL> _logger;

        public FormularySchemeRuleBL(IUnitOfWork unitOfWork, ILogger<FormularySchemeRuleBL> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<IEnumerable<FormularySchemeRule>> GetFormularySchemeRulesAsync()
        {
            return await _unitOfWork.FormularySchemeRuleRepository.GetFormularySchemeRulesAsync();
        }

        public async Task<IEnumerable<FormularySchemeRule>> GetFormularySchemeRulesFullAsync()
        {
            return await _unitOfWork.FormularySchemeRuleRepository.GetFormularySchemeRulesFullAsync();
        }

        public async Task<FormularySchemeRule> GetFormularySchemeRuleAsync(int id)
        {
            return await _unitOfWork.FormularySchemeRuleRepository.GetFormularySchemeRuleAsync(id);
        }

        public async Task<int> UpSert(FormularySchemeRule rule, int userId)
        {
            rule.UpdatedBy = userId;
            rule.UpdatedDate = DateTime.Now;

            if (rule.Id > 0)
            {
                _unitOfWork.FormularySchemeRuleRepository.Update(rule);
                _logger.LogInformation($"{StrConst.LOG_TYPE_UPDATE}: {rule.FormularyType}, USERID: {userId}");
            }
            else
            {
                rule.CreateBy = userId;
                rule.CreatedDate = DateTime.Now;
                rule.IsActive = true;

                _unitOfWork.FormularySchemeRuleRepository.Add(rule);
                _logger.LogInformation($"{StrConst.LOG_TYPE_ADDING}: {rule.FormularyType}, USERID: {userId}");
            }

            return await SaveAsync();
        }

        public async Task<int> SaveAsync()
        {
            return await _unitOfWork.SaveAsync();
        }
    }
}
