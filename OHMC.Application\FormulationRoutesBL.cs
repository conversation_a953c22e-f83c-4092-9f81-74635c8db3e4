﻿using Microsoft.Extensions.Logging;
using OHMC.Core.Common.Const;
using OHMC.Core.Models;
using OHMC.DataAccess.Interface;

namespace OHMC.Application
{
    public class FormulationRoutesBL : IFormulationRoutesBL
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<FormulationRoutesBL> _logger;

        public FormulationRoutesBL(IUnitOfWork unitOfWork, ILogger<FormulationRoutesBL> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<FormulationRoute> GetFormuationRoute(int id)
        {
            return await _unitOfWork.FormulationRoutesRepository.GetFormulationRouteAsync(id);
        }

        public async Task<IEnumerable<FormulationRoute>> GetFormulationRoutesByFormulationAsync(int formulationId)
        {
            return await _unitOfWork.FormulationRoutesRepository.GetFormulationRoutesByFormulationAsync(formulationId);
        }

        public async Task<int> UpSert(FormulationRoute formulationRoute, int userId)
        {
            formulationRoute.UpdatedBy = userId;
            formulationRoute.UpdatedDate = DateTime.Now;

            if (formulationRoute.Id > 0)
            {
                _unitOfWork.FormulationRoutesRepository.Update(formulationRoute);
                _logger.LogInformation($"{StrConst.LOG_TYPE_UPDATE}: {formulationRoute.RouteName}, USERID: {userId}");
            }
            else
            {
                formulationRoute.CreateBy = userId;
                formulationRoute.CreatedDate = DateTime.Now;

                _unitOfWork.FormulationRoutesRepository.Add(formulationRoute);
                _logger.LogInformation($"{StrConst.LOG_TYPE_ADDING}: {formulationRoute.RouteName}, USERID: {userId}");
            }

            return await SaveAsync();
        }

        public async Task<int> GetNumberOfRouteByFormulationAsync(int id)
        {
            var formulationRoutes = await _unitOfWork.FormulationRoutesRepository.GetFormulationRoutesByFormulationAsync(id);

            return formulationRoutes.Count();
        }

        public async Task<int> GetMaxOrderNumberRouteByFormulationAsync(int id)
        {
            return await _unitOfWork.FormulationRoutesRepository.GetMaxOrderNumberRouteByFormulationAsync(id);
        }

        public async Task<int> RemoveFormulationRoute(FormulationRoute formulationRoute)
        {
            _unitOfWork.FormulationRoutesRepository.RemoveFormulationRoute(formulationRoute);
            return await SaveAsync();
        }

        public async Task<int> SaveAsync()
        {
            return await _unitOfWork.SaveAsync();
        }
    }
}
