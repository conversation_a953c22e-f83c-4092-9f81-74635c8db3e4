﻿using System.ComponentModel.DataAnnotations;

namespace OHMC.Core.Models
{
    public class FormulationAudit : BaseAudit
    {
        [Key]
        public int FormulationAuditId { get; set; }
        public int FormulationId { get; set; }
        [MaxLength(10)]
        public string PrimaryRoute { get; set; } = string.Empty;
        public int INNId { get; set; }
        public int DoseUnitId { get; set; }
        [MaxLength(30)]
        public string DosageFormType { get; set; } = string.Empty;
        [MaxLength(50)]
        public string DosageForm { get; set; } = string.Empty;
        public bool CanBeAdministeredDivisibleUnits { get; set; } = false;
        [MaxLength(20)]
        public string DivisiblePerUnitVol { get; set; } = string.Empty;
        public decimal StrengthDose { get; set; }
        [MaxLength(20)]
        public string StrengthUnits { get; set; } = string.Empty;
        public decimal? StrengthVolume { get; set; }
        [MaxLength(20)]
        public string? StrengthVolumeUnits { get; set; }
        [MaxLength(500)]
        public string Strength { get; set; } = string.Empty;
        [Required]
        public string DosageFormDescriptionCalc { get; set; } = string.Empty;
        public bool IsTherapeuticClass { get; set; }
        [MaxLength(100)]
        public string? TherapeuticClass { get; set; }
        public int? TherapeuticId { get; set; }
        [MaxLength(10)]
        public string? IsTender { get; set; }
        [MaxLength(20)]
        public string? TenderNumber { get; set; }
        public DateTime? TenderStartDate { get; set; }
        public DateTime? TenderEndDate { get; set; }
        public bool IsTenderAwarded { get; set; }
        public decimal? TenderPrice { get; set; }
        [MaxLength(20)]
        public string? NSN { get; set; }
        public decimal? TotalVolume { get; set; }
        [MaxLength(20)]
        public string? IndexNappi { get; set; } = string.Empty;
        public bool IsFixedDoseCombination { get; set; }
        public bool IsCombinationPack { get; set; }
    }
}
