﻿using OHMC.Core.Models;

namespace OHMC.DataAccess.Interface
{
    public interface ICancerGroupTypeRepository
    {
        void Add(CancerGroupType groupType);
        Task<IEnumerable<CancerGroupType>> GetAllCancerGroupTypesAsync();
        Task<IEnumerable<CancerGroupType>> GetCancerGroupTypesPagedAsync(int pageNumber, int pageSize);
        Task<int> GetCancerGroupTypesCountAsync();
        Task<CancerGroupType> GetCancerGroupTypeAsync(int id);
        Task<IEnumerable<CancerGroupType>> SearchCancerGroupTypesAsync(string desc);
        void Update(CancerGroupType groupType);
        Task<int> GetGroupTypeCountByPrefixAsync(string prefix);
        Task<IEnumerable<CancerGroupType>> GetCancerTypesByGroupAsync(string group);
    }
}