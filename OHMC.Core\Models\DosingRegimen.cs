﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace OHMC.Core.Models
{
    public class DosingRegimen : ModelBase
    {
        [Key]
        public int Id { get; set; }
        [Required, MaxLength(20)]
        public string RegimenCode { get; set; } = string.Empty;
        [Required, MaxLength(500)]
        public string DosingRegimenName { get; set; } = string.Empty;
        [ForeignKey("INN"), DisplayName("INN")]
        public int INNId { get; set; }
        public INN INN { get; set; }
        [MaxLength(10)]
        public string Route { get; set; } = string.Empty;
        public double DoseValue { get; set; }
        public string? DoseDescriptionText { get; set; }
        [ForeignKey("Dose")]
        public int? DoseId { get; set; }
        public Dose? Dose { get; set; }
        [MaxLength(100)]
        public string? DrugDays { get; set; }
        public int NumberOfDaysPatientDosed { get; set; }
        public double? CostPerRegimen { get; set; }
        [MaxLength(500)]
        public string? Comment { get; set; }
        [MaxLength(500)]
        public string? CalculatedRegimenText { get; set; }
        public bool UseAsCalc { get; set; }
        [MaxLength(500)]
        public string? ModifiedCalculatedRegimenText { get; set; }
    }
}
