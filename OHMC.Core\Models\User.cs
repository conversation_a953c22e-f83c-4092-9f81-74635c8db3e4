﻿using System.ComponentModel.DataAnnotations;

namespace OHMC.Core.Models
{
    public class User : ModelBase
    {
        [Key]
        public int Id { get; set; }
        [MaxLength(100), EmailAddress]
        public string Email { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
        [MaxLength(100)]
        public string FullName { get; set; } = string.Empty;
        [MaxLength(100)]
        public string LastName { get; set; } = string.Empty;
        public bool Active { get; set; } = true;
        public byte[]? PictureSource { get; set; }
        [MaxLength(50)]
        public string? Position { get; set; }
        public List<UserRole> UserRoles { get; set; } = new List<UserRole>();
    }
}
