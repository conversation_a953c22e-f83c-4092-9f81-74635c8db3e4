﻿using Microsoft.Extensions.Logging;
using OHMC.Core.Common.Const;
using OHMC.Core.Common.Response;
using OHMC.Core.Models;
using OHMC.DataAccess.Interface;

namespace OHMC.Application
{
    public class TariffBL : ITariffBL
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<TariffBL> _logger;

        public TariffBL(IUnitOfWork unitOfWork, ILogger<TariffBL> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<IEnumerable<Tariff>> GetTariffsAsync()
        {
            return await _unitOfWork.TariffRepository.GetTariffsAsync();
        }

        public async Task<Tariff> GetTariffAsync(int id)
        {
            return await _unitOfWork.TariffRepository.GetTariffAsync(id);
        }

        public async Task<TariffPeriod> GetCurrentTarifPeriodAsync(int tariffId)
        {
            return await _unitOfWork.TariffRepository.GetCurrentTarifPeriodAsync(tariffId);
        }

        public async Task<UpSertTariffResponse> UpSertTariffAsync(Tariff tariff, int userId)
        {
            tariff.UpdatedBy = userId;
            tariff.UpdatedDate = DateTime.Now;

            var tariffExists = await _unitOfWork.TariffRepository.DoTariffsExistByCodeAsync(tariff.Id, tariff.Code);

            if(tariffExists) 
            {
                return new UpSertTariffResponse()
                {
                    ResponseCode = ResponseCodesConst.TARIFF_EXISTS,
                    ResponseMessage = "Tariff Code exists already"
                };
            }

            if (tariff.Id > 0)
            {
                _unitOfWork.TariffRepository.Update(tariff);
                _logger.LogInformation($"{StrConst.LOG_TYPE_UPDATE}: {tariff.ShortDescription}, USERID: {userId}");
            }
            else
            {
                tariff.CreateBy = userId;
                tariff.CreatedDate = DateTime.Now;

                _unitOfWork.TariffRepository.Add(tariff);
                _logger.LogInformation($"{StrConst.LOG_TYPE_ADDING}: {tariff.ShortDescription}, USERID: {userId}");
            }

            var saveResponse = await SaveAsync();

            if(saveResponse > 0)
            {
                return new UpSertTariffResponse()
                {
                    ResponseCode = ResponseCodesConst.TARIFF_UPDATE_SUCCESSFUL,
                    ResponseMessage = "Successful"
                };
            }
            else
            {
                return new UpSertTariffResponse()
                {
                    ResponseCode = ResponseCodesConst.TARIFF_UPDATE_FAILED,
                    ResponseMessage = "Failed"
                };
            }
        }

        public async Task<int> RemoveTariffAsync(int id, int userId)
        {
            var tariff = await _unitOfWork.TariffRepository.GetTariffAsync(id);
            tariff.UpdatedBy = userId;
            tariff.UpdatedDate = DateTime.Now;
            tariff.Deleted = true;
            tariff.DeleteOn = DateTime.Now;
            _unitOfWork.TariffRepository.Update(tariff);
            _logger.LogInformation($"{StrConst.LOG_TYPE_REMOVING}: {tariff.ShortDescription}, USERID: {userId}");
            return await _unitOfWork.SaveAsync();
        }

        public async Task<IEnumerable<TariffPeriod>> GetTariffPeriodAsync()
        {
            return await _unitOfWork.TariffPeriodRepository.GetTariffPeriodsAsync();
        }

        public async Task<TariffPeriod> GetTariffPeriodAsync(int id)
        {
            return await _unitOfWork.TariffPeriodRepository.GetTariffPeriodAsync(id);
        }

        public async Task<List<TariffPeriod>> GetTariffPeriodsByTariffAsync(int tariffId)
        {
            return await _unitOfWork.TariffPeriodRepository.GetTariffPeriodsByTariffAsync(tariffId);
        }

        public async Task<UpSertTariffPeriodResponse> UpSertTariffPeriodAsync(TariffPeriod period, int userId)
        {
            period.UpdatedBy = userId;
            period.UpdatedDate = DateTime.Now;

            //Check for duplicates
            var matchingTariffPeriodsExits = await _unitOfWork.TariffPeriodRepository
                .DoTariffPeriodExistByTariffDatesAsync(period.TariffId, period.StartDate, period.EndDate);
            
            if(matchingTariffPeriodsExits)
            {
                return new UpSertTariffPeriodResponse()
                {
                    ResponseCode = ResponseCodesConst.TARIFFPERIOD_EXISTS,
                    ResponseMessage = "There is a tariff of same type in the same period"
                };
            }

            if (period.Id > 0)
            {
                _unitOfWork.TariffPeriodRepository.Update(period);
                _logger.LogInformation($"{StrConst.LOG_TYPE_UPDATE}: {period.Id}, USERID: {userId}");
            }
            else
            {
                period.CreateBy = userId;
                period.CreatedDate = DateTime.Now;

                _unitOfWork.TariffPeriodRepository.Add(period);
                _logger.LogInformation($"{StrConst.LOG_TYPE_ADDING}: {period.Id}, USERID: {userId}");
            }

            var saveResponse = await SaveAsync();
            if (saveResponse > 0)
            {
                return new UpSertTariffPeriodResponse()
                {
                    ResponseCode = ResponseCodesConst.TARIFFPERIOD_UPDATE_SUCCESSFUL,
                    ResponseMessage = "Successful"
                };
            }
            else
            {
                return new UpSertTariffPeriodResponse()
                {
                    ResponseCode = ResponseCodesConst.TARIFFPERIOD_UPDATE_FALED,
                    ResponseMessage = "Failed To Save"
                };
            }
        }

        public async Task<int> RemoveTariffPeriodAsync(int id, int userId)
        {
            var period = await _unitOfWork.TariffPeriodRepository.GetTariffPeriodAsync(id);
            period.UpdatedBy = userId;
            period.UpdatedDate = DateTime.Now;
            period.Deleted = true;
            period.DeleteOn = DateTime.Now;
            _unitOfWork.TariffPeriodRepository.Update(period);
            _logger.LogInformation($"{StrConst.LOG_TYPE_REMOVING}: {period.Id}, USERID: {userId}");
            return await _unitOfWork.SaveAsync();
        }

        public async Task<int> SaveAsync()
        {
            return await _unitOfWork.SaveAsync();
        }

        public async  Task<IncreaseTariffResponse> IncreaseTariffAsync(int year, decimal percentageIncrease, int? tariffTypeId, int userId)
        {
            var currentYear = DateTime.Now.Year;
            var currentTariffPeriods = await _unitOfWork.TariffPeriodRepository.GetTariffPeriodsByYearAsync(currentYear, tariffTypeId);
            var futureTariffPeriods = await _unitOfWork.TariffPeriodRepository.GetTariffPeriodsByYearAsync(year, tariffTypeId);

            if (futureTariffPeriods.Any())
            {
                _logger.LogError($"There are tariffs already for year {year}");

                return new IncreaseTariffResponse()
                {
                    ResponseCode = ResponseCodesConst.TARIFF_INCREASE_FAILED,
                    ResponseMessage = $"Failed. There are tariffs already for year {year}"
                };
            }

            foreach (var period in currentTariffPeriods)
            {
                _unitOfWork.TariffPeriodRepository.Add(new TariffPeriod()
                {
                    TariffId = period.TariffId,
                    StartDate = new DateTime(year, 1, 1),
                    EndDate = new DateTime(year, 12, 31),
                    IsActive = true,
                    TariffAmount = period.TariffAmount * (percentageIncrease/100 + 1),
                    CreateBy = userId,
                    CreatedDate = DateTime.Now,
                    UpdatedBy = userId,
                    UpdatedDate = DateTime.Now,
                    Deleted = false
                });
            }

            try
            {
                await _unitOfWork.SaveAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex.Message);
            }

            _logger.LogInformation($"New tariffs for year {year} generated with increase of {percentageIncrease}%. USER: {userId}");

            return new IncreaseTariffResponse()
            {
                ResponseCode = ResponseCodesConst.TARIFF_INCREASE_SUCCESSFULL,
                ResponseMessage = $"New tariffs for year {year} generated with increase of {percentageIncrease}%"
            };
        }

        public async Task<UpdateProfessionalFeesToCurrentResponse> UpdateProfessionalFeesAsync()
        {
            var response = await _unitOfWork.TariffRepository.UpdateProfessionalFeesAsync();

            return new UpdateProfessionalFeesToCurrentResponse()
            {
                ResponseCode = response,
                ResponseMessage = "Done"
            };
            
        }            
    }
}
