﻿using System.ComponentModel.DataAnnotations;

namespace OHMC.Core.Models
{
    public class Notification : ModelBase
    {
        [Key]
        public int Id { get; set; }
        [Required, <PERSON><PERSON>ength(200)]
        public string Subject { get; set; } = string.Empty;
        [Required, <PERSON><PERSON>ength(300)]
        public string NotificationAddress { get; set; } = string.Empty;
        [Required]
        public string NotificationBody { get; set; } = string.Empty;
        [Required, MaxLength(200)]
        public string NotificationType { get; set; } = string.Empty;
        [Required, MaxLength(200)]
        public string NotificationStatus { get; set; } = string.Empty;
        public int SendRetryCount { get; set; }
        public bool IsRead { get; set; }
    }
}
