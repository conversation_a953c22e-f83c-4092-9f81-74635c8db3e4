﻿using OHMC.Core.Models;

namespace OHMC.DataAccess.Interface
{
    public interface INDODProductRepository
    {
        Task<NDOHProduct> GetNDOHProductAsync(string nappi9);
        Task<IEnumerable<NDOHProduct>> GetNDOHProductsAsync();
        Task<IEnumerable<NDOHProduct>> GetNDOHProductsPagedAsync(int pageNumber, int pageSize);
        Task<IEnumerable<NDOHProduct>> SearchProductsAsync(string searchString);
    }
}