﻿using OHMC.Core.Models;

namespace OHMC.DataAccess.Interface
{
    public interface INDOHProductsBL
    {
        Task<NDOHProduct> GetNDODProductAsync(string nappi);
        Task<IEnumerable<NDOHProduct>> GetNDODProductsAsync();
        Task<IEnumerable<NDOHProduct>> GetNDODProductsPagedAsync(int pageNumber, int pageSize);
        Task<IEnumerable<NDOHProduct>> SearchProductsAsync(string searchString);
    }
}