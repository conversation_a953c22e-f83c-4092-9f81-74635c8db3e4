﻿using Microsoft.Extensions.Logging;
using OHMC.Core.Common.Const;
using OHMC.Core.Common.Utils;
using OHMC.Core.Models;
using OHMC.DataAccess.Interface;

namespace OHMC.Application
{
    public class CancerGroupsBL : ICancerGroupsBL
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<CancerGroupsBL> _logger;
        public CancerGroupsBL(IUnitOfWork unitOfWork, ILogger<CancerGroupsBL> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<IEnumerable<CancerGroup>> GetAllCancerGroupsAsync()
        {
            return await _unitOfWork.CancerGroupRepository.GetAllCancerGroupsAsync();
        }

        public async Task<CancerGroup> GetCancerGroupAsync(int id)
        {
            return await _unitOfWork.CancerGroupRepository.GetCancerGroupAsync(id);
        }

        public async Task<IEnumerable<CancerGroupType>> GetAllCancerGroupTypesAsync()
        {
            return await _unitOfWork.CancerGroupTypeRepository.GetAllCancerGroupTypesAsync();
        }

        public async Task<IEnumerable<CancerGroup>> GetCancerGroupsByPTCAsync(int ptcTypeId)
        {
            return await _unitOfWork.CancerGroupRepository.GetCancerGroupsByPTCAsync(ptcTypeId);
        }

        public async Task<IEnumerable<CancerGroupType>> GetCancerGroupTypesPagesAsync(int pageNumber, int pageSize) 
        {
            return await _unitOfWork.CancerGroupTypeRepository.GetCancerGroupTypesPagedAsync(pageNumber, pageSize);
        }

        public async Task<int> GetCancerGroupTypesCountAsync()
        {
            return await _unitOfWork.CancerGroupTypeRepository.GetCancerGroupTypesCountAsync();
        }

        public async Task<CancerGroupType> GetCancerGroupTypeAsync(int id)
        {
            return await _unitOfWork.CancerGroupTypeRepository.GetCancerGroupTypeAsync(id);
        }

        public async Task<int> UpSertCancerGroup(CancerGroup group, int userId)
        {
            group.UpdatedBy = userId;
            group.UpdatedDate = DateTime.Now;

            if (group.Id > 0)
            {
                _unitOfWork.CancerGroupRepository.Update(group);
                _logger.LogInformation($"{StrConst.LOG_TYPE_UPDATE}: {group.GroupName}, USERID: {userId}");
            }
            else
            {
                group.CreateBy = userId;
                group.CreatedDate = DateTime.Now;
                group.GroupCode = await GetNextCodeReference(group.GroupName.Substring(0, 1));

                _unitOfWork.CancerGroupRepository.Add(group);
                _logger.LogInformation($"{StrConst.LOG_TYPE_ADDING}: {group.GroupName}, USERID: {userId}");
            }

            return await SaveAsync();
        }

        public async Task<int> UpSertCancerGroupType(CancerGroupType groupType, int userId)
        {
            groupType.UpdatedBy = userId;
            groupType.UpdatedDate = DateTime.Now;

            if (groupType.Id > 0)
            {
                _unitOfWork.CancerGroupTypeRepository.Update(groupType);
                _logger.LogInformation($"{StrConst.LOG_TYPE_UPDATE}: {groupType.CancerType}, USERID: {userId}");
            }
            else
            {
                groupType.CreateBy = userId;
                groupType.CreatedDate = DateTime.Now;
                groupType.Code = await GetNextCodeReference(groupType.GroupName.Substring(0, 1));

                _unitOfWork.CancerGroupTypeRepository.Add(groupType);
                _logger.LogInformation($"{StrConst.LOG_TYPE_ADDING} : {groupType.CancerType}, USERID: {userId}");
            }

            return await SaveAsync();
        }

        public async Task<int> RemoveCancerGroup(int id, int userId)
        {
            var group = await _unitOfWork.CancerGroupRepository.GetCancerGroupAsync(id);
            group.UpdatedBy = userId;
            group.UpdatedDate = DateTime.Now;
            group.Deleted = true;
            group.DeleteOn = DateTime.Now;
            _unitOfWork.CancerGroupRepository.Update(group);
            _logger.LogInformation($"{StrConst.LOG_TYPE_REMOVING}: {group.GroupName}, USERID: {userId}");
            return await _unitOfWork.SaveAsync();
        }

        public async Task<int> SaveAsync()
        {
            return await _unitOfWork.SaveAsync();
        }

        public async Task<int> RemoveCancerGroupType(int id, int userId)
        {
            var groupType = await _unitOfWork.CancerGroupTypeRepository.GetCancerGroupTypeAsync(id);
            groupType.UpdatedBy = userId;
            groupType.UpdatedDate = DateTime.Now;
            groupType.Deleted = true;
            groupType.DeleteOn = DateTime.Now;
            _unitOfWork.CancerGroupTypeRepository.Update(groupType);
            _logger.LogInformation($"{StrConst.LOG_TYPE_REMOVING}: {groupType.GroupName}, USERID: {userId}");
            return await _unitOfWork.SaveAsync();
        }

        public async Task<string> GetNextCodeReference(string prefix)
        {
            return StringHelper.AddDefaultPrefix(prefix, (await _unitOfWork.CancerGroupRepository.GetCancerGroupCountByPrefixAsync(prefix) + 1).ToString(), 5);
        }

        public async Task<string> GetNextCodeReferenceGroupType(string prefix)
        {
            return StringHelper.AddDefaultPrefix(prefix, (await _unitOfWork.CancerGroupTypeRepository.GetGroupTypeCountByPrefixAsync(prefix) + 1).ToString(), 5);
        }

        public Task<IEnumerable<CancerGroupType>> GetCancerTypesByGroupAsync(string group)
        {
            return _unitOfWork.CancerGroupTypeRepository.GetCancerTypesByGroupAsync(group);
        }

        public async Task<IEnumerable<CancerGroup>> GetCancerGroupsByFormularyType(int formularyTypeId)
        {
            return await _unitOfWork.CancerGroupRepository.GetCancerGroupsByFormularyType(formularyTypeId);
        }

        public async Task<IEnumerable<CancerGroupICD10>> GetCancerGroupICD10sByGroup(int cancerGroupId)
        {
            return await _unitOfWork.CancerGroupICD10Repository.GetCancerGroupICD10sByGroup(cancerGroupId); 
        }

        public async Task<CancerGroupICD10> GetCancerGroupICD10Async(int id)
        {
            return await _unitOfWork.CancerGroupICD10Repository.GetCancerGroupICD10Async(id);
        }

        public async Task<int> UpSertCancerGroupICD10(CancerGroupICD10 icd10, int userId)
        {
            icd10.UpdatedBy = userId;
            icd10.UpdatedDate = DateTime.Now;

            if (icd10.Id > 0)
            {
                _unitOfWork.CancerGroupICD10Repository.Update(icd10);
                _logger.LogInformation($"{StrConst.LOG_TYPE_UPDATE}: {icd10.ICD10Code}, USERID: {userId}");
            }
            else
            {
                icd10.CreateBy = userId;
                icd10.CreatedDate = DateTime.Now;

                _unitOfWork.CancerGroupICD10Repository.Add(icd10);
                _logger.LogInformation($"{StrConst.LOG_TYPE_ADDING} : {icd10.ICD10Code}, USERID: {userId}");
            }

            return await SaveAsync();
        }

        public async Task<string> GetCancerGroupICD10sDescription(int cancerGroupId)
        {
            return await _unitOfWork.CancerGroupICD10Repository.GetCancerGroupICD10sDescription(cancerGroupId);
        }

        public async Task<int> RemoveCancerGroupICD10ICD10Async(CancerGroupICD10 icd10)
        {
            _unitOfWork.CancerGroupICD10Repository.Remove(icd10);
            return await SaveAsync();
        }

        public async Task<CancerGroupICD10> GetCancerGroupICD10Async(string icd10Code, int cancerGroupId)
        {
            return await _unitOfWork.CancerGroupICD10Repository.GetCancerGroupICD10Async(icd10Code, cancerGroupId);
        }
    }
}
