﻿using Microsoft.Extensions.Logging;
using OHMC.Core.Common.Const;
using OHMC.Core.Models;
using OHMC.DataAccess.Interface;

namespace OHMC.Application
{
    public class FormularyTypeBL : IFormularyTypeBL
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<FormularyTypeBL> _logger;
        public FormularyTypeBL(IUnitOfWork unitOfWork, ILogger<FormularyTypeBL> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<IEnumerable<FormularyType>> GetFormularyTypesAsync()
        {
            return await _unitOfWork.FormularyTypeRepository.GetFormularyTypesAsync();
        }

        public async Task<FormularyType> GetFormularyType(int id)
        {
            return await _unitOfWork.FormularyTypeRepository.GetFormularyTypeAsync(id);
        }

        public async Task<FormularyType> GetFormularyType(string formularyType)
        {
            return await _unitOfWork.FormularyTypeRepository.GetFormularyTypeAsync(formularyType);
        }

        public async Task<int> UpSert(FormularyType formulary, int userId)
        {
            formulary.UpdatedBy = userId;
            formulary.UpdatedDate = DateTime.Now;

            if (formulary.Id > 0)
            {
                _unitOfWork.FormularyTypeRepository.Update(formulary);
                _logger.LogInformation($"{StrConst.LOG_TYPE_UPDATE}: {formulary.FormularyTypeName}, USERID: {userId}");
            }
            else
            {
                formulary.CreateBy = userId;
                formulary.CreatedDate = DateTime.Now;

                _unitOfWork.FormularyTypeRepository.Add(formulary);
                _logger.LogInformation($"{StrConst.LOG_TYPE_ADDING}: {formulary.FormularyTypeName}, USERID: {userId}");
            }

            return await SaveAsync();
        }

        public async Task<int> SaveAsync()
        {
            return await _unitOfWork.SaveAsync();
        }
    }
}
