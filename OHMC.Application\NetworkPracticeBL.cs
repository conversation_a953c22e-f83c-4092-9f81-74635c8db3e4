﻿using Microsoft.Extensions.Logging;
using OHMC.Core.Common.Const;
using OHMC.Core.Models;
using OHMC.DataAccess.Interface;

namespace OHMC.Application
{
    public class NetworkPracticeBL : INetworkPracticeBL
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<NetworkPracticeBL> _logger;

        public NetworkPracticeBL(IUnitOfWork unitOfWork, ILogger<NetworkPracticeBL> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<IEnumerable<NetworkPractice>> GetNetworkPracticesAsync()
        {
            return await _unitOfWork.NetworkPracticeRepository.GetNetworkPracticesAsync();
        }

        public async Task<NetworkPractice> GetNetworkPracticeAsync(int id)
        {
            return await _unitOfWork.NetworkPracticeRepository.GetNetworkPracticeAsync(id);
        }

        public async Task<NetworkPracticeSpeciality> GetNetworkPracticeSpecialityAsync(int id)
        {
            return await _unitOfWork.NetworkPracticeSpecialityRepository.GetNetworkPracticeSpecialityAsync(id);
        }

        public async Task<string> GetSpecialitiesDescription(int networkMemberId)
        {
            return await _unitOfWork.NetworkMemberSpecialityRepository.GetSpecialitiesDescription(networkMemberId);
        }

        public async Task<IEnumerable<NetworkPracticeSpeciality>> GetNetworkPracticeSpecialitiesAsync(int networkMemberId)
        {
            return await _unitOfWork.NetworkPracticeSpecialityRepository.GetNetworkPracticeSpecialitiesAsync(networkMemberId);
        }

        public async Task<IEnumerable<NetworkMember>> GetNetworkPracticeMembersAsync(int networkPracticeId)
        {
            return await _unitOfWork.NetworkMemberRepository.GetNetworkPracticeMembersAsync(networkPracticeId);
        }

        public async Task<int> UpSert(NetworkPractice practice, int userId)
        {
            practice.UpdatedBy = userId;
            practice.UpdatedDate = DateTime.Now;

            if (practice.Id > 0)
            {
                _unitOfWork.NetworkPracticeRepository.Update(practice);
                _logger.LogInformation($"{StrConst.LOG_TYPE_UPDATE}: {practice.PracticeName}, USERID: {userId}");
            }
            else
            {
                practice.CreateBy = userId;
                practice.CreatedDate = DateTime.Now;

                _unitOfWork.NetworkPracticeRepository.Add(practice);
                _logger.LogInformation($"{StrConst.LOG_TYPE_ADDING}: {practice.PracticeName}, USERID: {userId}");
            }

            return await SaveAsync();
        }

        public async Task<int> UpSertSpeciality(NetworkPracticeSpeciality speciality, int userId)
        {
            speciality.UpdatedBy = userId;
            speciality.UpdatedDate = DateTime.Now;

            if (speciality.Id > 0)
            {
                _unitOfWork.NetworkPracticeSpecialityRepository.Update(speciality);
                _logger.LogInformation($"{StrConst.LOG_TYPE_UPDATE}: {speciality.Speciality}, USERID: {userId}");
            }
            else
            {
                speciality.CreateBy = userId;
                speciality.CreatedDate = DateTime.Now;

                _unitOfWork.NetworkPracticeSpecialityRepository.Add(speciality);
                _logger.LogInformation($"{StrConst.LOG_TYPE_ADDING}: {speciality.Speciality}, USERID: {userId}");
            }

            return await SaveAsync();
        }

        public async Task<int> SaveAsync()
        {
            return await _unitOfWork.SaveAsync();
        }
    }
}
