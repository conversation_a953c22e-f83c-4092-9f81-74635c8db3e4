﻿using System.ComponentModel.DataAnnotations;

namespace OHMC.Core.Models.MediKredit
{
    public class ProductFileHeader : BaseMediKreditRecord
    {
        [StringLength(30)]
        public string FileName { get; set; } = string.Empty;
        [StringLength(4)]
        public string NappiFileVersion { get; set; } = string.Empty;
        [StringLength(3)]
        public string RunNumber { get; set; } = string.Empty;
        [StringLength(8)]
        public string RunDate { get; set; } = string.Empty;
        [StringLength(6)]
        public string RunTime { get; set; } = string.Empty;
        [StringLength(181)]
        public string Filler { get; set; } = string.Empty;
        [StringLength(8)]
        public string RecordCreationDate { get; set; } = string.Empty;
        [StringLength(6)]
        public string RecordCreationTime { get; set; } = string.Empty;

        public void ProcessFileHeaderLine(string line)
        {

        }
    }
}
