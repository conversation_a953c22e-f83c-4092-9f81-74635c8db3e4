﻿using OHMC.Core.Models;

namespace OHMC.DataAccess.Interface
{
    public interface IProtocolTreatmentIntentRepository
    {
        void Add(ProtocolTreatmentIntent protocolTreatmentIntent);
        Task<ProtocolTreatmentIntent> GetProtocolTreatmentIntentAsync(int id);
        Task<IEnumerable<ProtocolTreatmentIntent>> GetProtocolTreatmentIntentsAsync(int protocolId);
        void Update(ProtocolTreatmentIntent protocolTreatmentIntent);
    }
}