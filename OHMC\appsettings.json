{"ReportServerSettings": {"ReportServerUrl": "http://*************/Reportserver", "ReportServerPath": "/OHMC_Protocols/"}, "ConnectionStrings": {"DefaultConnection": "Server=*************;Database=OHMC_Protocols;Trusted_Connection=true"}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}, {"Name": "File", "Args": {"path": "C:\\logs\\OHMCProtocolsAPP\\log.txt", "rollingInterval": "Day"}}], "Enrich": ["FromLogContext"], "Properties": {"Application": "OHMC"}}, "AllowedHosts": "*"}