﻿using FoolProof.Core;
using Newtonsoft.Json;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace OHMC.Core.Models
{
    public class Protocol : ModelBase
    {
        [Key]
        public int Id { get; set; }
        [Required, MaxLength(20)]
        public string Code { get; set; } = string.Empty;
        [Required, MaxLength(500), DisplayName("Protocol Name")]
        public string ProtocolName { get; set; } = string.Empty;
        [ForeignKey("Regimen"), DisplayName("Regimen")]
        public int RegimenId { get; set; }
        public Regimen Regimen { get; set; }
        [Required, MaxLength(100), Display<PERSON><PERSON>("Cancer Group")]
        public string CancerGroupName { get; set; } = string.Empty;
        [ForeignKey("CancerGroup"), DisplayName("CancerGroup")]
        public int? CancerGroupId { get; set; }
        public CancerGroup? CancerGroup { get; set; }
        [MaxLength(200), DisplayName("Cancer Type")]
        public string? CancerType { get; set; } = string.Empty;
        [ForeignKey("Modality"), Display<PERSON><PERSON>("Modality")]
        public int? CancerTypeId { get; set; }
        public Modality? Modality { get; set; }
        [MaxLength(20), DisplayName("OM Code")]
        public string OMCode { get; set; } = string.Empty;
        [MaxLength(200)]
        public string? EndComment { get; set; } = string.Empty;
        public bool IsProtocolSupportedByHTA { get; set; }
        [MaxLength(50)]
        public string? HTANumber { get; set; }
        #region PracticeParameters
        [MaxLength(20)]
        public string? LevelOfCare { get; set; }    
        public bool Restriction {  get; set; }
        [MaxLength(200), DisplayName("Restriction")]
        [RequiredIf("Restriction", Operator.EqualTo, true, ErrorMessage = "Restriction text required.")]
        public string? RestrictionDescription { get; set; }
        public int? RestrictionLevel { get; set; }

        [MaxLength(50), DisplayName("Treatment Intent")]
        public string? TreatmentIntent { get; set; }
        #endregion

        #region Regulatory
        [MaxLength(10), DisplayName("PMB")]
        public string? IsPMB { get; set; }
        [MaxLength(20), DisplayName("PMB Number")]
        [RequiredIf("IsPMB", Operator.EqualTo, "YES", ErrorMessage = "PMB Number Required.")]
        public string? PMBNumber { get; set; }
        [DisplayName("PMB Issue Date")]
        public DateTime? PMBIssueDate { get; set; }
        public bool IsEML { get; set; }
        [MaxLength(200)]
        public string? EMLRestriction { get; set; } = string.Empty;
        [MaxLength(10)]
        public string? IsTender { get; set; }
        [MaxLength(20), DisplayName("Tender Number")]
        [RequiredIf("IsTender", Operator.EqualTo, "YES", ErrorMessage = "Tender number required.")]
        public string? TenderNumber { get; set; }
        [DisplayName("Tender Start Date")]
        public DateTime? TenderStartDate { get; set; }
        [DisplayName("Tender End Date")]
        public DateTime? TenderEndDate { get; set; }
        #endregion

        #region Cost
        public double? TotalCostPerCycle { get; set; }
        public double? TotalCostPerCourse { get; set; }
        [MaxLength(50)]
        public string? ReviewIndicator { get; set; }
        [MaxLength(200)]
        public string? ReviewIndicatorDescription { get; set; }
        public DateTime? ReviewStartDate { get; set; }
        public DateTime? ReviewEndDate { get; set; }
        #endregion
        [MaxLength(20)]
        public string? XCode { get; set; }
        [MaxLength(200)]
        public string? Chapter { get; set; }
        [ForeignKey("Chapter"), DisplayName("Chapter")]
        public int? ChapterId { get; set; }
        public Chapter? BookChapter { get; set; }
        [MaxLength(200), DisplayName("Treatment Option")]
        public string? TreatmentOption { get; set; }
        [ForeignKey("CancerGroupType")]
        public int? TreatmentOptionId { get; set; }
        public CancerGroupType CancerGroupType { get; set; }

        [ForeignKey("FormularyType")]
        public int? FormularyTypeId { get; set; }
        public FormularyType? FormularyType { get; set; }
        [MaxLength(100)]
        public string? Status { get; set; } = string.Empty;
        public List<ProtocolStatus>? ProtocolStatuses { get; set; }
        public int? PrintOrder { get; set; }
        [MaxLength(20)]
        public string? ICD10Code { get; set; }
        [ForeignKey("PTC"), DisplayName("PTC")]
        public int? PTCTypeId { get; set; }
        public PTCType? PTCType { get; set; }

        [ForeignKey("ProtocolHeader"), DisplayName("Protocol Header")]
        public int? ProtocolHeaderId { get; set; }
        public ProtocolHeader? ProtocolHeader { get; set; }
    }
}
