﻿using OHMC.Core.Models;

namespace OHMC.DataAccess.Interface
{
    public interface ITariffPeriodRepository
    {
        void Add(TariffPeriod period);
        Task<TariffPeriod> GetTariffPeriodAsync(int id);
        Task<bool> DoTariffPeriodExistByTariffDatesAsync(int tariffId, DateTime startDate, DateTime endDate);
        Task<IEnumerable<TariffPeriod>> GetTariffPeriodsAsync();
        Task<List<TariffPeriod>> GetTariffPeriodsByTariffAsync(int tariffId);
        void Update(TariffPeriod period);
        Task<List<TariffPeriod>> GetTariffPeriodsByYearAsync(int year, int? tariffTypeId);
    }
}