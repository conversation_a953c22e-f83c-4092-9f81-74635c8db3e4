﻿using Microsoft.Extensions.Logging;
using OHMC.Core.Common.Const;
using OHMC.Core.Models;
using OHMC.DataAccess.Interface;

namespace OHMC.Application
{
    public class RoutesBL : IRoutesBL
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<RoutesBL> _logger;

        public RoutesBL(IUnitOfWork unitOfWork, ILogger<RoutesBL> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<IEnumerable<Route>> GetRoutes()
        {
            return await _unitOfWork.RouteRepository.GetAllRoutesAsync();
        }

        public async Task<Route> GetRouteAsync(int id)
        {
            return await _unitOfWork.RouteRepository.GetRouteAsync(id);
        }

        public async Task<int> UpSert(Route route, int userId)
        {
            route.UpdatedBy = userId;
            route.UpdatedDate = DateTime.Now;

            if (route.Id > 0)
            {
                _unitOfWork.RouteRepository.Update(route);
                _logger.LogInformation($"{StrConst.LOG_TYPE_UPDATE}: {route.RouteName}, USERID: {userId}");
            }
            else
            {
                route.CreateBy = userId;
                route.CreatedDate = DateTime.Now;

                _unitOfWork.RouteRepository.Add(route);
                _logger.LogInformation($"{StrConst.LOG_TYPE_ADDING}: {route.RouteName}, USERID: {userId}");
            }

            return await SaveAsync();
        }

        public async Task<int> RemoveRoute(int id, int userId)
        {
            var route = await _unitOfWork.RouteRepository.GetRouteAsync(id);
            route.UpdatedBy = userId;
            route.UpdatedDate = DateTime.Now;
            route.Deleted = true;
            route.DeleteOn = DateTime.Now;
            _unitOfWork.RouteRepository.Update(route);
            _logger.LogInformation($"{StrConst.LOG_TYPE_REMOVING}: {route.RouteName}, USERID: {userId}");
            return await _unitOfWork.SaveAsync();
        }

        public async Task<int> SaveAsync()
        {
            return await _unitOfWork.SaveAsync();
        }
    }
}
