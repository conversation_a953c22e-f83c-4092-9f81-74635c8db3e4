﻿using OHMC.Core.Models;

namespace OHMC.DataAccess.Interface
{
    public interface IMedicalSchemeBenefitPlanRepository
    {
        void Add(MedicalSchemeBenefitPlan plan);
        Task<MedicalSchemeBenefitPlan> GetMedicalSchemeBenefitPlanAsync(int id);
        Task<IEnumerable<MedicalSchemeBenefitPlan>> GetMedicalSchemeBenefitPlansAsync();
        Task<IEnumerable<MedicalSchemeBenefitPlan>> GetMedicalSchemeBenefitPlansAsync(int medicalSchemeId);
        void Update(MedicalSchemeBenefitPlan plan);
    }
}