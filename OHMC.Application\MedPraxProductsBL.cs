﻿using Microsoft.Extensions.Logging;
using OHMC.Core.Models;
using OHMC.DataAccess.Interface;

namespace OHMC.Application
{
    public class MedPraxProductsBL : IMedPraxProductsBL
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<MedPraxProductsBL> _logger;

        public MedPraxProductsBL(IUnitOfWork unitOfWork, ILogger<MedPraxProductsBL> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<IEnumerable<MedPraxProduct>> GetMedPraxProductsByInnAsync(string inn)
        {
            return await _unitOfWork.MedPraxProductRepository.GetMedPraxProductsByInnAsync(inn);
        }        

        public async Task<IEnumerable<MedPraxProduct>> GetMedPraxProductsByInnStrengthUnitAsync(string inn, string strength, string? inn2 = null, string? strength2=null)
        {
            return await _unitOfWork.MedPraxProductRepository.GetMedPraxProductsByInnStrengthAsync(inn, strength, inn2, strength2);
        }

        public async Task<MedPraxProduct> GetMedPraxProductByAsync(string nappi)
        {
            return await _unitOfWork.MedPraxProductRepository.GetMedPraxProductAsync(nappi);
        }

        public async Task<IEnumerable<MedPraxProduct>> SearchMedPraxProductsByProductNameAsync(string searchProductName)
        {
            return await _unitOfWork.MedPraxProductRepository.SearchMedPraxProductsByProductNameAsync(searchProductName);
        }
    }
}
