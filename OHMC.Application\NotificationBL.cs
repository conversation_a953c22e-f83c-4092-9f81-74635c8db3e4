﻿using Microsoft.Extensions.Logging;
using OHMC.Core.Common.Const;
using OHMC.Core.Models;
using OHMC.DataAccess.Interface;

namespace OHMC.Application
{
    public class NotificationBL : INotificationBL
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<NotificationBL> _logger;
        public NotificationBL(IUnitOfWork unitOfWork, ILogger<NotificationBL> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<Notification> GetNotificationAsync(int id)
        {
            return await _unitOfWork.NotificationRepository.GetNotificationAsync(id);
        }

        public async Task<IEnumerable<Notification>> GetNotificationsAsync()
        {
            return await _unitOfWork.NotificationRepository.GetNotificationsAsync();
        }

        public async Task<IEnumerable<Notification>> GetNotificationsByTypeAsync(string notificationType)
        {
            return await _unitOfWork.NotificationRepository.GetNotificationsByTypeAsync(notificationType);
        }

        public async Task<int> AddNotificationAsync(Notification notification, int userId)
        {
            notification.UpdatedBy = userId;
            notification.UpdatedDate = DateTime.Now;
            notification.CreateBy = userId;
            notification.CreatedDate = DateTime.Now;
            notification.Deleted = false;
            _unitOfWork.NotificationRepository.Add(notification);
            _logger.LogInformation($"{StrConst.LOG_TYPE_ADDING}: {notification.Subject}, USERID: {userId}");
            return await SaveAsync();
        }

        public void Update(Notification notification) 
        {
            _unitOfWork.NotificationRepository.Update(notification);            
        }

        public async Task<int> SaveAsync()
        {
            return await _unitOfWork.SaveAsync();
        }
    }
}
