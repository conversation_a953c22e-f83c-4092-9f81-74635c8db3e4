﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace OHMC.DataAccess.Migrations
{
    public partial class NetworkMembers_IDNumberSpecialityProvinceHPCSANumber : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "IDNumber",
                table: "NetworkMembers",
                type: "nvarchar(20)",
                maxLength: 20,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Initials",
                table: "NetworkMembers",
                type: "nvarchar(10)",
                maxLength: 10,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Province",
                table: "NetworkMembers",
                type: "nvarchar(20)",
                maxLength: 20,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Speciality",
                table: "NetworkMembers",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IDNumber",
                table: "NetworkMembers");

            migrationBuilder.DropColumn(
                name: "Initials",
                table: "NetworkMembers");

            migrationBuilder.DropColumn(
                name: "Province",
                table: "NetworkMembers");

            migrationBuilder.DropColumn(
                name: "Speciality",
                table: "NetworkMembers");
        }
    }
}
