﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace OHMC.DataAccess.Migrations
{
    public partial class MedicineAvailability_AddSupplierId_OutOfStockInternationalSignalId_RemedyId : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "InternationalSignals",
                table: "MedicineAvailabilities");

            migrationBuilder.DropColumn(
                name: "Remedy",
                table: "MedicineAvailabilities");

            migrationBuilder.DropColumn(
                name: "Supplier",
                table: "MedicineAvailabilities");

            migrationBuilder.AddColumn<int>(
                name: "OutOfStockInternationalSignalId",
                table: "MedicineAvailabilities",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "RemedyId",
                table: "MedicineAvailabilities",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "SupplierId",
                table: "MedicineAvailabilities",
                type: "int",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_MedicineAvailabilities_OutOfStockInternationalSignalId",
                table: "MedicineAvailabilities",
                column: "OutOfStockInternationalSignalId");

            migrationBuilder.CreateIndex(
                name: "IX_MedicineAvailabilities_RemedyId",
                table: "MedicineAvailabilities",
                column: "RemedyId");

            migrationBuilder.CreateIndex(
                name: "IX_MedicineAvailabilities_SupplierId",
                table: "MedicineAvailabilities",
                column: "SupplierId");

            migrationBuilder.AddForeignKey(
                name: "FK_MedicineAvailabilities_OutOfStockInternationalSignals_OutOfStockInternationalSignalId",
                table: "MedicineAvailabilities",
                column: "OutOfStockInternationalSignalId",
                principalTable: "OutOfStockInternationalSignals",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_MedicineAvailabilities_OutOfStockRemedies_RemedyId",
                table: "MedicineAvailabilities",
                column: "RemedyId",
                principalTable: "OutOfStockRemedies",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_MedicineAvailabilities_Suppliers_SupplierId",
                table: "MedicineAvailabilities",
                column: "SupplierId",
                principalTable: "Suppliers",
                principalColumn: "Id");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_MedicineAvailabilities_OutOfStockInternationalSignals_OutOfStockInternationalSignalId",
                table: "MedicineAvailabilities");

            migrationBuilder.DropForeignKey(
                name: "FK_MedicineAvailabilities_OutOfStockRemedies_RemedyId",
                table: "MedicineAvailabilities");

            migrationBuilder.DropForeignKey(
                name: "FK_MedicineAvailabilities_Suppliers_SupplierId",
                table: "MedicineAvailabilities");

            migrationBuilder.DropIndex(
                name: "IX_MedicineAvailabilities_OutOfStockInternationalSignalId",
                table: "MedicineAvailabilities");

            migrationBuilder.DropIndex(
                name: "IX_MedicineAvailabilities_RemedyId",
                table: "MedicineAvailabilities");

            migrationBuilder.DropIndex(
                name: "IX_MedicineAvailabilities_SupplierId",
                table: "MedicineAvailabilities");

            migrationBuilder.DropColumn(
                name: "OutOfStockInternationalSignalId",
                table: "MedicineAvailabilities");

            migrationBuilder.DropColumn(
                name: "RemedyId",
                table: "MedicineAvailabilities");

            migrationBuilder.DropColumn(
                name: "SupplierId",
                table: "MedicineAvailabilities");

            migrationBuilder.AddColumn<string>(
                name: "InternationalSignals",
                table: "MedicineAvailabilities",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Remedy",
                table: "MedicineAvailabilities",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Supplier",
                table: "MedicineAvailabilities",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);
        }
    }
}
