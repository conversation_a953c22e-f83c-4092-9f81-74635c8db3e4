﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace OHMC.Core.Models
{
    public class ProductSEPOverride : ModelBase
    {
        [Key]
        public int Id { get; set; }
        [ForeignKey("Product")]
        public int ProductId { get; set; }
        public Product Product { get; set; }
        public decimal OriginalSEPExclVat { get; set; } = decimal.Zero;
        public decimal NewSEPExclVat { get; set; } = decimal.Zero;
        [MaxLength(200)]
        public string? Reason { get; set; }
    }
}
