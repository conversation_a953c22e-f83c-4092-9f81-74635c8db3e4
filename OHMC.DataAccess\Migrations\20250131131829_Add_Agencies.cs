﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace OHMC.DataAccess.Migrations
{
    public partial class Add_Agencies : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Agency",
                table: "MedicineAvailabilities");

            migrationBuilder.AddColumn<int>(
                name: "AgencyId",
                table: "MedicineAvailabilities",
                type: "int",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "Agencies",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    AgencyName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreateBy = table.Column<int>(type: "int", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedBy = table.Column<int>(type: "int", nullable: false),
                    Deleted = table.Column<bool>(type: "bit", nullable: false),
                    DeleteOn = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Agencies", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_MedicineAvailabilities_AgencyId",
                table: "MedicineAvailabilities",
                column: "AgencyId");

            migrationBuilder.AddForeignKey(
                name: "FK_MedicineAvailabilities_Agencies_AgencyId",
                table: "MedicineAvailabilities",
                column: "AgencyId",
                principalTable: "Agencies",
                principalColumn: "Id");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_MedicineAvailabilities_Agencies_AgencyId",
                table: "MedicineAvailabilities");

            migrationBuilder.DropTable(
                name: "Agencies");

            migrationBuilder.DropIndex(
                name: "IX_MedicineAvailabilities_AgencyId",
                table: "MedicineAvailabilities");

            migrationBuilder.DropColumn(
                name: "AgencyId",
                table: "MedicineAvailabilities");

            migrationBuilder.AddColumn<string>(
                name: "Agency",
                table: "MedicineAvailabilities",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true);
        }
    }
}
