﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace OHMC.Core.Models
{
    public class Tariff : ModelBase
    {
        [Key]
        public int Id { get; set; }
        [Required, <PERSON><PERSON><PERSON><PERSON>(20)]
        public string Code { get; set; } = string.Empty;
        [Required, Display<PERSON><PERSON>("Short Description")]
        public string ShortDescription { get; set; } = string.Empty;
        [DisplayName("Long Description")]
        public string? LongDescription { get; set; } = string.Empty;

        [ForeignKey("TariffType")]
        public int TariffTypeId { get; set; }
        public TariffType TariffType { get; set; }

        public string? Notes { get; set; } = string.Empty;
    }
}
