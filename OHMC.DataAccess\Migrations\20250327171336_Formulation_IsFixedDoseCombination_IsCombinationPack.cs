﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace OHMC.DataAccess.Migrations
{
    public partial class Formulation_IsFixedDoseCombination_IsCombinationPack : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "IsCombinationPack",
                table: "FormulationsAudit",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsFixedDoseCombination",
                table: "FormulationsAudit",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsCombinationPack",
                table: "Formulations",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsFixedDoseCombination",
                table: "Formulations",
                type: "bit",
                nullable: false,
                defaultValue: false);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsCombinationPack",
                table: "FormulationsAudit");

            migrationBuilder.DropColumn(
                name: "IsFixedDoseCombination",
                table: "FormulationsAudit");

            migrationBuilder.DropColumn(
                name: "IsCombinationPack",
                table: "Formulations");

            migrationBuilder.DropColumn(
                name: "IsFixedDoseCombination",
                table: "Formulations");
        }
    }
}
