﻿using Microsoft.Extensions.Logging;
using OHMC.Core.Common.Const;
using OHMC.Core.Models;
using OHMC.DataAccess.Interface;

namespace OHMC.Application
{
    public class PhaseTypeBL : IPhaseTypeBL
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<PhaseTypeBL> _logger;
        public PhaseTypeBL(IUnitOfWork unitOfWork, ILogger<PhaseTypeBL> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<IEnumerable<PhaseType>> GetPhaseTypesAsync()
        {
            return await _unitOfWork.PhaseTypeRepository.GetPhaseTypesAsync();
        }

        public async Task<PhaseType> GetPhaseType(int id)
        {
            return await _unitOfWork.PhaseTypeRepository.GetPhaseTypeAsync(id);
        }

        public async Task<int> UpSert(PhaseType phase, int userId)
        {
            phase.UpdatedBy = userId;
            phase.UpdatedDate = DateTime.Now;

            if (phase.Id > 0)
            {
                _unitOfWork.PhaseTypeRepository.Update(phase);
                _logger.LogInformation($"{StrConst.LOG_TYPE_UPDATE}: {phase.PhaseName}, USERID: {userId}");
            }
            else
            {
                phase.CreateBy = userId;
                phase.CreatedDate = DateTime.Now;

                _unitOfWork.PhaseTypeRepository.Add(phase);
                _logger.LogInformation($"{StrConst.LOG_TYPE_ADDING}: {phase.PhaseName}, USERID: {userId}");
            }

            return await SaveAsync();
        }

        public async Task<int> SaveAsync()
        {
            return await _unitOfWork.SaveAsync();
        }
    }
}
