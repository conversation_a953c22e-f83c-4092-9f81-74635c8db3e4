﻿using OHMC.Core.DTOs;
using OHMC.DataAccess.Interface;

namespace OHMC.Application
{
    public class QueryReportsBL : IQueryReportsBL
    {
        private readonly IQueryRepository _queryRepository;

        public QueryReportsBL(IQueryRepository queryRepository)
        {
            _queryRepository = queryRepository;
        }

        public async Task<List<Rpt_Product_NewSEPsDTO>> GetProductNewSEPs()
        {
            return await _queryRepository.GetProductNewSEPs();
        }

        public async Task<List<Rpt_ProductOutOfStockDTO>> GetProductOutOfStocks()
        {
            return await _queryRepository.GetProductOutOfStocks();
        }
    }
}
