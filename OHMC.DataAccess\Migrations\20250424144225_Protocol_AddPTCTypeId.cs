﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace OHMC.DataAccess.Migrations
{
    public partial class Protocol_AddPTCTypeId : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "PTCTypeId",
                table: "Protocols",
                type: "int",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_Protocols_PTCTypeId",
                table: "Protocols",
                column: "PTCTypeId");

            migrationBuilder.AddForeignKey(
                name: "FK_Protocols_PTCTypes_PTCTypeId",
                table: "Protocols",
                column: "PTCTypeId",
                principalTable: "PTCTypes",
                principalColumn: "Id");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Protocols_PTCTypes_PTCTypeId",
                table: "Protocols");

            migrationBuilder.DropIndex(
                name: "IX_Protocols_PTCTypeId",
                table: "Protocols");

            migrationBuilder.DropColumn(
                name: "PTCTypeId",
                table: "Protocols");
        }
    }
}
