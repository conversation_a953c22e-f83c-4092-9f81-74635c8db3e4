﻿using OHMC.Core.Models;

namespace OHMC.DataAccess.Interface
{
    public interface IProtocolRepository
    {
        void Add(Protocol protocol);
        Task<int> GetNextProtocolCodeReference(string prefix);
        Task<Protocol> GetProtocolAsync(int id);
        Task<Protocol> GetProtocolFullAsync(int id);
        Task<int> GetProtocolCountAsync();
        Task<IEnumerable<Protocol>> GetProtocolsAsync();
        Task<IEnumerable<Protocol>> GetPagedProtocolsAsync(int pageNumber, int pageSize);
        Task<IEnumerable<Protocol>> GetPagedProtocolItemsAsync(int pageNumber, int pageSize);
        Task<IEnumerable<Protocol>> SearchProtocolsAsync(string desc);
        Task<IEnumerable<Protocol>> SearchProtocolsByCancerTypeGroupAsync(string cancerType, string cancerGroup);
        void Update(Protocol protocol);
        Task<IEnumerable<Protocol>> SearchProtocolByStatusAsync(string desc);
        Task<IEnumerable<Protocol>> SearchProtocolsByChapterAsync(string chapter);
        Task<IEnumerable<Protocol>> SearchProtocolsByCancerGroupAsync(string cancerGroup);
        Task<IEnumerable<Protocol>> SearchProtocolsByCancerTypeAsync(string cancerType);
        Task<IEnumerable<ProtocolAudit>> GetProtocolAuditHistoryAsync(int protocolId);
        Task<IEnumerable<Protocol>> GetProtocolPartsAsync(int protocolHeaderId);
    }
}