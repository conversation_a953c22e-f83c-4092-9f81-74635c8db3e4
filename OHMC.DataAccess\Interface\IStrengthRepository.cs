﻿using OHMC.Core.Models;

namespace OHMC.DataAccess.Interface
{
    public interface IStrengthRepository
    {
        void Add(Strength strength);
        Task<IEnumerable<Strength>> GetAllStrengthsAsync();
        Task<Strength> GetStrengthAsync(int id);
        Task<int> GetCountStartingWithPrefix(string prefix);
        Task<IEnumerable<Strength>> SearchStrengthAsync(string desc);
        void Update(Strength strength);
    }
}