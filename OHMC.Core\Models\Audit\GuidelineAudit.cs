﻿using System.ComponentModel.DataAnnotations;

namespace OHMC.Core.Models.Audit
{
    public class GuidelineAudit : BaseAudit
    {
        [Key]
        public int GuidelineAuditId { get; set; }
        public int GuidelineId { get; set; }
        [MaxLength(200)]
        public string GuidelineName { get; set; } = string.Empty;
        [MaxLength(250)]
        public string? GuidelineDisplayName{ get; set; }
        public int OrganisationId { get; set; }
        public int YearLastReviewed { get; set; }
        [MaxLength(300)]
        public string? Comment { get; set; } = string.Empty;
        [MaxLength(200)]
        public string? Version { get; set; } = string.Empty;
        public string? ReferenceEvidence { get; set; }
    }
}
