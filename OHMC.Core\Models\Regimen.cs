﻿using System.ComponentModel.DataAnnotations;

namespace OHMC.Core.Models
{
    public class Regimen : ModelBase
    {
        [Key]
        public int Id { get; set; }

        [Required, MaxLength(20)]
        public string Code { get; set; } = string.Empty;

        [Required, MaxLength(200)]
        public string RegimenName { get; set; } = string.Empty;
        [MaxLength(20)]
        public string? RegimenAdministeredTerm { get; set; } = string.Empty;
        public int? RegimenAdministeredNumber { get; set; }
        [MaxLength(20)]
        public string? RegimenAdministeredUnits { get; set; } = string.Empty;
        [MaxLength(20)]
        public string? RegimenAdministeredCycles { get; set; } = string.Empty;
        public int? NumberOfCycles { get; set; }
        public int? RegimenAdministeredCyclesFromValue { get; set; }
        public int? RegimenAdministeredCyclesToValue { get; set; }
        [MaxLength(200)]
        public string? CycleInstruction { get; set; } = string.Empty;
        [MaxLength(500)]
        public string? RegimenTail { get; set; } = string.Empty;
        [MaxLength(600)]
        public string? RegimenLongDescription { get; set; }
        public bool IsClass { get; set; } = false;
        [MaxLength(100)]
        public string? Status { get; set; } = string.Empty;
        public bool IsUntilProgression { get; set; }
        public List<RegimenDosingRegimen>? RegimenDosingRegimens { get; set; }
        public List<RegimenStatus>? RegimenStatuses { get; set; }
        public List<RegimenCost>? RegimenCosts { get; set; } 
    }
}
