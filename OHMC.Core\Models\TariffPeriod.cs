﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace OHMC.Core.Models
{
    public class TariffPeriod : ModelBase
    {
        [Key]
        public int Id { get; set; }
        [ForeignKey("Tariff")]
        public int TariffId { get; set; }
        public Tariff Tariff { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public bool IsActive { get; set; }
        public decimal TariffAmount { get; set; }
    }
}
