﻿using Microsoft.Extensions.Logging;
using OHMC.Core.Common.Const;
using OHMC.Core.Models;
using OHMC.DataAccess.Interface;

namespace OHMC.Application
{
    public class OutcomeTypeBL : IOutcomeTypeBL
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<OutcomeTypeBL> _logger;
        public OutcomeTypeBL(IUnitOfWork unitOfWork, ILogger<OutcomeTypeBL> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<IEnumerable<OutcomeType>> GetOutcomeTypesAsync()
        {
            return await _unitOfWork.OutcomeTypeRepository.GetOutcomeTypesAsync();
        }

        public async Task<OutcomeType> GetOutcomeType(int id)
        {
            return await _unitOfWork.OutcomeTypeRepository.GetOutcomeTypeAsync(id);
        }

        public async Task<int> UpSert(OutcomeType outcomeType, int userId)
        {
            outcomeType.UpdatedBy = userId;
            outcomeType.UpdatedDate = DateTime.Now;

            if (outcomeType.Id > 0)
            {
                _unitOfWork.OutcomeTypeRepository.Update(outcomeType);
                _logger.LogInformation($"{StrConst.LOG_TYPE_UPDATE}: {outcomeType.OutcomeName}, USERID: {userId}");
            }
            else
            {
                outcomeType.CreateBy = userId;
                outcomeType.CreatedDate = DateTime.Now;

                _unitOfWork.OutcomeTypeRepository.Add(outcomeType);
                _logger.LogInformation($"{StrConst.LOG_TYPE_ADDING}: {outcomeType.OutcomeName}, USERID: {userId}");
            }

            return await SaveAsync();
        }

        public async Task<int> SaveAsync()
        {
            return await _unitOfWork.SaveAsync();
        }
    }
}
