﻿using OHMC.Core.Models;

namespace OHMC.DataAccess.Interface
{
    public interface IUserServiceRepository
    {
        Task ChangePassword(int userId, string password);
        Task<User> Create(User user);
        Task EditRoles(int userId, int[] roles);
        Task<User> GetUser(string login, string password);
        Task<User> GetUser(string login);
        Task<User> GetUser(int id);
        IQueryable<User> GetUsersWithRoles();
        Task<User> GetUserWithRoleAsync(int id);
        Task<int> Update(User user);
        Task<List<User>> GetAllUsers();
        Task<List<User>> GetUsers();
        Task LogUserAsync(string v, User user);
        Task<List<Role>> GetRoles();
        Task<int> AddUserRoleAsync(int userId, int newRoleId);
        Task<User> GetUserEmailAsync(string email);
        Task<UserRole> GetUserRoleAsync(int userRoleId);
        Task<int> DeleleteUserRoleAsync(int userRoleId);

        Task<string> GetUserName(int id);
        Task<string> GetInitialSurnname(int id);
    }
}