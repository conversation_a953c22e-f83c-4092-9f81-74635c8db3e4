﻿using Microsoft.Extensions.Logging;
using OHMC.Core.Common.Const;
using OHMC.Core.Models;
using OHMC.DataAccess.Interface;

namespace OHMC.Application
{
    public class TariffTypeBL : ITariffTypeBL
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<TariffTypeBL> _logger;

        public TariffTypeBL(IUnitOfWork unitOfWork, ILogger<TariffTypeBL> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<IEnumerable<TariffType>> GeTariffTypesAsync()
        {
            return await _unitOfWork.TariffTypeRepository.GetTariffTypesAsync();
        }

        public async Task<TariffType> GetTariffTypeAsync(int id)
        {
            return await _unitOfWork.TariffTypeRepository.GetTariffTypeAsync(id);
        }

        public async Task<int> UpSert(TariffType tariffType, int userId)
        {
            tariffType.UpdatedBy = userId;
            tariffType.UpdatedDate = DateTime.Now;

            if (tariffType.Id > 0)
            {
                _unitOfWork.TariffTypeRepository.Update(tariffType);
                _logger.LogInformation($"{StrConst.LOG_TYPE_UPDATE}: {tariffType.TypeDescription}, USERID: {userId}");
            }
            else
            {
                tariffType.CreateBy = userId;
                tariffType.CreatedDate = DateTime.Now;

                _unitOfWork.TariffTypeRepository.Add(tariffType);
                _logger.LogInformation($"{StrConst.LOG_TYPE_ADDING}: {tariffType.TypeDescription}, USERID: {userId}");
            }

            return await SaveAsync();
        }

        public async Task<int> RemoveTariffType(int id, int userId)
        {
            var tariffType = await _unitOfWork.TariffTypeRepository.GetTariffTypeAsync(id);
            tariffType.UpdatedBy = userId;
            tariffType.UpdatedDate = DateTime.Now;
            tariffType.Deleted = true;
            tariffType.DeleteOn = DateTime.Now;
            _unitOfWork.TariffTypeRepository.Update(tariffType);
            _logger.LogInformation($"{StrConst.LOG_TYPE_REMOVING}: {tariffType.TypeDescription}, USERID: {userId}");
            return await _unitOfWork.SaveAsync();
        }

        public async Task<int> SaveAsync()
        {
            return await _unitOfWork.SaveAsync();
        }
    }
}
