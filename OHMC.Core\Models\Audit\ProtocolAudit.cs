﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace OHMC.Core.Models
{
    public class ProtocolAudit : BaseAudit
    {
        [Key]
        public int ProtocolAuditId { get; set; }
        public int ProtocolId { get; set; }
        [MaxLength(20)]
        public string Code { get; set; } = string.Empty;
        [MaxLength(500)]
        public string ProtocolName { get; set; } = string.Empty;
        public int RegimenId { get; set; }
        [MaxLength(200)]
        public string? EndComment { get; set; } = string.Empty;
        [MaxLength(100)]
        public string CancerGroupName { get; set; } = string.Empty;
        [MaxLength(200)]
        public string? CancerType { get; set; } = string.Empty;
        [MaxLength(20)]
        public string? OMCode { get; set; } = string.Empty;
        public bool IsProtocolSupportedByHTA { get; set; }
        public string? HTANumber { get; set; }
        public string? LevelOfCare { get; set; }
        public bool Restriction { get; set; }
        [MaxLength(200)]
        public string? RestrictionDescription { get; set; }
        public int? RestrictionLevel { get; set; }
        [MaxLength(50)]
        public string? TreatmentIntent { get; set; }

        [MaxLength(10)]
        public string? IsPMB { get; set; }
        [MaxLength(20)]
        public string? PMBNumber { get; set; }
        public DateTime? PMBIssueDate { get; set; }
        public bool IsEML { get; set; }
        [MaxLength(200)]
        public string? EMLRestriction { get; set; } = string.Empty;
        [MaxLength(10)]
        public string? IsTender { get; set; }
        [MaxLength(20)]
        public string? TenderNumber { get; set; }
        public DateTime? TenderStartDate { get; set; }
        public DateTime? TenderEndDate { get; set; }
        public double? TotalCostPerCycle { get; set; }
        public double? TotalCostPerCourse { get; set; }
        [MaxLength(50)]
        public string? ReviewIndicator { get; set; }
        [MaxLength(200)]
        public string? ReviewIndicatorDescription { get; set; }
        public DateTime? ReviewStartDate { get; set; }
        public DateTime? ReviewEndDate { get; set; }
        [MaxLength(20)]
        public string? XCode { get; set; }
        [MaxLength(200)]
        public string? Chapter { get; set; }
        [MaxLength(200)]
        public string? TreatmentOption { get; set; }
        public int? CancerGroupId { get; set; }
        public int? CancerTypeId { get; set; }
        public int? TreatmentOptionId { get; set; }
        public int? ChapterId { get; set; }
        public int? FormularyTypeId { get; set; }
        [MaxLength(100)]
        public string? Status { get; set; } = string.Empty;
        public int? PrintOrder { get; set; }
        [MaxLength(20)]
        public string? ICD10Code { get; set; }
        public int? PTCTypeId { get; set; }
        public int? ProtocolHeaderId { get; set; }
    }
}
