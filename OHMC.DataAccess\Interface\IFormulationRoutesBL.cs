﻿using OHMC.Core.Models;

namespace OHMC.DataAccess.Interface
{
    public interface IFormulationRoutesBL
    {
        Task<FormulationRoute> GetFormuationRoute(int id);
        Task<IEnumerable<FormulationRoute>> GetFormulationRoutesByFormulationAsync(int formulationId);
        Task<int> GetNumberOfRouteByFormulationAsync(int id);
        Task<int> GetMaxOrderNumberRouteByFormulationAsync(int id);
        Task<int> SaveAsync();
        Task<int> UpSert(FormulationRoute formulationRoute, int userId);
        Task<int> RemoveFormulationRoute(FormulationRoute formulationRoute);
    }
}