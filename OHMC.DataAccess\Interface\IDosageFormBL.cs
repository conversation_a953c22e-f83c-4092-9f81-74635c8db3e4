﻿using OHMC.Core.Models;

namespace OHMC.DataAccess.Interface
{
    public interface IDosageFormBL
    {
        Task<IEnumerable<DosageForm>> GetDosageFormsAsync();
        Task<DosageForm> GetDosageFormAsync(int id);
        Task<int> RemoveFormType(int id, int userId);
        Task<int> SaveAsync();
        Task<IEnumerable<DosageForm>> SearchFormTypeAsync(string desc);
        Task<IEnumerable<DosageForm>> GetDosageFormsByFormTypeAsync(string formType);
        Task<int> UpSert(DosageForm dosageForm, int userId);
    }
}