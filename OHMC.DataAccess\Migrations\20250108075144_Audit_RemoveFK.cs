﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace OHMC.DataAccess.Migrations
{
    public partial class Audit_RemoveFK : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_CancerGroupsAudit_FormularyTypes_FormularyTypeId",
                table: "CancerGroupsAudit");

            migrationBuilder.DropForeignKey(
                name: "FK_ChaptersAudit_FormularyTypes_FormularyTypeId",
                table: "ChaptersAudit");

            migrationBuilder.DropForeignKey(
                name: "FK_ProtocolEvidencesAudit_Protocols_ProtocolId",
                table: "ProtocolEvidencesAudit");

            migrationBuilder.DropForeignKey(
                name: "FK_RegimenCostsAudit_Regimens_RegimenId",
                table: "RegimenCostsAudit");

            migrationBuilder.DropForeignKey(
                name: "FK_RegimenCostsAudit_Tariffs_TariffId",
                table: "RegimenCostsAudit");

            migrationBuilder.DropIndex(
                name: "IX_RegimenCostsAudit_RegimenId",
                table: "RegimenCostsAudit");

            migrationBuilder.DropIndex(
                name: "IX_RegimenCostsAudit_TariffId",
                table: "RegimenCostsAudit");

            migrationBuilder.DropIndex(
                name: "IX_ProtocolEvidencesAudit_ProtocolId",
                table: "ProtocolEvidencesAudit");

            migrationBuilder.DropIndex(
                name: "IX_ChaptersAudit_FormularyTypeId",
                table: "ChaptersAudit");

            migrationBuilder.DropIndex(
                name: "IX_CancerGroupsAudit_FormularyTypeId",
                table: "CancerGroupsAudit");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateIndex(
                name: "IX_RegimenCostsAudit_RegimenId",
                table: "RegimenCostsAudit",
                column: "RegimenId");

            migrationBuilder.CreateIndex(
                name: "IX_RegimenCostsAudit_TariffId",
                table: "RegimenCostsAudit",
                column: "TariffId");

            migrationBuilder.CreateIndex(
                name: "IX_ProtocolEvidencesAudit_ProtocolId",
                table: "ProtocolEvidencesAudit",
                column: "ProtocolId");

            migrationBuilder.CreateIndex(
                name: "IX_ChaptersAudit_FormularyTypeId",
                table: "ChaptersAudit",
                column: "FormularyTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_CancerGroupsAudit_FormularyTypeId",
                table: "CancerGroupsAudit",
                column: "FormularyTypeId");

            migrationBuilder.AddForeignKey(
                name: "FK_CancerGroupsAudit_FormularyTypes_FormularyTypeId",
                table: "CancerGroupsAudit",
                column: "FormularyTypeId",
                principalTable: "FormularyTypes",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_ChaptersAudit_FormularyTypes_FormularyTypeId",
                table: "ChaptersAudit",
                column: "FormularyTypeId",
                principalTable: "FormularyTypes",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_ProtocolEvidencesAudit_Protocols_ProtocolId",
                table: "ProtocolEvidencesAudit",
                column: "ProtocolId",
                principalTable: "Protocols",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_RegimenCostsAudit_Regimens_RegimenId",
                table: "RegimenCostsAudit",
                column: "RegimenId",
                principalTable: "Regimens",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_RegimenCostsAudit_Tariffs_TariffId",
                table: "RegimenCostsAudit",
                column: "TariffId",
                principalTable: "Tariffs",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
