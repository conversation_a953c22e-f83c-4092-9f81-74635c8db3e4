﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace OHMC.DataAccess.Migrations
{
    public partial class OutcomeType_AddOutcomeDescription : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "OutcomeDescription",
                table: "OutcomeTypesAudit",
                type: "nvarchar(300)",
                maxLength: 300,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "OutcomeDescription",
                table: "OutcomeTypes",
                type: "nvarchar(300)",
                maxLength: 300,
                nullable: true);

        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "OutcomeDescription",
                table: "OutcomeTypesAudit");

            migrationBuilder.DropColumn(
                name: "OutcomeDescription",
                table: "OutcomeTypes");
        }
    }
}
