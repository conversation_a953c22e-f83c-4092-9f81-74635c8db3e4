﻿using OHMC.Core.Models;

namespace OHMC.DataAccess.Interface
{
    public interface ICancerGroupsBL
    {
        Task<IEnumerable<CancerGroup>> GetAllCancerGroupsAsync();
        Task<CancerGroup> GetCancerGroupAsync(int id);
        Task<IEnumerable<CancerGroupType>> GetAllCancerGroupTypesAsync();
        Task<IEnumerable<CancerGroupType>> GetCancerGroupTypesPagesAsync(int pageNumber, int pageSize);
        Task<int> GetCancerGroupTypesCountAsync();
        Task<CancerGroupType> GetCancerGroupTypeAsync(int id);
        Task<int> UpSertCancerGroup(CancerGroup group, int userId);
        Task<int> UpSertCancerGroupType(CancerGroupType groupType, int userId);
        Task<int> RemoveCancerGroup(int id, int userId);
        Task<int> RemoveCancerGroupType(int id, int userId);
        Task<IEnumerable<CancerGroupType>> GetCancerTypesByGroupAsync(string group);
        Task<IEnumerable<CancerGroup>> GetCancerGroupsByPTCAsync(int ptcTypeId);
        Task<IEnumerable<CancerGroup>> GetCancerGroupsByFormularyType(int formularyTypeId);
        Task<IEnumerable<CancerGroupICD10>> GetCancerGroupICD10sByGroup(int cancerGroupId);
        Task<CancerGroupICD10> GetCancerGroupICD10Async(int id);
        Task<int> UpSertCancerGroupICD10(CancerGroupICD10 icd10, int userId);
        Task<string> GetCancerGroupICD10sDescription(int cancerGroupId);
        Task<CancerGroupICD10> GetCancerGroupICD10Async(string icd10Code, int cancerGroupId);
        Task<int> RemoveCancerGroupICD10ICD10Async(CancerGroupICD10 icd10);
    }
}