﻿using OHMC.Core.Models;

namespace OHMC.DataAccess.Interface
{
    public interface IDispensingAbbreviationRepository
    {
        void Add(DispensingAbbreviation abbreviation);
        Task<DispensingAbbreviation> GetDispensingAbbreviationAsync(int id);
        Task<DispensingAbbreviation> GetDispensingAbbreviationByAbbreviationAsync(string abbreviation);
        Task<IEnumerable<DispensingAbbreviation>> GetDispensingAbbreviationsAsync();
        void Update(DispensingAbbreviation abbreviation);
    }
}