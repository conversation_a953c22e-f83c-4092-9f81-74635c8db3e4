﻿using OHMC.Core.Models;

namespace OHMC.DataAccess.Interface
{
    public interface IMedicalSchemeBenefitPlanBL
    {
        Task<MedicalSchemeBenefitPlan> GetMedicalSchemeBenefitPlanAsync(int id);
        Task<IEnumerable<MedicalSchemeBenefitPlan>> GetMedicalSchemeBenefitPlansAsync();
        Task<IEnumerable<MedicalSchemeBenefitPlan>> GetMedicalSchemeBenefitPlansAsync(int medicalSchemeId);
        Task<int> SaveAsync();
        Task<int> UpSert(MedicalSchemeBenefitPlan plan, int userId);
    }
}