﻿using OHMC.Core.Models;

namespace OHMC.DataAccess.Interface
{
    public interface IProtocolStatusRepository
    {
        void Add(ProtocolStatus protocolStatus);
        Task<ProtocolStatus> GetLatestProtocolStatusAsync(int protocolId);
        Task<IEnumerable<ProtocolStatus>> GetProtocolStatusesAsync(int protocolId);
        Task<ProtocolStatus> GetProtocolStatusAsync(int id);
        void Update(ProtocolStatus protocolStatus);
    }
}