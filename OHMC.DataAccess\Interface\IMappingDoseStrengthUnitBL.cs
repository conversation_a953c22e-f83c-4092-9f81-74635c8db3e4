﻿using OHMC.Core.Models;

namespace OHMC.Application
{
    public interface IMappingDoseStrengthUnitBL
    {
        Task<MappingDoseStrengthUnit> GetMappingDoseStrengthUnitAsync(int id);
        Task<MappingDoseStrengthUnit> GetMappingDoseStrengthUnitByDoseAsync(string doseUnit);
        Task<IEnumerable<MappingDoseStrengthUnit>> GetMappingDoseStrengthUnitsAsync();
        Task<int> SaveAsync();
        Task<int> UpSert(MappingDoseStrengthUnit mapping, int userId);
    }
}