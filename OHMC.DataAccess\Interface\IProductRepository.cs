﻿using OHMC.Core.Models;

namespace OHMC.DataAccess.Interface
{
    public interface IProductRepository
    {
        void Add(Product product);
        Task<Product> GetProductAsync(int id);
        Task<Product> GetProductSimpleAsync(int id);
        Task<int> GetProductsCountAsync();
        Task<IEnumerable<Product>> GetPagedProductsAsync(int pageNumber, int pageSize);
        Task<IEnumerable<Product>> GetProductsAsync();
        Task<IEnumerable<Product>> GetProductsFullAsync();

        Task<IEnumerable<Product>> SearchProductsAsync(string desc);
        void Update(Product product);
        Task<IEnumerable<Product>> GetProductsByFormulationAsync(int formulationId);
        Task<IEnumerable<ProductSEPOverride>> GetProductSEPOverridesAsync(int productId);
    }
}