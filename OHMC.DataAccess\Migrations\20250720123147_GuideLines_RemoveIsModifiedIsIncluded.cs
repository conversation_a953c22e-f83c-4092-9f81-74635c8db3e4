﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace OHMC.DataAccess.Migrations
{
    public partial class GuideLines_RemoveIsModifiedIsIncluded : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsIncluded",
                table: "GuidelinesAudit");

            migrationBuilder.DropColumn(
                name: "IsModified",
                table: "GuidelinesAudit");

            migrationBuilder.DropColumn(
                name: "IsIncluded",
                table: "Guidelines");

            migrationBuilder.DropColumn(
                name: "IsModified",
                table: "Guidelines");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "IsIncluded",
                table: "GuidelinesAudit",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsModified",
                table: "GuidelinesAudit",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsIncluded",
                table: "Guidelines",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsModified",
                table: "Guidelines",
                type: "bit",
                nullable: false,
                defaultValue: false);
        }
    }
}
