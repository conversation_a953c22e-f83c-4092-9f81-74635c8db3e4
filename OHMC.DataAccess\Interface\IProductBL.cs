﻿using OHMC.Core.Common.Response;
using OHMC.Core.Models;

namespace OHMC.DataAccess.Interface
{
    public interface IProductBL
    {
        Task<IEnumerable<Product>> GetPagedProductsAsync(int pageNumber, int pageSize);
        Task<Product> GetProductAsync(int id);
        Task<IEnumerable<Product>> GetProductsFullAsync();
        Task<IEnumerable<Product>> GetProductsFullWithFormulationAsync();
        Task<int> GetProductsCountAsync();
        Task<int> SaveAsync();
        Task<IEnumerable<Product>> SearchProductsAsync(string desc);
        void AddProductDocument(ProductDocument document);
        Task<IEnumerable<ProductDocument>> GetProductDocumentsAsync(int productId);
        Task<MedicineAvailability> GetMedicineAvailabilityAsync(int id);
        Task<IEnumerable<MedicineAvailability>> GetMedicineAvailabilitiesAsync(int productId);
        Task<MedicineAvailability> GetLatestMedicineAvailabilityAsync(int productId);
        Task<int> UpSertMedicineAvailability(MedicineAvailability availability, int userId);
        Task<int> UpSert(Product product, int userId);
        Task<CopyProductResponse> CopyProductAsync(int id, int userId);
        Task<IEnumerable<Product>> GetProductsAsync();
        Task<bool> CheckProductAvailability(int productId);
        Task<IEnumerable<ProductSEPOverride>> GetProductSEPOverridesAsync(int productId);
    }
}