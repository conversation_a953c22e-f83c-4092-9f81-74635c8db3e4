﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace OHMC.DataAccess.Migrations
{
    public partial class Doses_AddDoseLow2 : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<double>(
                name: "<PERSON><PERSON><PERSON>ow2",
                table: "<PERSON>ses<PERSON><PERSON><PERSON>",
                type: "float",
                nullable: true);

            migrationBuilder.AddColumn<double>(
                name: "DoseLow2",
                table: "Doses",
                type: "float",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "<PERSON><PERSON>Low2",
                table: "<PERSON><PERSON>Audit");

            migrationBuilder.DropColumn(
                name: "<PERSON><PERSON><PERSON>ow2",
                table: "<PERSON><PERSON>");
        }
    }
}
