﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace OHMC.DataAccess.Migrations
{
    public partial class Doses_AddINN2Id : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "INN2Id",
                table: "DosesAudit",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "INN2Id",
                table: "Doses",
                type: "int",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_Doses_INN2Id",
                table: "Doses",
                column: "INN2Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Doses_INNs_INN2Id",
                table: "Doses",
                column: "INN2Id",
                principalTable: "INNs",
                principalColumn: "Id");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Doses_INNs_INN2Id",
                table: "Doses");

            migrationBuilder.DropIndex(
                name: "IX_Doses_INN2Id",
                table: "Doses");

            migrationBuilder.DropColumn(
                name: "INN2Id",
                table: "DosesAudit");

            migrationBuilder.DropColumn(
                name: "INN2Id",
                table: "Doses");
        }
    }
}
