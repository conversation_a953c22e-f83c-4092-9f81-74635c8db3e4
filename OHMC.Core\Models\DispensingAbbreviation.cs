﻿using System.ComponentModel.DataAnnotations;

namespace OHMC.Core.Models
{
    public class DispensingAbbreviation : ModelBase
    {
        [Key]
        public int Id { get; set; }
        [Required, MaxLength(100)]
        public string FullText { get; set; } = string.Empty;
        [Required, <PERSON><PERSON>ength(20)]
        public string Abbreviation { get; set; } = string.Empty;
        [Required]
        public int Hourly { get; set; }
        [Required]
        public int DailyDoses { get; set; }
    }
}
