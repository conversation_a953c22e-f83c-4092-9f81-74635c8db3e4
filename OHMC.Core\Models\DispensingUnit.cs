﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace OHMC.Core.Models
{
    public class DispensingUnit : ModelBase
    {
        [Key]
        public int Id { get; set; }
        [Required, DisplayName("Qty")]
        public int AdminUnitQty { get; set; }
        [Required, <PERSON><PERSON><PERSON><PERSON>(50), <PERSON><PERSON><PERSON><PERSON><PERSON>("Unit")]
        public string AdminUnit {  get; set; } = string.Empty;
        [Required, <PERSON><PERSON><PERSON><PERSON>(50), <PERSON><PERSON><PERSON><PERSON><PERSON>("Unit Type")]
        public string AdminUnitType { get; set; } = string.Empty;
    }
}
