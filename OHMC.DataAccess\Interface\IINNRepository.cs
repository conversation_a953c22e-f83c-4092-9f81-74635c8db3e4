﻿using OHMC.Core.Models;

namespace OHMC.DataAccess.Interface
{
    public interface IINNRepository
    {
        Task<INN> GetINNAsync(int id);
        Task<IEnumerable<INN>> GetAllINNsAsync();
        Task<IEnumerable<INN>> GetPagedINNAsync(int pageNumber, int pageSize);
        Task<int> GetAllINNCounts();
        Task<IEnumerable<INN>> SearchINNsAsync(string desc);
        void Add(INN inn);
        void Update(INN inn);
        Task<IEnumerable<INN>> GetINNsWithProductsAsync();
        Task<int> GetINNCountByPrefixAsync(string prefix);
    }
}
