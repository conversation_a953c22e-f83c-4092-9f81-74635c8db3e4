﻿using OHMC.Core.Models;

namespace OHMC.DataAccess.Interface
{
    public interface IProtocolEvidenceRepository
    {
        void Add(ProtocolEvidence protocolEvidence);
        Task<int> GenerateRefNumbers();
        Task<int> GetMaxReferenceNumber();
        Task<ProtocolEvidence> GetProtocolEvidenceAsync(int id);
        Task<IEnumerable<ProtocolEvidence>> GetProtocolEvidencesAsync(int protocolId);
        void RemoveProtocolEvidence(ProtocolEvidence protocolEvidence);
        void Update(ProtocolEvidence protocolEvidence);
    }
}