﻿using OHMC.Core.Models;

namespace OHMC.DataAccess.Interface
{
    public interface IFormulationRepository
    {
        void Add(Formulation formulation);
        Task<Formulation> GetFormulationAsync(int id);
        Task<IEnumerable<Formulation>> GetFormulationsAsync();
        Task<Formulation> GetFormulationOnlyAsync(int id);
        Task<IEnumerable<Formulation>> GetPagedFormulationsAsync(int pageNumber, int pageSize);
        Task<int> GetFormulationsCountAsync();
        Task<IEnumerable<Formulation>> SearchFormulationsAsync(string desc);
        void Update(Formulation formulation);
        Task<IEnumerable<Formulation>> GetFormulationsByINNAsync(string inn);
        Task<IEnumerable<Formulation>> GetFormulationsSimpleAsync();
        Task<IEnumerable<Formulation>> GetFormulationByDosageFormDescriptionCalcAsync(int id, string dosageFormDescriptionCalc);
    }
}