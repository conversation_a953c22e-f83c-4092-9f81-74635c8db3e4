﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace OHMC.DataAccess.Migrations
{
    public partial class CancerGroupType_Modality_Add_PrintOrder : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "PrintOrder",
                table: "ModalitiesAudit",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "PrintOrder",
                table: "Modalities",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "PrintOrder",
                table: "CancerGroupTypesAudit",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "PrintOrder",
                table: "CancerGroupTypes",
                type: "int",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "PrintOrder",
                table: "ModalitiesAudit");

            migrationBuilder.DropColumn(
                name: "PrintOrder",
                table: "Modalities");

            migrationBuilder.DropColumn(
                name: "PrintOrder",
                table: "CancerGroupTypesAudit");

            migrationBuilder.DropColumn(
                name: "PrintOrder",
                table: "CancerGroupTypes");
        }
    }
}
