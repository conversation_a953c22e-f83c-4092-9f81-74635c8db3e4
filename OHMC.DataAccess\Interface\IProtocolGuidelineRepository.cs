﻿using OHMC.Core.Models;

namespace OHMC.DataAccess.Interface
{
    public interface IProtocolGuidelineRepository
    {
        void Add(ProtocolGuideline protocolGuideline);
        Task<ProtocolGuideline> GetProtocolGuidelineAsync(int id);
        Task<ProtocolGuideline> GetProtocolGuidelineAsync(int protocolId, int guidelineId);
        Task<IEnumerable<ProtocolGuideline>> GetProtocolGuidelineByProtocolAsync(int protocolId);
        Task<string> GetProtocolGuidelinesDescription(int protocolId);
        void Update(ProtocolGuideline guideline);
    }
}