﻿using Microsoft.Extensions.Logging;
using OHMC.Core.Common.Const;
using OHMC.Core.Common.Utils;
using OHMC.Core.Models;
using OHMC.DataAccess.Interface;

namespace OHMC.Application
{
    public class INNBL : IINNBL
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<INNBL> _logger;
        public INNBL(IUnitOfWork unitOfWork, ILogger<INNBL> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<IEnumerable<INN>> GetINNsAsync()
        {
            return await _unitOfWork.INNRepository.GetAllINNsAsync();
        }

        public async Task<IEnumerable<INN>> GetPagedAsync(int pageNumber, int pageSize)
        {
            return await _unitOfWork.INNRepository.GetPagedINNAsync(pageNumber, pageSize);
        }

        public async Task<int> GetINNCountsAsync()
        {
            return await _unitOfWork.INNRepository.GetAllINNCounts();
        }

        public async Task<IEnumerable<INN>> SearchAsync(string desc)
        {
            return await _unitOfWork.INNRepository.SearchINNsAsync(desc);
        }

        public async Task<IEnumerable<INN>> GetINNsWithProductsAsync()
        {
            return await _unitOfWork.INNRepository.GetINNsWithProductsAsync();
        }

        public async Task<IEnumerable<INN>> SearchINNAsync(string desc)
        {
            return await _unitOfWork.INNRepository.SearchINNsAsync(desc);
        }

        public async Task<INN> GetINN(int id)
        {
            return await _unitOfWork.INNRepository.GetINNAsync(id);
        }

        public async Task<int> UpSert(INN inn, int userId)
        {
            inn.UpdatedBy = userId;
            inn.UpdatedDate = DateTime.Now;

            if (inn.Id > 0)
            {
                _unitOfWork.INNRepository.Update(inn);
                _logger.LogInformation($"{StrConst.LOG_TYPE_UPDATE}: {inn.INNDescription}, USERID: {userId}");
            }
            else
            {
                inn.CreateBy = userId;
                inn.CreatedDate = DateTime.Now;
                inn.Code = await GetNextCodeReference(inn.INNDescription.Substring(0, 1));

                _unitOfWork.INNRepository.Add(inn);
                _logger.LogInformation($"{StrConst.LOG_TYPE_ADDING}: {inn.INNDescription}, USERID: {userId}");
            }           

            return await SaveAsync();
        }

        public async Task<int> RemoveINN(int id, int userId)
        {
            var inn = await _unitOfWork.INNRepository.GetINNAsync(id);
            inn.UpdatedBy = userId;
            inn.UpdatedDate = DateTime.Now;
            inn.Deleted = true;
            inn.DeleteOn = DateTime.Now;
            _unitOfWork.INNRepository.Update(inn);
            _logger.LogInformation($"{StrConst.LOG_TYPE_REMOVING}: {inn.INNDescription}, USERID: {userId}");
            return await _unitOfWork.SaveAsync();
        }

        public async Task<string> GetNextCodeReference(string prefix)
        {
            return StringHelper.AddDefaultPrefix(prefix, (await _unitOfWork.INNRepository.GetINNCountByPrefixAsync(prefix) + 1).ToString(), 5);
        }

        public async Task<int> SaveAsync()
        {
            return await _unitOfWork.SaveAsync();
        }
    }
}
