﻿using Microsoft.Extensions.Logging;
using OHMC.Core.Common.Const;
using OHMC.Core.Models;
using OHMC.DataAccess.Interface;

namespace OHMC.Application
{
    public class EvidenceTypeBL : IEvidenceTypeBL
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<EvidenceTypeBL> _logger;
        public EvidenceTypeBL(IUnitOfWork unitOfWork, ILogger<EvidenceTypeBL> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<IEnumerable<EvidenceType>> GetEvidenceTypesAsync()
        {
            return await _unitOfWork.EvidenceTypeRepository.GetEvidenceTypesAsync();
        }

        public async Task<EvidenceType> GetEvidenceType(int id)
        {
            return await _unitOfWork.EvidenceTypeRepository.GetEvidenceTypeAsync(id);
        }

        public async Task<int> UpSert(EvidenceType evidenceType, int userId)
        {
            evidenceType.UpdatedBy = userId;
            evidenceType.UpdatedDate = DateTime.Now;

            if (evidenceType.Id > 0)
            {
                _unitOfWork.EvidenceTypeRepository.Update(evidenceType);
                _logger.LogInformation($"{StrConst.LOG_TYPE_UPDATE}: {evidenceType.EvidenceName}, USERID: {userId}");
            }
            else
            {
                evidenceType.CreateBy = userId;
                evidenceType.CreatedDate = DateTime.Now;

                _unitOfWork.EvidenceTypeRepository.Add(evidenceType);
                _logger.LogInformation($"{StrConst.LOG_TYPE_ADDING}: {evidenceType.EvidenceName}, USERID: {userId}");
            }

            return await SaveAsync();
        }

        public async Task<int> SaveAsync()
        {
            return await _unitOfWork.SaveAsync();
        }
    }
}
