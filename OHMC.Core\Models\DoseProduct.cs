﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace OHMC.Core.Models
{
    public class DoseProduct : ModelBase
    {
        [Key]
        public int Id { get; set; }
        [ForeignKey("Dose")]
        public int DoseId { get; set; }
        public Dose Dose { get; set; }
        [Required]
        public int OrderNumber { get; set; } = 1;
        [ForeignKey("Product")]
        public int ProductId { get; set; }
        public Product Product { get; set; }

        public double? StrengthValue { get; set; }
        public string DoseUnits { get; set; } = string.Empty;
        public double? Price { get; set; }
        public double? DispensingUnitsNumber { get; set; }
        public double? AdminFraction { get; set; }
        public double? AdminFractionByDispensingUnits { get; set; }
        public string DispensingUnit { get; set; } = string.Empty;
        public double DoseAccumulated { get; set; }
        public double DoseAccumulatedPerc { get; set; }
    }
}
