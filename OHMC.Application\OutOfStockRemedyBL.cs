﻿using Microsoft.Extensions.Logging;
using OHMC.Core.Common.Const;
using OHMC.Core.Models;
using OHMC.DataAccess.Interface;

namespace OHMC.Application
{
    public class OutOfStockRemedyBL : IOutOfStockRemedyBL
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<OutOfStockRemedyBL> _logger;

        public OutOfStockRemedyBL(IUnitOfWork unitOfWork, ILogger<OutOfStockRemedyBL> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<IEnumerable<OutOfStockRemedy>> GetOutOfStockRemediesAsync()
        {
            return await _unitOfWork.OutOfStockRemedyRepository.GetOutOfStockRemediesAsync();
        }

        public async Task<OutOfStockRemedy> GetOutOfStockRemedy(int id)
        {
            return await _unitOfWork.OutOfStockRemedyRepository.GetOutOfStockRemedyAsync(id);
        }

        public async Task<int> UpSert(OutOfStockRemedy remedy, int userId)
        {
            remedy.UpdatedBy = userId;
            remedy.UpdatedDate = DateTime.Now;

            if (remedy.Id > 0)
            {
                _unitOfWork.OutOfStockRemedyRepository.Update(remedy);
                _logger.LogInformation($"{StrConst.LOG_TYPE_UPDATE}: {remedy.Description}, USERID: {userId}");
            }
            else
            {
                remedy.CreateBy = userId;
                remedy.CreatedDate = DateTime.Now;

                _unitOfWork.OutOfStockRemedyRepository.Add(remedy);
                _logger.LogInformation($"{StrConst.LOG_TYPE_ADDING}: {remedy.Description}, USERID: {userId}");
            }

            return await SaveAsync();
        }

        public async Task<int> SaveAsync()
        {
            return await _unitOfWork.SaveAsync();
        }
    }
}
