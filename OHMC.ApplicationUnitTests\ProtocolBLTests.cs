﻿using FluentAssertions;
using Microsoft.Extensions.Logging;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using OHMC.Application;
using OHMC.Core.Common.Response;
using OHMC.Core.Models;
using OHMC.DataAccess.Interface;

namespace OHMC.ApplicationUnitTests
{
    [TestClass]
    public class ProtocolBLTests : BaseTest
    {
        private readonly Mock<IUnitOfWork> _unitOfWork = new();
        private readonly Mock<ILogger<ProtocolBL>> _logger = new();
        private ProtocolBL _protocolBL;

        [TestInitialize]
        public void Initialize()
        {
            _protocolBL = new ProtocolBL(_unitOfWork.Object, _logger.Object);
        }

        [TestMethod]
        public async Task GetProtocol_Should_Return_SingleProtocol()
        {
            //given 
            Protocol protocol = new Protocol()
            {
                Id = 1,
                Code = _faker.Random.String2(20),
                ProtocolName =  _faker.Random.String2(100),
                Status = _faker.Random.String2(20),
                CreateBy = 1,
                CreatedDate = DateTime.Now,
                UpdatedBy = 1,
                UpdatedDate = DateTime.Now,
                Deleted = false
            };

            bool protocolUnitOfWorkRepositoryGetAsyncHasBeenCalled = false;
            _unitOfWork.Setup(x => x.ProtocolRepository.GetProtocolAsync(It.IsAny<int>()))
                .Callback(() => protocolUnitOfWorkRepositoryGetAsyncHasBeenCalled = true)
                .Returns(Task.FromResult(protocol));

            //when 
            var result = await _protocolBL.GetProtocolAsync(1);

            //then
            Assert.IsNotNull(result);
            protocolUnitOfWorkRepositoryGetAsyncHasBeenCalled.Should().BeTrue();
        }

        [TestMethod]
        public async Task AddProtocol_Should_AddStatus()
        {
            //given 
            Protocol protocol = new Protocol()
            {
                Id = 0,
                Code = _faker.Random.String2(20),
                ProtocolName = _faker.Random.String2(100),
                Status = _faker.Random.String2(20),
                CreateBy = 1,
                CreatedDate = DateTime.Now,
                UpdatedBy = 1,
                UpdatedDate = DateTime.Now,
                Deleted = false
            };

            ProtocolStatus protocolStatus = new ProtocolStatus()
            {
                Id = 0,
                ProtocolId = protocol.Id,
                Status = protocol.Status,
                Comment = _faker.Random.String2(200),
                CreateBy = 1,
                CreatedDate = DateTime.Now,
                UpdatedBy = 1,
                UpdatedDate = DateTime.Now,
                Deleted = false
            };

            bool protocolUnitOfWorkRepositoryAddAsyncHasBeenCalled = false;
            _unitOfWork.Setup(x => x.ProtocolRepository.Add(protocol))
                .Callback(() => protocolUnitOfWorkRepositoryAddAsyncHasBeenCalled = true);

            bool protocolUnitOfWorkRepositorySaveAsyncHasBeenCalled = false;
            _unitOfWork.Setup(x => x.SaveAsync())
                .Callback(() => protocolUnitOfWorkRepositorySaveAsyncHasBeenCalled = true)
                .Returns(Task.FromResult(1));

            bool protocolStatusUnitOfWorkRepositoryAddProtocolStatusAsyncHasBeenCalled = false;
            _unitOfWork.Setup(x => x.ProtocolStatusRepository.Add(It.IsAny<ProtocolStatus>()))
                .Callback(() => protocolStatusUnitOfWorkRepositoryAddProtocolStatusAsyncHasBeenCalled = true);

            //when 
            var result = await _protocolBL.UpSert(protocol, 1);

            //then
            Assert.IsNotNull(result);
            protocolUnitOfWorkRepositoryAddAsyncHasBeenCalled.Should().BeTrue();
            protocolStatusUnitOfWorkRepositoryAddProtocolStatusAsyncHasBeenCalled.Should().BeTrue();
            protocolUnitOfWorkRepositorySaveAsyncHasBeenCalled.Should().BeTrue();
        }

        [TestMethod]
        public async Task GenerateNumbers_Success()
        {
            //given 
            int success = 20;
            GenerateRefNumbersResponse response = new GenerateRefNumbersResponse() 
            { 
                ResponseCode = success, 
                ResponseMessage = "Notification-Reference numbers generated." 
            };

            bool protocolUnitOfWorkRepositoryGenerateRefNumbersAsyncHasBeenCalled = false;
            _unitOfWork.Setup(x => x.ProtocolEvidenceRepository.GenerateRefNumbers())
                .Callback(() => protocolUnitOfWorkRepositoryGenerateRefNumbersAsyncHasBeenCalled = true)
                .Returns(Task.FromResult(success));

            //when 
            var result = await _protocolBL.GenerateRefNumbers();

            //then
            Assert.IsNotNull(result);
            protocolUnitOfWorkRepositoryGenerateRefNumbersAsyncHasBeenCalled.Should().BeTrue();
        }

        [TestMethod]
        public async Task GenerateNumbers_Fail()
        {
            //given 
            int success = -1;
            GenerateRefNumbersResponse response = new GenerateRefNumbersResponse()
            {
                ResponseCode = success,
                ResponseMessage = "Failed - Reference number generation."
            };

            bool protocolUnitOfWorkRepositoryGenerateRefNumbersAsyncHasBeenCalled = false;
            _unitOfWork.Setup(x => x.ProtocolEvidenceRepository.GenerateRefNumbers())
                .Callback(() => protocolUnitOfWorkRepositoryGenerateRefNumbersAsyncHasBeenCalled = true)
                .Returns(Task.FromResult(success));

            //when 
            var result = await _protocolBL.GenerateRefNumbers();

            //then
            Assert.IsNotNull(result);
            protocolUnitOfWorkRepositoryGenerateRefNumbersAsyncHasBeenCalled.Should().BeTrue();
            result.ResponseMessage.Contains("Failed");
        }
    }
}
