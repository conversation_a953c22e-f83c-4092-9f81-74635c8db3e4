﻿using FluentAssertions;
using Microsoft.Extensions.Logging;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using OHMC.Application;
using OHMC.Core.Models;
using OHMC.DataAccess.Interface;

namespace OHMC.ApplicationUnitTests
{
    [TestClass]
    public class DosingRegimenBLTests : BaseTest
    {
        private readonly Mock<IUnitOfWork> _unitOfWork = new();
        private readonly Mock<ILogger<DosingRegimenBL>> _logger = new();
        private DosingRegimenBL _dosingRegimenBL;

        [TestInitialize]
        public void Initialize()
        {
            _dosingRegimenBL = new DosingRegimenBL(_unitOfWork.Object, _logger.Object);
        }

        [TestMethod]
        public async Task GetDosingRegimen_Should_Return_SingleDosingRegimen()
        {
            //given 
            DosingRegimen dosingRegimen = new DosingRegimen()
            {
                Id = 1,
                INNId = 1,
                Route = "PO",
                CalculatedRegimenText = _faker.Random.String2(500),
                DosingRegimenName = _faker.Random.String2(500),
                DoseDescriptionText = _faker.Random.String2(500),
                DoseId = 1,
                DoseValue = _faker.Random.Float(200),
                Comment = _faker.Random.String2(500),
                ModifiedCalculatedRegimenText = _faker.Random.String2(500),
                NumberOfDaysPatientDosed = 2,
                DrugDays = "d1-20",
                UseAsCalc = false,
                CostPerRegimen = _faker.Random.Double(2000),
                CreateBy = 1,
                CreatedDate = DateTime.Now,
                UpdatedBy = 1,
                UpdatedDate = DateTime.Now,
                Deleted = false
            };

            bool dosingRegimenUnitOfWorkRepositoryGetAsyncHasBeenCalled = false;
            _unitOfWork.Setup(x => x.DosingRegimenRepository.GetDosingRegimenAsync(It.IsAny<int>()))
                .Callback(() => dosingRegimenUnitOfWorkRepositoryGetAsyncHasBeenCalled = true)
                .Returns(Task.FromResult(dosingRegimen));

            //when 
            var result = await _dosingRegimenBL.GetDosingRegimenAsync(1);

            //then
            Assert.IsNotNull(result);
            dosingRegimenUnitOfWorkRepositoryGetAsyncHasBeenCalled.Should().BeTrue();
        }
    }
}
