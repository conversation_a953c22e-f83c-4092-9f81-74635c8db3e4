﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace OHMC.Core.Models.Audit
{
    public class EvidenceAudit : BaseAudit
    {
        [Key]
        public int EvidenceAuditId { get; set; }
        public int EvidenceId { get; set; }
        [MaxLength(250), DisplayName("Title")]
        public string? Title { get; set; } = string.Empty;
        public string EvidenceDescription { get; set; } = string.Empty;
        [MaxLength(200)]
        public string? EvidenceTrialName { get; set; }
        [MaxLength(6)]
        public string? EvidencePublicationYear { get; set; }

        [MaxLength(30)]
        public string? EvidenceType { get; set; }

        [MaxLength(10)]
        public string? Phase { get; set; }

        [MaxLength(50)]
        public string? Blinding { get; set; }

        [MaxLength(50)]
        public string? LevelOfEvidence { get; set; }

        [MaxLength(100)]
        public string? SampleSize { get; set; }
        [MaxLength(100)]
        public string? ObservationPeriod { get; set; }
        [MaxLength(300)]
        public string? Outcome1 { get; set; }
        [MaxLength(300)]
        public string? Outcome1Effect { get; set; }
        [MaxLength(300)]
        public string? Outcome2 { get; set; }
        [MaxLength(300)]
        public string? Outcome2Effect { get; set; }
        [MaxLength(300)]
        public string? Outcome3 { get; set; }
        [MaxLength(300)]
        public string? Outcome3Effect { get; set; }
        [MaxLength(300)]
        public string? Outcome4 { get; set; }
        [MaxLength(300),]
        public string? Outcome4Effect { get; set; }
        [MaxLength(300)]
        public string? Outcome5 { get; set; }
        [MaxLength(300),]
        public string? Outcome5Effect { get; set; }
        [MaxLength(300)]
        public string? Comparator { get; set; }
        [MaxLength(300)]
        public string? CommentClinical { get; set; }
        [MaxLength(300)]
        public string? CommentCriticalAppraisal { get; set; }
    }
}
