﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace OHMC.Core.Models
{
    public class ProtocolICD10Code : ModelBase
    {
        [Key]
        public int Id { get; set; }
        [Required, ForeignKey("Protocol")]
        public int ProtocolId { get; set; }
        public Protocol Protocol { get; set; }
        [Required, MaxLength(100)]
        public string ICD10Code { get; set; } = string.Empty;
    }
}
