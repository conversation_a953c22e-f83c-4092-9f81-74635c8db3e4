﻿using System.ComponentModel.DataAnnotations;

namespace OHMC.Core.Models.MediKredit
{
    public class ProductFileTrailer : BaseMediKreditRecord
    {
        [StringLength(9)]
        public string TotalNumberOfRecords { get; set; } = string.Empty;
        [StringLength(6)]
        public string NumberOfType201 { get; set; } = string.Empty;
        [StringLength(6)]
        public string NumberOfType202 { get; set; } = string.Empty;
        [StringLength(6)]
        public string NumberOfType203 { get; set; } = string.Empty;
        [StringLength(6)]
        public string NumberOfType204 { get; set; } = string.Empty;
        [StringLength(6)]
        public string NumberOfType205 { get; set; } = string.Empty;
        [StringLength(6)]
        public string NumberOfType206 { get; set; } = string.Empty;
        [StringLength(9)]
        public string NumberOfType209 { get; set; } = string.Empty;
        [StringLength(6)]
        public string NumberOfType215 { get; set; } = string.Empty;
        [StringLength(6)]
        public string NumberOfType216 { get; set; } = string.Empty;
        [StringLength(6)]
        public string Filler1 { get; set; } = string.Empty;
        [StringLength(6)]
        public string Filler2 { get; set; } = string.Empty;
        [StringLength(6)]
        public string NumberOfType219 { get; set; } = string.Empty;
        [StringLength(6)]
        public string NumberOfType220 { get; set; } = string.Empty;
        [StringLength(6)]
        public string Filler3 { get; set; } = string.Empty;
        [StringLength(6)]
        public string Filler4 { get; set; } = string.Empty;
        [StringLength(6)]
        public string Filler5 { get; set; } = string.Empty;
        [StringLength(6)]
        public string NumberOfType224 { get; set; } = string.Empty;
        [StringLength(6)]
        public string NumberOfType225 { get; set; } = string.Empty;
        [StringLength(6)]
        public string NumberOfType226 { get; set; } = string.Empty;
        [StringLength(6)]
        public string NumberOfType227 { get; set; } = string.Empty;
        [StringLength(6)]
        public string NumberOfType228 { get; set; } = string.Empty;
        [StringLength(6)]
        public string NumberOfType229 { get; set; } = string.Empty;
        [StringLength(6)]
        public string NumberOfType230 { get; set; } = string.Empty;
        [StringLength(82)]
        public string Filler6 { get; set; } = string.Empty;
        [StringLength(8)]
        public string RecordCreationDate { get; set; } = string.Empty;
        [StringLength(6)]
        public string RecordCreationTime { get; set; } = string.Empty;
    }
}
