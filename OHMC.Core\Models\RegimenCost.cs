﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel;

namespace OHMC.Core.Models
{
    public class RegimenCost : ModelBase
    {
        [Key]
        public int Id { get; set; }
        [ForeignKey("Regimen")]
        public int RegimenId { get; set; }
        public Regimen Regimen { get; set; }
        [ForeignKey("Tariff"), DisplayName("Tariff Code")]
        public int TariffId { get; set; }
        public Tariff Tariff { get; set; }
        public decimal Price { get; set; }
        public decimal Quantity { get; set; }
        public decimal Cost { get; set; }
        [MaxLength(100)]
        public string? Comment { get; set; }
    }
}
