﻿using Microsoft.Extensions.Logging;
using OHMC.Core.Common.Const;
using OHMC.Core.Models;
using OHMC.DataAccess.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace OHMC.Application
{
    public class StockStatusBL : IStockStatusBL
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<StockStatusBL> _logger;

        public StockStatusBL(IUnitOfWork unitOfWork, ILogger<StockStatusBL> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<IEnumerable<StockStatus>> GetStockStatusesAsync()
        {
            return await _unitOfWork.StockStatusRepository.GetStockStatusesAsync();
        }

        public async Task<StockStatus> GetStockStatus(int id)
        {
            return await _unitOfWork.StockStatusRepository.GetStockStatusAsync(id);
        }

        public async Task<int> UpSert(StockStatus status, int userId)
        {
            status.UpdatedBy = userId;
            status.UpdatedDate = DateTime.Now;

            if (status.Id > 0)
            {
                _unitOfWork.StockStatusRepository.Update(status);
                _logger.LogInformation($"{StrConst.LOG_TYPE_UPDATE}: {status.Description}, USERID: {userId}");
            }
            else
            {
                status.CreateBy = userId;
                status.CreatedDate = DateTime.Now;

                _unitOfWork.StockStatusRepository.Add(status);
                _logger.LogInformation($"{StrConst.LOG_TYPE_ADDING}: {status.Description}, USERID: {userId}");
            }

            return await SaveAsync();
        }

        public async Task<int> SaveAsync()
        {
            return await _unitOfWork.SaveAsync();
        }
    }
}
