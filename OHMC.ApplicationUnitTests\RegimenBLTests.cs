﻿using Bogus.Extensions;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using OHMC.Application;
using OHMC.Core.Models;
using OHMC.DataAccess.Interface;

namespace OHMC.ApplicationUnitTests
{
    [TestClass]
    public class RegimenBLTests : BaseTest
    {
        private readonly Mock<IUnitOfWork> _unitOfWork = new();
        private readonly Mock<ILogger<RegimenBL>> _logger = new();
        private RegimenBL _regimenBL;

        [TestInitialize]
        public void Initialize()
        {
            _regimenBL = new RegimenBL(_unitOfWork.Object, _logger.Object);
        }

        [TestMethod]
        public async Task GetRegimen_Should_Return_SingleRegimen()
        {
            //given 
            Regimen dosingRegimen = new Regimen()
            {
                Id = 1,
                Code = _faker.Random.String2(20),
                RegimenName = _faker.Random.String2(200),
                CycleInstruction = _faker.Random.String2(200),
                RegimenAdministeredCycles = _faker.Random.String2(20),
                RegimenAdministeredCyclesFromValue = _faker.Random.Int(10),
                RegimenAdministeredCyclesToValue = _faker.Random.Int(10),
                RegimenAdministeredNumber = _faker.Random.Int(10),  
                RegimenAdministeredTerm = _faker.Random.String2(20),
                RegimenAdministeredUnits = _faker.Random.String2(20),
                RegimenTail = _faker.Random.String2(500),
                RegimenLongDescription = _faker.Random.String2(600),
                NumberOfCycles = _faker.Random.Int(10),
                IsClass = false,
                Status = _faker.Random.String2(20),
                CreateBy = 1,
                CreatedDate = DateTime.Now,
                UpdatedBy = 1,
                UpdatedDate = DateTime.Now,
                Deleted = false
            };

            bool regimenUnitOfWorkRepositoryGetAsyncHasBeenCalled = false;
            _unitOfWork.Setup(x => x.RegimenRepository.GetRegimenAsync(It.IsAny<int>()))
                .Callback(() => regimenUnitOfWorkRepositoryGetAsyncHasBeenCalled = true)
                .Returns(Task.FromResult(dosingRegimen));

            //when 
            var result = await _regimenBL.GetRegimenAsync(1);

            //then
            Assert.IsNotNull(result);
            regimenUnitOfWorkRepositoryGetAsyncHasBeenCalled.Should().BeTrue();
        }

        [TestMethod]
        public async Task AddRegimen_Should_AddStatus()
        {
            //given 
            Regimen regimen = new Regimen()
            {
                Id = 0,
                Code = _faker.Random.String2(20),
                RegimenName = _faker.Random.String2(200),
                CycleInstruction = _faker.Random.String2(200),
                RegimenAdministeredCycles = _faker.Random.String2(20),
                RegimenAdministeredCyclesFromValue = _faker.Random.Int(10),
                RegimenAdministeredCyclesToValue = _faker.Random.Int(10),
                RegimenAdministeredNumber = _faker.Random.Int(10),
                RegimenAdministeredTerm = _faker.Random.String2(20),
                RegimenAdministeredUnits = _faker.Random.String2(20),
                RegimenTail = _faker.Random.String2(500),
                RegimenLongDescription = _faker.Random.String2(600),
                NumberOfCycles = _faker.Random.Int(10),
                IsClass = false,
                Status = _faker.Random.String2(20),
                CreateBy = 1,
                CreatedDate = DateTime.Now,
                UpdatedBy = 1,
                UpdatedDate = DateTime.Now,
                Deleted = false
            };

            RegimenStatus regimenStatus = new RegimenStatus()
            {
                Id = 0,
                RegimenId = regimen.Id,
                Status = regimen.Status,
                Comment = _faker.Random.String2(200),
                CreateBy = 1,
                CreatedDate = DateTime.Now,
                UpdatedBy = 1,
                UpdatedDate = DateTime.Now,
                Deleted = false
            };

            bool getAllRegimeDosingRegimenUsedDescriptionAsyncHasBeenCalled = false;
            _unitOfWork.Setup(x => x.RegimenDosingRegimenRepository.GetAllRegimeDosingRegimenUsedDescription(It.IsAny<int>()))
                .Callback(() => getAllRegimeDosingRegimenUsedDescriptionAsyncHasBeenCalled = true)
                .Returns(Task.FromResult(regimen.RegimenLongDescription));

            bool regimenUnitOfWorkRepositoryAddAsyncHasBeenCalled = false;
            _unitOfWork.Setup(x => x.RegimenRepository.Add(regimen))
                .Callback(() => regimenUnitOfWorkRepositoryAddAsyncHasBeenCalled = true);

            bool regimenUnitOfWorkRepositoryUpSertRegimenStatusAsyncHasBeenCalled = false;
            _unitOfWork.Setup(x => x.RegimenStatusRepository.Add(It.IsAny<RegimenStatus>()))
                .Callback(() => regimenUnitOfWorkRepositoryUpSertRegimenStatusAsyncHasBeenCalled = true);

            bool regimenUnitOfWorkRepositorySaveAsyncHasBeenCalled = false;
            _unitOfWork.Setup(x => x.SaveAsync())
                .Callback(() => regimenUnitOfWorkRepositorySaveAsyncHasBeenCalled = true)
                .Returns(Task.FromResult(1));

            //when 
            var result = await _regimenBL.UpSert(regimen, 1);

            //then
            Assert.IsNotNull(result);
            getAllRegimeDosingRegimenUsedDescriptionAsyncHasBeenCalled.Should().BeTrue();
            regimenUnitOfWorkRepositoryAddAsyncHasBeenCalled.Should().BeTrue();
            regimenUnitOfWorkRepositoryUpSertRegimenStatusAsyncHasBeenCalled.Should().BeTrue();
            regimenUnitOfWorkRepositorySaveAsyncHasBeenCalled.Should().BeTrue();
        }

        [TestMethod]
        public async Task DoesCodeExists_Should_Return_True_If_Code_Already_Used()
        {
            //given 
            Regimen regimen = new Regimen()
            {
                Id = 0,
                Code = "123456",
                RegimenName = _faker.Random.String2(200),
                CycleInstruction = _faker.Random.String2(200),
                RegimenAdministeredCycles = _faker.Random.String2(20),
                RegimenAdministeredCyclesFromValue = _faker.Random.Int(10),
                RegimenAdministeredCyclesToValue = _faker.Random.Int(10),
                RegimenAdministeredNumber = _faker.Random.Int(10),
                RegimenAdministeredTerm = _faker.Random.String2(20),
                RegimenAdministeredUnits = _faker.Random.String2(20),
                RegimenTail = _faker.Random.String2(500),
                RegimenLongDescription = _faker.Random.String2(600),
                NumberOfCycles = _faker.Random.Int(10),
                IsClass = false,
                Status = _faker.Random.String2(20),
                CreateBy = 1,
                CreatedDate = DateTime.Now,
                UpdatedBy = 1,
                UpdatedDate = DateTime.Now,
                Deleted = false
            };

            //_unitOfWork.RegimenRepository.DoesCodeExists
            bool doesCodeExistsAsyncHasBeenCalled = false;
            _unitOfWork.Setup(x => x.RegimenRepository.DoesCodeExists("123456", It.IsAny<int>()))
                .Callback(() => doesCodeExistsAsyncHasBeenCalled = true)
                .Returns(Task.FromResult(true));

            //when 
            var result = await _regimenBL.DoesCodeExists("123456", regimen.Id);

            //then
            Assert.IsNotNull(result);
            doesCodeExistsAsyncHasBeenCalled.Should().BeTrue();
            result.Should().BeTrue();
        }

        [TestMethod]
        public async Task DoesCodeExists_Should_Return_False_If_Code_Not_Used()
        {
            //given 
            Regimen regimen = new Regimen()
            {
                Id = 0,
                Code = "123456",
                RegimenName = _faker.Random.String2(200),
                CycleInstruction = _faker.Random.String2(200),
                RegimenAdministeredCycles = _faker.Random.String2(20),
                RegimenAdministeredCyclesFromValue = _faker.Random.Int(10),
                RegimenAdministeredCyclesToValue = _faker.Random.Int(10),
                RegimenAdministeredNumber = _faker.Random.Int(10),
                RegimenAdministeredTerm = _faker.Random.String2(20),
                RegimenAdministeredUnits = _faker.Random.String2(20),
                RegimenTail = _faker.Random.String2(500),
                RegimenLongDescription = _faker.Random.String2(600),
                NumberOfCycles = _faker.Random.Int(10),
                IsClass = false,
                Status = _faker.Random.String2(20),
                CreateBy = 1,
                CreatedDate = DateTime.Now,
                UpdatedBy = 1,
                UpdatedDate = DateTime.Now,
                Deleted = false
            };

            //_unitOfWork.RegimenRepository.DoesCodeExists
            bool doesCodeExistsAsyncHasBeenCalled = false;
            _unitOfWork.Setup(x => x.RegimenRepository.DoesCodeExists("M1", It.IsAny<int>()))
                .Callback(() => doesCodeExistsAsyncHasBeenCalled = true)
                .Returns(Task.FromResult(false));

            //when 
            var result = await _regimenBL.DoesCodeExists("M1", regimen.Id);

            //then
            Assert.IsNotNull(result);
            doesCodeExistsAsyncHasBeenCalled.Should().BeTrue();
            result.Should().BeFalse();
        }

        [TestMethod]
        public async Task GenerateRegimenCode_Should_Return_Code()
        {
            //given

            bool generateCodeReferenceAsyncHasBeenCalled = false;
            _unitOfWork.Setup(x => x.RegimenRepository.GetNextRegimenCodeReference(It.IsAny<string>()))
                .Callback(() => generateCodeReferenceAsyncHasBeenCalled = true)
                .Returns(Task.FromResult(1));

            bool doesCodeExistsAsyncHasBeenCalled = false;
            _unitOfWork.Setup(x => x.RegimenRepository.DoesCodeExists("M0020", It.IsAny<int>()))
                .Callback(() => doesCodeExistsAsyncHasBeenCalled = true)
                .Returns(Task.FromResult(false));

            //when 
            var result = await _regimenBL.GenerateRegimenCode("M");

            //then
            Assert.IsNotNull(result);
            doesCodeExistsAsyncHasBeenCalled.Should().BeTrue();
            generateCodeReferenceAsyncHasBeenCalled.Should().BeTrue();
        }

        //CopyRegimenAsync
        [TestMethod]
        public async Task CopyRegimen_Should_Be_Succesful()
        {
            //given 
            Regimen regimen = new Regimen()
            {
                Id = 1,
                Code = "OM1300",
                RegimenName = "M123456",
                CycleInstruction = _faker.Random.String2(200),
                RegimenAdministeredCycles = _faker.Random.String2(20),
                RegimenAdministeredCyclesFromValue = _faker.Random.Int(10),
                RegimenAdministeredCyclesToValue = _faker.Random.Int(10),
                RegimenAdministeredNumber = _faker.Random.Int(10),
                RegimenAdministeredTerm = _faker.Random.String2(20),
                RegimenAdministeredUnits = _faker.Random.String2(20),
                RegimenTail = _faker.Random.String2(500),
                RegimenLongDescription = _faker.Random.String2(600),
                NumberOfCycles = _faker.Random.Int(10),
                IsClass = false,
                Status = _faker.Random.String2(20),
                CreateBy = 1,
                CreatedDate = DateTime.Now,
                UpdatedBy = 1,
                UpdatedDate = DateTime.Now,
                Deleted = false
            };

            Regimen newRegimen = new Regimen()
            {
                Id = 2,
                Code = "OM1320",
                RegimenName = "M987654",
                CycleInstruction = _faker.Random.String2(200),
                RegimenAdministeredCycles = _faker.Random.String2(20),
                RegimenAdministeredCyclesFromValue = _faker.Random.Int(10),
                RegimenAdministeredCyclesToValue = _faker.Random.Int(10),
                RegimenAdministeredNumber = _faker.Random.Int(10),
                RegimenAdministeredTerm = _faker.Random.String2(20),
                RegimenAdministeredUnits = _faker.Random.String2(20),
                RegimenTail = _faker.Random.String2(500),
                RegimenLongDescription = _faker.Random.String2(600),
                NumberOfCycles = _faker.Random.Int(10),
                IsClass = false,
                Status = _faker.Random.String2(20),
                CreateBy = 1,
                CreatedDate = DateTime.Now,
                UpdatedBy = 1,
                UpdatedDate = DateTime.Now,
                Deleted = false
            };

            List<RegimenCost> currentRegimenCosts = new List<RegimenCost>();
            currentRegimenCosts.Add(new RegimenCost()
            {
                RegimenId = regimen.Id,
                Price = _faker.Random.Decimal2(10),
                Quantity = _faker.Random.Decimal2(2),
                Cost = _faker.Random.Decimal2(10),
                Comment = _faker.Random.String2(20),
                TariffId = _faker.Random.Int(10),
                CreateBy = 1,
                CreatedDate = DateTime.Now,
                UpdatedBy = 1,
                UpdatedDate = DateTime.Now,
                Deleted = false
            });

            RegimenCost newRegimenCost = new RegimenCost()
            {
                RegimenId = 2,
                Price = _faker.Random.Decimal2(10),
                Quantity = _faker.Random.Decimal2(2),
                Cost = _faker.Random.Decimal2(10),
                Comment = _faker.Random.String2(20),
                TariffId = _faker.Random.Int(10),
                CreateBy = 1,
                CreatedDate = DateTime.Now,
                UpdatedBy = 1,
                UpdatedDate = DateTime.Now,
                Deleted = false
            };

            List<RegimenDosingRegimen> regimenDosingRegimens = new List<RegimenDosingRegimen>();
            regimenDosingRegimens.Add(new RegimenDosingRegimen()
            {
                RegimenId = regimen.Id,
                JoiningTerm = _faker.Random.String2(10),
                OrderNumber = 1,
                DosingRegimenId = 1,
                CreateBy = 1,
                CreatedDate = DateTime.Now,
                UpdatedBy = 1,
                UpdatedDate = DateTime.Now,
                Deleted = false
            });

            RegimenDosingRegimen newRegimenDosingRegimen = new RegimenDosingRegimen()
            {
                RegimenId = 2,
                JoiningTerm = _faker.Random.String2(10),
                OrderNumber = 1,
                DosingRegimenId = 1,
                CreateBy = 1,
                CreatedDate = DateTime.Now,
                UpdatedBy = 1,
                UpdatedDate = DateTime.Now,
                Deleted = false
            };

            RegimenStatus newRegimenStatus = new RegimenStatus()
            {
                RegimenId = 2,
                Status = "ACTIVE",
                Comment = _faker.Random.String2(20),
                CreateBy = 1,
                CreatedDate = DateTime.Now,
                UpdatedBy = 1,
                UpdatedDate = DateTime.Now,
                Deleted = false
            };

            bool generateCodeReferenceAsyncHasBeenCalled = false;
            _unitOfWork.Setup(x => x.RegimenRepository.GetNextRegimenCodeReference(It.IsAny<string>()))
                .Callback(() => generateCodeReferenceAsyncHasBeenCalled = true)
                .Returns(Task.FromResult(1));

            bool doesCodeExistsAsyncHasBeenCalled = false;
            _unitOfWork.Setup(x => x.RegimenRepository.DoesCodeExists("OM1320", newRegimen.Id))
                .Callback(() => doesCodeExistsAsyncHasBeenCalled = true)
                .Returns(Task.FromResult(true));

            bool getRegmimenAsyncHasBeenCalled = false;
            _unitOfWork.Setup(x => x.RegimenRepository.GetRegimenAsync(regimen.Id))
                .Callback(() => getRegmimenAsyncHasBeenCalled = true)
                .Returns(Task.FromResult(regimen));

            bool getRegimenDosingRegimenByRegimenAsyncAsyncHasBeenCalled = false;
            _unitOfWork.Setup(x => x.RegimenDosingRegimenRepository.GetRegimenDosingRegimenByRegimenAsync(regimen.Id))
                .Callback(() => getRegimenDosingRegimenByRegimenAsyncAsyncHasBeenCalled = true)
                .Returns(Task.FromResult(regimenDosingRegimens.AsEnumerable()));

            bool getRegmimenCostAsyncHasBeenCalled = false;
            _unitOfWork.Setup(x => x.RegimenCostRepository.GetRegimenCostsByRegimenAsync(regimen.Id))
                .Callback(() => getRegmimenAsyncHasBeenCalled = true)
                .Returns(Task.FromResult(currentRegimenCosts.AsEnumerable()));

            //_unitOfWork.RegimenRepository.Add
            bool addRegimenAsyncHasBeenCalled = false;
            _unitOfWork.Setup(x => x.RegimenRepository.Add(newRegimen))
                .Callback(() => addRegimenAsyncHasBeenCalled = true);

            //_unitOfWork.RegimenDosingRegimenRepository.Add
            bool addRegimenDosingRegimenAsyncHasBeenCalled = false;
            _unitOfWork.Setup(x => x.RegimenDosingRegimenRepository.Add(newRegimenDosingRegimen))
                .Callback(() => addRegimenDosingRegimenAsyncHasBeenCalled = true);

            //_unitOfWork.RegimenCostRepository.Add
            bool addRegimenCostAsyncHasBeenCalled = false;
            _unitOfWork.Setup(x => x.RegimenCostRepository.Add(newRegimenCost))
                .Callback(() => addRegimenCostAsyncHasBeenCalled = true);

            //_unitOfWork.RegimenStatusRepository.Add
            bool addRegimenStatusAsyncHasBeenCalled = false;
            _unitOfWork.Setup(x => x.RegimenStatusRepository.Add(newRegimenStatus))
                .Callback(() => addRegimenStatusAsyncHasBeenCalled = true);

            //SaveAsync
            bool saveRegmimenAsyncHasBeenCalled = false;
            _unitOfWork.Setup(x => x.SaveAsync())
                .Callback(() => saveRegmimenAsyncHasBeenCalled = true)
                .Returns(Task.FromResult(1));

            //when 
            var result = await _regimenBL.CopyRegimenAsync(regimen.Id, 1);

            //then
            Assert.IsNotNull(result);
            generateCodeReferenceAsyncHasBeenCalled.Should().BeTrue();
            saveRegmimenAsyncHasBeenCalled.Should().BeTrue();
            result.NewCode.Should().NotBeNullOrEmpty();
            result.NewId.Should().NotBe(regimen.Id);
        }
    }
}
