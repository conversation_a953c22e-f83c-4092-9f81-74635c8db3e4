﻿using OHMC.Core.Models;

namespace OHMC.DataAccess.Interface
{
    public interface INetworkPracticeBL
    {
        Task<NetworkPractice> GetNetworkPracticeAsync(int id);
        Task<IEnumerable<NetworkPractice>> GetNetworkPracticesAsync();
        Task<IEnumerable<NetworkPracticeSpeciality>> GetNetworkPracticeSpecialitiesAsync(int networkMemberId);
        Task<IEnumerable<NetworkMember>> GetNetworkPracticeMembersAsync(int networkPracticeId);
        Task<NetworkPracticeSpeciality> GetNetworkPracticeSpecialityAsync(int id);
        Task<string> GetSpecialitiesDescription(int networkMemberId);
        Task<int> SaveAsync();
        Task<int> UpSert(NetworkPractice practice, int userId);
        Task<int> UpSertSpeciality(NetworkPracticeSpeciality speciality, int userId);
    }
}