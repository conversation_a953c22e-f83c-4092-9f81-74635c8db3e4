﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace OHMC.Core.Models
{
    public class Evidence : ModelBase
    {
        [Key]
        public int Id { get; set; }
        [MaxLength(250), DisplayName("Title")]
        public string? Title { get; set; } = string.Empty;

        [Required, Display<PERSON><PERSON>("Evidence")]
        public string EvidenceDescription { get; set; } = string.Empty;
        [MaxLength(200), DisplayName("Trial Name")]
        public string? EvidenceTrialName { get; set; }
        [MaxLength(6), Di<PERSON>lay<PERSON><PERSON>("Publication Year")]
        public string? EvidencePublicationYear { get; set; }

        [MaxLength(30), Display<PERSON>ame("Evidence Type")]
        public string? EvidenceType { get; set; }

        [MaxLength(10), DisplayName("Phase")]
        public string? Phase { get; set; }

        [MaxLength(50), DisplayName("Blinding")]
        public string? Blinding { get; set; }

        [<PERSON><PERSON>ength(50), <PERSON><PERSON><PERSON><PERSON><PERSON>("Level of evidence")]
        public string? LevelOfEvidence { get; set; }

        [MaxLength(100), Display<PERSON><PERSON>("Sample size")]
        public string? SampleSize { get; set; }
        [MaxLength(100), Display<PERSON><PERSON>("Observation Period")]
        public string? ObservationPeriod { get; set; }
        [MaxLength(300)]
        public string? Outcome1 { get; set; }
        [MaxLength(300)]
        public string? Outcome1Effect { get; set; }
        [MaxLength(300)]
        public string? Outcome2 { get; set; }
        [MaxLength(300)]
        public string? Outcome2Effect { get; set; }
        [MaxLength(300)]
        public string? Outcome3 { get; set; }
        [MaxLength(300)]
        public string? Outcome3Effect { get; set; }
        [MaxLength(300)]
        public string? Outcome4 { get; set; }
        [MaxLength(300),]
        public string? Outcome4Effect { get; set; }
        [MaxLength(300)]
        public string? Outcome5 { get; set; }
        [MaxLength(300),]
        public string? Outcome5Effect { get; set; }
        [MaxLength(300)]
        public string? Comparator { get; set; }
        [MaxLength(300)]
        public string? CommentClinical { get; set; }
        [MaxLength(300)]
        public string? CommentCriticalAppraisal { get; set; }
    }
}
