﻿using OHMC.Core.Models;

namespace OHMC.DataAccess.Interface
{
    public interface IDosingRegimenRepository
    {
        void Add(DosingRegimen dosingRegimen);
        Task<IEnumerable<DosingRegimen>> GetDosingRegimenAsync();
        Task<DosingRegimen> GetDosingRegimenAsync(int id);
        Task<IEnumerable<DosingRegimen>> GetDosingRegimenByDoseAsync(int doseId);
        Task<int> GetDosingRegimenCountAsync();
        Task<int> GetNextRegimenCodeReference(string prefix);
        Task<IEnumerable<DosingRegimen>> GetPagedDosingRegimensAsync(int pageNumber, int pageSize);
        Task<IEnumerable<DosingRegimen>> SearchDosingRegimensAsync(string desc);
        void Update(DosingRegimen dosingRegimen);
    }
}