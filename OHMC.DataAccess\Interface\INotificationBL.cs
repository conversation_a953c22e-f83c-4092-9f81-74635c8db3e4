﻿using OHMC.Core.Models;

namespace OHMC.DataAccess.Interface
{
    public interface INotificationBL
    {
        Task<int> AddNotificationAsync(Notification notification, int userId);
        Task<Notification> GetNotificationAsync(int id);
        Task<IEnumerable<Notification>> GetNotificationsAsync();
        Task<IEnumerable<Notification>> GetNotificationsByTypeAsync(string notificationType);
        Task<int> SaveAsync();
        void Update(Notification notification);
    }
}