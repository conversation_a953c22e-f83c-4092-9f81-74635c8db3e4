﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace OHMC.Core.Models
{
    public class RegimenDosingRegimen : ModelBase
    {
        [Key]
        public int Id { get; set; }
        public int OrderNumber { get; set; }

        [ForeignKey("Regimen")]
        public int RegimenId { get; set; }
        public Regimen Regimen { get; set; }

        [ForeignKey("DosingRegimen")]
        public int DosingRegimenId { get; set; }

        public DosingRegimen DosingRegimen { get; set; }
        [MaxLength(20)]
        public string? JoiningTerm { get; set; }
    }
}
