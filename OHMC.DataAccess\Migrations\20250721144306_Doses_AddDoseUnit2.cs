﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace OHMC.DataAccess.Migrations
{
    public partial class Doses_AddDoseUnit2 : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "<PERSON>se<PERSON>nit2",
                table: "<PERSON>ses<PERSON><PERSON><PERSON>",
                type: "nvarchar(20)",
                maxLength: 20,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "DoseUnit2",
                table: "Doses",
                type: "nvarchar(20)",
                maxLength: 20,
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "<PERSON>se<PERSON>nit2",
                table: "DosesAudi<PERSON>");

            migrationBuilder.DropColumn(
                name: "DoseUnit2",
                table: "<PERSON>ses");
        }
    }
}
