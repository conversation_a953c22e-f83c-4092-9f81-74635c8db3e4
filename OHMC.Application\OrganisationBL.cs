﻿using Microsoft.Extensions.Logging;
using OHMC.Core.Common.Const;
using OHMC.Core.Models;
using OHMC.DataAccess.Interface;

namespace OHMC.Application
{
    public class OrganisationBL : IOrganisationBL
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<OrganisationBL> _logger;

        public OrganisationBL(IUnitOfWork unitOfWork, ILogger<OrganisationBL> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<IEnumerable<Organisation>> GetOrganisationsAsync()
        {
            return await _unitOfWork.OrganisationRepository.GetOrganisationsAsync();
        }

        public async Task<Organisation> GetOrganisationAsync(int id)
        {
            return await _unitOfWork.OrganisationRepository.GetOrganisationAsync(id);
        }

        public async Task<int> UpSert(Organisation organisation, int userId)
        {
            organisation.UpdatedBy = userId;
            organisation.UpdatedDate = DateTime.Now;

            if (organisation.Id > 0)
            {
                _unitOfWork.OrganisationRepository.Update(organisation);
                _logger.LogInformation($"{StrConst.LOG_TYPE_UPDATE}: {organisation.Description}, USERID: {userId}");
            }
            else
            {
                organisation.CreateBy = userId;
                organisation.CreatedDate = DateTime.Now;

                _unitOfWork.OrganisationRepository.Add(organisation);
                _logger.LogInformation($"{StrConst.LOG_TYPE_ADDING}: {organisation.Description}, USERID: {userId}");
            }

            return await SaveAsync();
        }

        public async Task<int> SaveAsync()
        {
            return await _unitOfWork.SaveAsync();
        }
    }
}
