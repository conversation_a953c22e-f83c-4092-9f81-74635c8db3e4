﻿using Microsoft.Extensions.Logging;
using OHMC.Core.Common.Const;
using OHMC.Core.Models;
using OHMC.DataAccess.Interface;

namespace OHMC.Application
{
    public class NetworkMemberBL : INetworkMemberBL
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<NetworkMemberBL> _logger;

        public NetworkMemberBL(IUnitOfWork unitOfWork, ILogger<NetworkMemberBL> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<IEnumerable<NetworkMember>> GetNetworkMembersAsync()
        {
            return await _unitOfWork.NetworkMemberRepository.GetNetworkMembersAsync();
        }

        public async Task<NetworkMember> GetNetworkMemberAsync(int id)
        {
            return await _unitOfWork.NetworkMemberRepository.GetNetworkMemberAsync(id);
        }

        public async Task<NetworkMemberSpeciality> GetNetworkMemberSpecialityAsync(int id)
        {
            return await _unitOfWork.NetworkMemberSpecialityRepository.GetNetworkMemberSpecialityAsync(id);
        }

        public async Task<string> GetSpecialitiesDescription(int networkMemberId)
        {
            return await _unitOfWork.NetworkMemberSpecialityRepository.GetSpecialitiesDescription(networkMemberId);
        }

        public async Task<IEnumerable<NetworkMemberSpeciality>> GetNetworkMemberSpecialitiesAsync(int networkMemberId)
        {
            return await _unitOfWork.NetworkMemberSpecialityRepository.GetNetworkMemberSpecialitiesAsync(networkMemberId);
        }

        public async Task<int> UpSert(NetworkMember member, int userId)
        {
            member.UpdatedBy = userId;
            member.UpdatedDate = DateTime.Now;

            if (member.Id > 0)
            {
                _unitOfWork.NetworkMemberRepository.Update(member);
                _logger.LogInformation($"{StrConst.LOG_TYPE_UPDATE}: {member.Surname}, USERID: {userId}");
            }
            else
            {
                member.CreateBy = userId;
                member.CreatedDate = DateTime.Now;

                _unitOfWork.NetworkMemberRepository.Add(member);
                _logger.LogInformation($"{StrConst.LOG_TYPE_ADDING}: {member.Surname}, USERID: {userId}");
            }

            return await SaveAsync();
        }

        public async Task<int> UpSertSpeciality(NetworkMemberSpeciality speciality, int userId)
        {
            speciality.UpdatedBy = userId;
            speciality.UpdatedDate = DateTime.Now;

            if (speciality.Id > 0)
            {
                _unitOfWork.NetworkMemberSpecialityRepository.Update(speciality);
                _logger.LogInformation($"{StrConst.LOG_TYPE_UPDATE}: {speciality.Speciality}, USERID: {userId}");
            }
            else
            {
                speciality.CreateBy = userId;
                speciality.CreatedDate = DateTime.Now;

                _unitOfWork.NetworkMemberSpecialityRepository.Add(speciality);
                _logger.LogInformation($"{StrConst.LOG_TYPE_ADDING}: {speciality.Speciality}, USERID: {userId}");
            }

            return await SaveAsync();
        }

        public async Task<int> SaveAsync()
        {
            return await _unitOfWork.SaveAsync();
        }
    }
}
