﻿using System.ComponentModel.DataAnnotations;

namespace OHMC.Core.Models
{
    public class ProductAudit : BaseAudit
    {
        [Key]
        public int ProductAuditId { get; set; }
        public int ProductId { get; set; }
        public decimal PackSize { get; set; }
        [MaxLength(50)]
        public string DispensingUnit { get; set; } = string.Empty;
        [MaxLength(20)]
        public string? Nappi9 { get; set; } = string.Empty;
        [MaxLength(500)]
        public string ActiveIngredient { get; set; } = string.Empty;
        [MaxLength(500)]
        public string ProductName { get; set; } = string.Empty;
        [MaxLength(50)]
        public string PrimaryRoute { get; set; } = string.Empty;
        [MaxLength(50)]
        public string DosageFormType { get; set; } = string.Empty;
        [MaxLength(50)]
        public string DosageForm { get; set; } = string.Empty;
        public decimal StrengthValue { get; set; } = decimal.Zero;
        public string StrengthValueUnit { get; set; } = string.Empty;
        public decimal? StrengthVolume { get; set; }
        [MaxLength(50)]
        public string? StrengthVolumeUnit { get; set; } = string.Empty;
        [MaxLength(100)]
        public string StrengthDescription { get; set; } = string.Empty;
        public decimal? SEPExclVat { get; set; } = decimal.Zero;
        public decimal? PricePerPack { get; set; } = decimal.Zero;
        public decimal? PricePerUnit { get; set; } = decimal.Zero;
        public decimal? PricePerDosingUnit { get; set; } = decimal.Zero;
        [MaxLength(500)]
        public string Level3Description { get; set; } = string.Empty;
        public string PresentationDescription { get; set; } = string.Empty;
        [MaxLength(30)]
        public string PacketIntake { get; set; } = string.Empty;
        [MaxLength(40)]
        public string? IsRegistered { get; set; } = string.Empty;
        public string SEPStatus { get; set; } = string.Empty;
        public DateTime? SEPTempDate { get; set; }
        public string SEPPermanent { get; set; } = string.Empty;
        public bool IsAvailable { get; set; } = true;
        public string Manufacturer { get; set; } = string.Empty;
        public bool IsRequireDiluent { get; set; } = false;
        public bool IsDiluentIncluded { get; set; } = false;
        public bool IsBreakBulk { get; set; } = false;
        public bool IsSeries { get; set; } = false;
        [MaxLength(50)]
        public string AdministrationUnit { get; set; } = string.Empty;
        [MaxLength(20)]
        public string? DoseUnitName { get; set; }
        public int FormulationId { get; set; }
        public int? Formulation2Id { get; set; }
        public decimal? StandardPackSize { get; set; }
        public decimal? TempSEPExclVat { get; set; } = decimal.Zero;
        public decimal? TempPricePerPack { get; set; } = decimal.Zero;
        public decimal? TempPricePerUnit { get; set; } = decimal.Zero;
        public decimal? TempPricePerDosingUnit { get; set; } = decimal.Zero;
        [MaxLength(100)]
        public string? PriceChangeComment { get; set; }
        public bool? IsFDCCombo { get; set; }
    }
}
