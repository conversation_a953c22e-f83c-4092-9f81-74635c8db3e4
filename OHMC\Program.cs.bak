using FoolProof.Core;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Localization;
using Microsoft.EntityFrameworkCore;
using OHMC.Application;
using OHMC.Core.Models.Settings;
using OHMC.DataAccess;
using OHMC.DataAccess.Interface;
using OHMC.DataAccess.Repository;
using Serilog;
using System.Globalization;

var builder = WebApplication.CreateBuilder(args);
// Add serilog services to the container and read config from appsettings
builder.Host.UseSerilog((context, configuration) =>
    configuration.ReadFrom.Configuration(context.Configuration));

IConfiguration Configuration = builder.Configuration;

builder.Services.AddDbContext<ApplicationDbContext>(options =>
{
    options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnection"));

});

builder.Services.AddAuthentication(CookieAuthenticationDefaults.AuthenticationScheme)
    .AddCookie(opt => opt.LoginPath = "/Account/SignIn");

builder.Services.Configure<ReportConfig>(Configuration.GetSection("ReportServerSettings"));

builder.Services.AddScoped<IUserServiceRepository, UserServiceRepository>();
builder.Services.AddScoped<IHasher, Hasher>();
builder.Services.AddScoped<IINNRepository, INNRepository>();
builder.Services.AddScoped<ICancerGroupRepository, CancerGroupRepository>();
builder.Services.AddScoped<ICancerGroupTypeRepository, CancerGroupTypeRepository>();
builder.Services.AddScoped<IEvidenceRepository, EvidenceRepository>();
builder.Services.AddScoped<IRouteRepository, RouteRepository>();
builder.Services.AddScoped<IStrengthRepository, StrengthRepository>();
builder.Services.AddScoped<IDispensingUnitRepository, DispensingUnitRepository>();
builder.Services.AddScoped<IFixedSettingsRepository, FixedSettingsRepository>();
builder.Services.AddScoped<IDoseUnitRepository, DoseUnitRepository>();
builder.Services.AddScoped<IFormTypeRepository, FormTypeRepository>();
builder.Services.AddScoped<IDosageFormRepository, DosageFormRepository>();
builder.Services.AddScoped<IFormulationRepository, FormulationRepository>();
builder.Services.AddScoped<IFormulationRoutesRepository, FormulationRoutesRepository>();
builder.Services.AddScoped<IProductRepository, ProductRepository>();
builder.Services.AddScoped<IProductRoutesRepository, ProductRoutesRepository>();
builder.Services.AddScoped<IDoseRepository, DoseRepository>();
builder.Services.AddScoped<IDoseProductRepository, DoseProductRepository>();
builder.Services.AddScoped<IMedPraxProductRepository, MedPraxProductRepository>();
builder.Services.AddScoped<IDosingRegimenRepository, DosingRegimenRepository>();
builder.Services.AddScoped<IRegimenRepository, RegimenRepository>();
builder.Services.AddScoped<IRegimenDosingRegimenRepository, RegimenDosingRegimenRepository>();
builder.Services.AddScoped<IProtocolRepository, ProtocolRepository>();
builder.Services.AddScoped<IProtocolStatusRepository, ProtocolStatusRepository>();
builder.Services.AddScoped<IProtocolEvidenceRepository, ProtocolEvidenceRepository>();
builder.Services.AddScoped<IProtocolTreatmentIntentRepository, ProtocolTreatmentIntentRepository>();
builder.Services.AddScoped<IChapterRepository, ChapterRepository>();
builder.Services.AddScoped<IModalityRepository, ModalityRepository>();
builder.Services.AddScoped<INDODProductRepository, NDODProductRepository>();
builder.Services.AddScoped<IUnitOfWork, UnitOfWork>();

builder.Services.AddScoped<ICancerGroupsBL, CancerGroupsBL>();
builder.Services.AddScoped<IINNBL, INNBL>();
builder.Services.AddScoped<IEvidenceBL, EvidenceBL>();
builder.Services.AddScoped<IRoutesBL, RoutesBL>();
builder.Services.AddScoped<IStrengthsBL, StrengthsBL>();
builder.Services.AddScoped<IDispensingUnitBL, DispensingUnitBL>();
builder.Services.AddScoped<IFixedSettingsBL, FixedSettingsBL>();
builder.Services.AddScoped<IDoseUnitsBL, DoseUnitsBL>();
builder.Services.AddScoped<IFormTypeBL, FormTypeBL>();
builder.Services.AddScoped<IDosageFormBL, DosageFormBL>();
builder.Services.AddScoped<IFormulationBL, FormulationBL>();
builder.Services.AddScoped<IFormulationRoutesBL, FormulationRoutesBL>();
builder.Services.AddScoped<IProductBL, ProductBL>();
builder.Services.AddScoped<IProductRoutesBL, ProductRoutesBL>();
builder.Services.AddScoped<IDoseBL, DoseBL>();
builder.Services.AddScoped<IDosingRegimenBL, DosingRegimenBL>();
builder.Services.AddScoped<IRegimenBL, RegimenBL>();
builder.Services.AddScoped<IProtocolBL, ProtocolBL>();
builder.Services.AddScoped<IChapterBL, ChapterBL>();
builder.Services.AddScoped<IModalityBL, ModalityBL>();
builder.Services.AddScoped<IMedPraxProductsBL, MedPraxProductsBL>();
builder.Services.AddScoped<INDOHProductsBL, NDOHProductsBL>();
builder.Services.AddScoped<IPrintServiceBL, PrintServiceBL>();
builder.Services.AddFoolProof();

//localization
builder.Services.AddLocalization();

// Add services to the container.
builder.Services.AddControllersWithViews();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Home/Error");
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseSerilogRequestLogging();
app.UseHttpsRedirection();

var supportedCultures = new[]
{
 new CultureInfo("en-US")
};

app.UseRequestLocalization(new RequestLocalizationOptions
{
    DefaultRequestCulture = new RequestCulture("en-US"),
    // Formatting numbers, dates, etc.
    SupportedCultures = supportedCultures,
    // UI strings that we have localized.
    SupportedUICultures = supportedCultures
});

app.UseStaticFiles();

app.UseAuthentication();
app.UseRouting();

app.UseAuthorization();

app.MapControllerRoute(
    name: "default",
    pattern: "{controller=Home}/{action=Index}/{id?}");

app.Run();
