﻿using System.ComponentModel.DataAnnotations;

namespace OHMC.Core.Models
{
    public class DosingRegimenAudit : BaseAudit
    {
        [Key]
        public int DosingRegimenAuditId { get; set; }
        public int DosingRegimenId { get; set; }
        [MaxLength(20)]
        public string RegimenCode { get; set; } = string.Empty;
        [MaxLength(500)]
        public string DosingRegimenName { get; set; } = string.Empty;
        public int INNId { get; set; }
        [MaxLength(10)]
        public string Route { get; set; } = string.Empty;
        public double DoseValue { get; set; }
        public string? DoseDescriptionText { get; set; }
        public int? DoseId { get; set; }
        [MaxLength(100)]
        public string? DrugDays { get; set; }
        public int NumberOfDaysPatientDosed { get; set; }
        public double? CostPerRegimen { get; set; }
        [MaxLength(500)]
        public string? Comment { get; set; }
        [MaxLength(500)]
        public string? CalculatedRegimenText { get; set; }
        public bool UseAsCalc { get; set; }
        [MaxLength(500)]
        public string? ModifiedCalculatedRegimenText { get; set; }
    }
}
