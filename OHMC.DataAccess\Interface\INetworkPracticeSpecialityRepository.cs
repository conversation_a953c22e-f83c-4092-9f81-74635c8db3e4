﻿using OHMC.Core.Models;

namespace OHMC.DataAccess.Interface
{
    public interface INetworkPracticeSpecialityRepository
    {
        void Add(NetworkPracticeSpeciality speciality);
        Task<IEnumerable<NetworkPracticeSpeciality>> GetNetworkPracticeSpecialitiesAsync(int networkMemberId);
        Task<NetworkPracticeSpeciality> GetNetworkPracticeSpecialityAsync(int id);
        Task<string> GetSpecialitiesDescription(int networkMemberId);
        void Update(NetworkPracticeSpeciality speciality);
    }
}