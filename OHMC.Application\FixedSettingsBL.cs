﻿using Microsoft.Extensions.Logging;
using OHMC.Core.Common.Const;
using OHMC.Core.Models;
using OHMC.DataAccess.Interface;

namespace OHMC.Application
{
    public class FixedSettingsBL : IFixedSettingsBL
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<FixedSettingsBL> _logger;

        public FixedSettingsBL(IUnitOfWork unitOfWork, ILogger<FixedSettingsBL> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<FixedSetting> GetFixedSettingAsync()
        {
            return await _unitOfWork.FixedSettingsRepository.GetFixedSettingAsync();
        }

        public async Task<int> UpSert(FixedSetting setting, int userId)
        {
            setting.UpdatedBy = userId;
            setting.UpdatedDate = DateTime.Now;

            if (setting.Id > 0)
            {
                _unitOfWork.FixedSettingsRepository.Update(setting);
                _logger.LogInformation($"{StrConst.LOG_TYPE_UPDATE}: {setting.BSA}, {setting.Weight}, USERID: {userId}");
            }
            else
            {
                setting.CreateBy = userId;
                setting.CreatedDate = DateTime.Now;

                _unitOfWork.FixedSettingsRepository.Add(setting);
                _logger.LogInformation($"{StrConst.LOG_TYPE_ADDING}: {setting.Id}, {setting.Weight}, USERID: {userId}");
            }

            return await SaveAsync();
        }

        public async Task<int> SaveAsync()
        {
            return await _unitOfWork.SaveAsync();
        }
    }
}
