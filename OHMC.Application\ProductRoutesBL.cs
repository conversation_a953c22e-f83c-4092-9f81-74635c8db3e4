﻿using Microsoft.Extensions.Logging;
using OHMC.Core.Common.Const;
using OHMC.Core.Models;
using OHMC.DataAccess.Interface;

namespace OHMC.Application
{
    public class ProductRoutesBL : IProductRoutesBL
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<ProductRoutesBL> _logger;

        public ProductRoutesBL(IUnitOfWork unitOfWork, ILogger<ProductRoutesBL> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<ProductRoute> GetProductRoute(int id)
        {
            return await _unitOfWork.ProductRoutesRepository.GetProductRouteAsync(id);
        }

        public async Task<IEnumerable<ProductRoute>> GetProductRoutesByProductAsync(int productId)
        {
            return await _unitOfWork.ProductRoutesRepository.GetProductRoutesByProductAsync(productId);
        }

        public async Task<int> UpSert(ProductRoute productRoute, int userId)
        {
            productRoute.UpdatedBy = userId;
            productRoute.UpdatedDate = DateTime.Now;

            if (productRoute.Id > 0)
            {
                _unitOfWork.ProductRoutesRepository.Update(productRoute);
                _logger.LogInformation($"{StrConst.LOG_TYPE_UPDATE}: {productRoute.RouteName}, USERID: {userId}");
            }
            else
            {
                productRoute.CreateBy = userId;
                productRoute.CreatedDate = DateTime.Now;

                _unitOfWork.ProductRoutesRepository.Add(productRoute);
                _logger.LogInformation($"{StrConst.LOG_TYPE_ADDING}: {productRoute.RouteName}, USERID: {userId}");
            }

            return await SaveAsync();
        }

        public async Task<int> SaveAsync()
        {
            return await _unitOfWork.SaveAsync();
        }
    }
}
