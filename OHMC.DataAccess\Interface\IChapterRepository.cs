﻿using OHMC.Core.Models;

namespace OHMC.DataAccess.Interface
{
    public interface IChapterRepository
    {
        void Add(Chapter chapter);
        Task<Chapter> GetChapterAsync(int id);
        Task<IEnumerable<Chapter>> GetChaptersAsync();
        Task<IEnumerable<Chapter>> GetChaptersByPTCType(int ptcTypeId);
        Task<IEnumerable<Chapter>> GetChaptersByFormularyType(int formularyTypeId);
        Task<IEnumerable<Chapter>> SearchChaptersAsync(string desc);
        void Update(Chapter chapter);
    }
}