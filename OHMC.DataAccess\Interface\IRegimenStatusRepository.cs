﻿using OHMC.Core.Models;

namespace OHMC.DataAccess.Interface
{
    public interface IRegimenStatusRepository
    {
        void Add(RegimenStatus regimenStatus);
        Task<RegimenStatus> GetLatestRegimenStatusAsync(int regimenId);
        Task<RegimenStatus> GetRegimenStatusAsync(int id);
        Task<IEnumerable<RegimenStatus>> GetRegimenStatusesAsync(int regimenId);
        void Update(RegimenStatus regimenStatus);
    }
}