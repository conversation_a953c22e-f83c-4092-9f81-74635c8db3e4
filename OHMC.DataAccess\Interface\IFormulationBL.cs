﻿using OHMC.Core.Common.Response;
using OHMC.Core.Models;

namespace OHMC.DataAccess.Interface
{
    public interface IFormulationBL
    {
        Task<Formulation> GetFormulationAsync(int id);
        Task<Formulation> GetFormulationOnlyAsync(int id);
        Task<IEnumerable<Formulation>> GetFormulationsAsync();
        Task<IEnumerable<Formulation>> GetFormulationsSimpleAsync();
        Task<IEnumerable<Formulation>> GetPagedFormulationsAsync(int pageNumber, int pageSize);
        Task<int> GetFormulationCountAsync();
        Task<int> SaveAsync();
        Task<int> UpSert(Formulation formulation, int userId);
        Task<IEnumerable<Formulation>> GetFormulationsByINNAsync(string inn);
        Task<int> AddRoute(int formulationId, Route route, int userId);
        Task<IEnumerable<Formulation>> SearchFormulationsAsync(string searchString);
        Task<GeneralResponse> Delete(int id, int userId);
        Task<CopyFormulationResponse> CopyFormulationAsync(int id, int userId);
        Task<bool> IsFormulationDuplicate(int id, string dosageFormDescriptionCalc);
    }
}