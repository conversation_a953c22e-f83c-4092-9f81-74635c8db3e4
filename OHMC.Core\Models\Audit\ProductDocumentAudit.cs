﻿using System.ComponentModel.DataAnnotations;

namespace OHMC.Core.Models.Audit
{
    public class ProductDocumentAudit : BaseAudit
    {
        [Key]
        public int ProductDocumentAuditId { get; set; }
        public int ProductDocumentId { get; set; }
        [MaxLength(200)]
        public string Description { get; set; } = string.Empty;
        [MaxLength(200)]
        public string FileName { get; set; } = string.Empty;
        [MaxLength(30)]
        public string DocumentType { get; set; } = string.Empty;
        [MaxLength(200)]
        public string? FileLink { get; set; }
        public int ProductId { get; set; }
    }
}
