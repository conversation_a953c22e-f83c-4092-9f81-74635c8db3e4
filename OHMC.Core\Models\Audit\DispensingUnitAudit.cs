﻿using System.ComponentModel.DataAnnotations;

namespace OHMC.Core.Models
{
    public class DispensingUnitAudit : BaseAudit
    {
        [Key]
        public int DispensingUnitAuditId { get; set; }
        public int DispensingUnitId { get; set; }
        public int AdminUnitQty { get; set; }
        [MaxLength(50)]
        public string AdminUnit { get; set; } = string.Empty;
        [MaxLength(50)]
        public string AdminUnitType { get; set; } = string.Empty;
    }
}
