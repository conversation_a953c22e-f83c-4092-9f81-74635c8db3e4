﻿using Microsoft.Extensions.Logging;
using OHMC.Core.Common.Const;
using OHMC.Core.Models;
using OHMC.DataAccess.Interface;

namespace OHMC.Application
{
    public class OutOfStockInternationalSignalsBL : IOutOfStockInternationalSignalsBL
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<OutOfStockInternationalSignalsBL> _logger;

        public OutOfStockInternationalSignalsBL(IUnitOfWork unitOfWork, ILogger<OutOfStockInternationalSignalsBL> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<IEnumerable<OutOfStockInternationalSignal>> GetOutOfStockInternationalSignalsAsync()
        {
            return await _unitOfWork.OutOfStockInternationalSignalRepository.GetOutOfStockInternationalSignalsAsync();
        }

        public async Task<OutOfStockInternationalSignal> GetOutOfStockInternationalSignal(int id)
        {
            return await _unitOfWork.OutOfStockInternationalSignalRepository.GetOutOfStockInternationalSignalAsync(id);
        }

        public async Task<int> UpSert(OutOfStockInternationalSignal signal, int userId)
        {
            signal.UpdatedBy = userId;
            signal.UpdatedDate = DateTime.Now;

            if (signal.Id > 0)
            {
                _unitOfWork.OutOfStockInternationalSignalRepository.Update(signal);
                _logger.LogInformation($"{StrConst.LOG_TYPE_UPDATE}: {signal.Description}, USERID: {userId}");
            }
            else
            {
                signal.CreateBy = userId;
                signal.CreatedDate = DateTime.Now;

                _unitOfWork.OutOfStockInternationalSignalRepository.Add(signal);
                _logger.LogInformation($"{StrConst.LOG_TYPE_ADDING}: {signal.Description}, USERID: {userId}");
            }

            return await SaveAsync();
        }

        public async Task<int> SaveAsync()
        {
            return await _unitOfWork.SaveAsync();
        }
    }
}
