﻿using OHMC.Core.Models;

namespace OHMC.DataAccess.Interface
{
    public interface IFormularySchemeRuleRepository
    {
        void Add(FormularySchemeRule rule);
        Task<FormularySchemeRule> GetFormularySchemeRuleAsync(int id);
        Task<IEnumerable<FormularySchemeRule>> GetFormularySchemeRulesAsync();
        Task<IEnumerable<FormularySchemeRule>> GetFormularySchemeRulesFullAsync();
        void Update(FormularySchemeRule rule);
    }
}