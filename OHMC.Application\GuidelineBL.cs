﻿using Microsoft.Extensions.Logging;
using OHMC.Core.Common.Const;
using OHMC.Core.Models;
using OHMC.DataAccess.Interface;

namespace OHMC.Application
{
    public class GuidelineBL : IGuidelineBL
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<GuidelineBL> _logger;

        public GuidelineBL(IUnitOfWork unitOfWork, ILogger<GuidelineBL> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<IEnumerable<Guideline>> GetGuidelinesAsync()
        {
            return await _unitOfWork.GuidelineRepository.GetGuidelinesAsync();
        }

        public async Task<Guideline> GetGuidelineAsync(int id)
        {
            return await _unitOfWork.GuidelineRepository.GetGuidelineAsync(id);
        }

        public async Task<int> UpSert(Guideline guideline, int userId)
        {
            guideline.UpdatedBy = userId;
            guideline.UpdatedDate = DateTime.Now;
            guideline.GuidelineDisplayName = (await _unitOfWork.OrganisationRepository.GetOrganisationAsync(guideline.OrganisationId)).Abbreviation + " " + guideline.GuidelineName + " " + guideline.Version;

            if (guideline.Id > 0)
            {
                _unitOfWork.GuidelineRepository.Update(guideline);
                _logger.LogInformation($"{StrConst.LOG_TYPE_UPDATE}: {guideline.GuidelineName}, USERID: {userId}");
            }
            else
            {
                guideline.CreateBy = userId;
                guideline.CreatedDate = DateTime.Now;

                _unitOfWork.GuidelineRepository.Add(guideline);
                _logger.LogInformation($"{StrConst.LOG_TYPE_ADDING}: {guideline.GuidelineName}, USERID: {userId}");
            }

            return await SaveAsync();
        }

        public async Task<int> SaveAsync()
        {
            return await _unitOfWork.SaveAsync();
        }

        public async Task<IEnumerable<Guideline>> GetOrganisationGuidelinesAsync(int organisationId)
        {
            return await _unitOfWork.GuidelineRepository.GetOrganisationGuidelinesAsync(organisationId);
        }
    }
}
