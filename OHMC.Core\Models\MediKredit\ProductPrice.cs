﻿using System.ComponentModel.DataAnnotations;

namespace OHMC.Core.Models.MediKredit
{
    public class ProductPrice : BaseMediKreditRecord
    {
        [StringLength(7)]
        public string NAPPIProductCode { get; set; } = string.Empty;
        [StringLength(3)]
        public string NAPPISuffix { get; set; } = string.Empty;
        [StringLength(9)]
        public string ProductPackSize { get; set; } = string.Empty;
        [StringLength(9)]
        public string WholeSalePrice { get; set; } = string.Empty;
        [StringLength(8)]
        public string RetailPrice { get; set; } = string.Empty;
        [StringLength(14)]
        public string EANProductCode { get; set; } = string.Empty;
        [StringLength(8)]
        public string PriceEffectiveDate { get; set; } = string.Empty;
        [StringLength(8)]
        public string PriceTerminationDate { get; set; } = string.Empty;
        [StringLength(1)]
        public string SchedulePrefix { get; set; } = string.Empty;
        [StringLength(2)]
        public string Schedule { get; set; } = string.Empty;
        [StringLength(2)]
        public string CountryCode { get; set; } = string.Empty;
        [StringLength(2)]
        public string SectorID { get; set; } = string.Empty;
        [StringLength(81)]
        public string Filler1 { get; set; } = string.Empty;
        [StringLength(9)]
        public string FullWholesalePrice { get; set; } = string.Empty;
        [StringLength(9)]
        public string FullRetailPrice { get; set; } = string.Empty;
        [StringLength(60)]
        public string FullProductName { get; set; } = string.Empty;
        [StringLength(8)]
        public string PriceUpdateDate { get; set; } = string.Empty;
        [StringLength(6)]
        public string PriceUpdateTime { get; set; } = string.Empty;
    }
}
