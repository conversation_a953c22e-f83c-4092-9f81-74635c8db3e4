﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace OHMC.Core.Models.Audit
{
    public class ChapterAudit : BaseAudit
    {
        [Key]
        public int ChapterAuditId { get; set; }
        public int ChapterId { get; set; }
        [Required, MaxLength(200)]
        public string ChapterDescription { get; set; } = string.Empty;
        public int FormularyTypeId { get; set; }
        public int OrderNumber { get; set; }
        public int? PrintOrder { get; set; }
        public int? PTCTypeId { get; set; }
    }
}
