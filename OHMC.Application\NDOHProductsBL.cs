﻿using OHMC.Core.Models;
using OHMC.DataAccess.Interface;

namespace OHMC.Application
{
    public class NDOHProductsBL : INDOHProductsBL
    {
        private readonly IUnitOfWork _unitOfWork;

        public NDOHProductsBL(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<NDOHProduct> GetNDODProductAsync(string nappi)
        {
            return await _unitOfWork.NDODProductRepository.GetNDOHProductAsync(nappi);
        }

        public async Task<IEnumerable<NDOHProduct>> GetNDODProductsAsync()
        {
            return await _unitOfWork.NDODProductRepository.GetNDOHProductsAsync();
        }

        public async Task<IEnumerable<NDOHProduct>> GetNDODProductsPagedAsync(int pageNumber, int pageSize)
        {
            return await _unitOfWork.NDODProductRepository.GetNDOHProductsPagedAsync(pageNumber, pageSize);
        }

        public async Task<IEnumerable<NDOHProduct>> SearchProductsAsync(string searchString)
        {
            return await _unitOfWork.NDODProductRepository.SearchProductsAsync(searchString);
        }
    }
}
