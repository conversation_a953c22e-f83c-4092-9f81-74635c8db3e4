﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Bogus" Version="35.6.1" />
    <PackageReference Include="FluentAssertions" Version="6.12.0" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.11.1" />
    <PackageReference Include="Moq" Version="4.20.72" />
    <PackageReference Include="MSTest.TestAdapter" Version="3.5.2" />
    <PackageReference Include="MSTest.TestFramework" Version="3.5.2" />
    <PackageReference Include="Selenium.Support" Version="4.28.0" />
    <PackageReference Include="Selenium.WebDriver" Version="4.28.0" />
    <PackageReference Include="Selenium.WebDriver.ChromeDriver" Version="133.0.6943.5300" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\OHMC.Application\OHMC.Application.csproj" />
    <ProjectReference Include="..\OHMC.DataAccess\OHMC.DataAccess.csproj" />
  </ItemGroup>

</Project>
