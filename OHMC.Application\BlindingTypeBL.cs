﻿using Microsoft.Extensions.Logging;
using OHMC.Core.Common.Const;
using OHMC.Core.Models;
using OHMC.DataAccess.Interface;

namespace OHMC.Application
{
    public class BlindingTypeBL : IBlindingTypeBL
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<BlindingTypeBL> _logger;
        public BlindingTypeBL(IUnitOfWork unitOfWork, ILogger<BlindingTypeBL> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<IEnumerable<BlindingType>> GetBlindingTypesAsync()
        {
            return await _unitOfWork.BlindingTypeRepository.GetBlindingTypesAsync();
        }

        public async Task<BlindingType> GetBlindingType(int id)
        {
            return await _unitOfWork.BlindingTypeRepository.GetBlindingTypeAsync(id);
        }

        public async Task<int> UpSert(BlindingType blinding, int userId)
        {
            blinding.UpdatedBy = userId;
            blinding.UpdatedDate = DateTime.Now;

            if (blinding.Id > 0)
            {
                _unitOfWork.BlindingTypeRepository.Update(blinding);
                _logger.LogInformation($"{StrConst.LOG_TYPE_UPDATE}: {blinding.BlindingName}, USERID: {userId}");
            }
            else
            {
                blinding.CreateBy = userId;
                blinding.CreatedDate = DateTime.Now;

                _unitOfWork.BlindingTypeRepository.Add(blinding);
                _logger.LogInformation($"{StrConst.LOG_TYPE_ADDING}: {blinding.BlindingName}, USERID: {userId}");
            }

            return await SaveAsync();
        }

        public async Task<int> SaveAsync()
        {
            return await _unitOfWork.SaveAsync();
        }
    }
}
