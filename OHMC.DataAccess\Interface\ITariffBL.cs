﻿using OHMC.Core.Common.Response;
using OHMC.Core.Models;

namespace OHMC.DataAccess.Interface
{
    public interface ITariffBL
    {
        Task<TariffPeriod> GetCurrentTarifPeriodAsync(int tariffId);
        Task<Tariff> GetTariffAsync(int id);
        Task<IEnumerable<TariffPeriod>> GetTariffPeriodAsync();
        Task<TariffPeriod> GetTariffPeriodAsync(int id);
        Task<List<TariffPeriod>> GetTariffPeriodsByTariffAsync(int tariffId);
        Task<IEnumerable<Tariff>> GetTariffsAsync();
        Task<int> RemoveTariffAsync(int id, int userId);
        Task<int> RemoveTariffPeriodAsync(int id, int userId);
        Task<int> SaveAsync();
        Task<UpSertTariffResponse> UpSertTariffAsync(Tariff tariff, int userId);
        Task<UpSertTariffPeriodResponse> UpSertTariffPeriodAsync(TariffPeriod period, int userId);
        Task<IncreaseTariffResponse> IncreaseTariffAsync(int year, decimal percentageIncrease, int? tariffTypeId, int userId);
        Task<UpdateProfessionalFeesToCurrentResponse> UpdateProfessionalFeesAsync();
    }
}