﻿using System.ComponentModel.DataAnnotations;

namespace OHMC.Core.Models
{
    public class NDOHProduct
    {
        [Key]
        public int Id { get; set; }
        [MaxLength(100)]
        public string? ApplicantsMCCSAHPRALicenceNo { get; set; } = string.Empty;
        [MaxLength(500)]
        public string? ApplicantName { get; set; } = string.Empty;
        [MaxLength(100)]
        public string? MCCSAHPRAMedicineRegNo { get; set; } = string.Empty;
        [MaxLength(50)]
        public string? NappiCode { get; set; } = string.Empty;
        [MaxLength(20)]
        public string? ATC4 { get; set; } = string.Empty;
        [MaxLength(10)]
        public string? MedicineSchedule { get; set; } = string.Empty;
        [MaxLength(500)]
        public string? MedicineName { get; set; } = string.Empty;
        [MaxLength(500)]
        public string? ActiveIngredients { get; set; } = string.Empty;
        public decimal? Strength { get; set; }
        [MaxLength(100)]
        public string? Unit { get; set; } = string.Empty;
        [MaxLength(100)]
        public string? DosageForm { get; set; } = string.Empty;
        public decimal? PackSize { get; set; }
        public decimal? Quantity { get; set; }
        public decimal? ManufacturerPrice { get; set; }
        [MaxLength(20)]
        public string? LogisticsFee { get; set; } = string.Empty;
        public decimal? VAT { get; set; }
        public decimal? SEP { get; set; }
        public decimal? UnitPrice { get; set; }
        [MaxLength(20)]
        public string? EffectiveDate { get; set; } = string.Empty;
        [MaxLength(500)]
        public string? Status { get; set; } = string.Empty;
        [MaxLength(100)]
        public string? OriginatorOrGeneric { get; set; } = string.Empty;
        public DateTime UploadedDate { get; set; }
        public DateTime FileDate { get; set; }
    }
}
