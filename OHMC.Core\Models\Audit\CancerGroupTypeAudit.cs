﻿using System.ComponentModel.DataAnnotations;

namespace OHMC.Core.Models
{
    public class CancerGroupTypeAudit : BaseAudit
    {
        [Key]
        public int CancerGroupTypeAuditId { get; set; }
        public int CancerGroupTypeId { get; set; }
        [MaxLength(20)]
        public string Code { get; set; } = string.Empty;
        [MaxLength(100)]
        public string GroupName { get; set; } = string.Empty;
        [MaxLength(200)]
        public string CancerType { get; set; } = string.Empty;
        public int? PrintOrder { get; set; }
    }
}
