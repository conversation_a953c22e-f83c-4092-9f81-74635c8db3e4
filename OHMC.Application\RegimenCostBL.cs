﻿using Microsoft.Extensions.Logging;
using OHMC.Core.Common.Const;
using OHMC.Core.Models;
using OHMC.DataAccess.Interface;

namespace OHMC.Application
{
    public class RegimenCostBL : IRegimenCostBL
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<RegimenCostBL> _logger;

        public RegimenCostBL(IUnitOfWork unitOfWork, ILogger<RegimenCostBL> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<RegimenCost> GetRegimenCostAsync(int id)
        {
            return await _unitOfWork.RegimenCostRepository.GetRegimenCostAsync(id);
        }

        public async Task<IEnumerable<RegimenCost>> GetRegimenCostsByRegimenAsync(int regimenId)
        {
            return await _unitOfWork.RegimenCostRepository.GetRegimenCostsByRegimenAsync(regimenId);
        }

        public async Task<int> RemoveRegimenCost(int id, int userId)
        {
            var cost = await _unitOfWork.RegimenCostRepository.GetRegimenCostAsync(id);
            cost.UpdatedBy = userId;
            cost.UpdatedDate = DateTime.Now;
            cost.Deleted = true;
            cost.DeleteOn = DateTime.Now;
            _unitOfWork.RegimenCostRepository.Remove(cost);
            _logger.LogInformation($"{StrConst.LOG_TYPE_REMOVING}: {cost.Id}, USERID: {userId}");
            return await _unitOfWork.SaveAsync();
        }

        public async Task<int> UpSert(RegimenCost cost, int userId)
        {
            cost.UpdatedBy = userId;
            cost.UpdatedDate = DateTime.Now;

            if (cost.Id > 0)
            {
                _unitOfWork.RegimenCostRepository.Update(cost);
                _logger.LogInformation($"{StrConst.LOG_TYPE_UPDATE}: {cost.Id}, USERID: {userId}");
            }
            else
            {
                cost.CreateBy = userId;
                cost.CreatedDate = DateTime.Now;

                _unitOfWork.RegimenCostRepository.Add(cost);
                _logger.LogInformation($"{StrConst.LOG_TYPE_ADDING}: {cost.Id}, USERID: {userId}");
            }

            return await SaveAsync();
        }

        public async Task<int> SaveAsync()
        {
            return await _unitOfWork.SaveAsync();
        }
    }
}
