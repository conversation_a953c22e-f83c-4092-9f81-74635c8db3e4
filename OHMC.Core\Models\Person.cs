﻿using System.ComponentModel.DataAnnotations;

namespace OHMC.Core.Models
{
    public class Person : ModelBase
    {
        [MaxLength(20)]
        public string? Title { get; set; }
        [MaxLength(100)]
        public string FirstName { get; set; } = string.Empty;
        [MaxLength(100)]
        public string Surname { get; set; } = string.Empty;
        [MaxLength(10)]
        public string? Initials { get; set; } = string.Empty;
        [MaxLength(20)]
        public string? IDNumber { get; set; }
        [MaxLength(200)]
        public string? Email { get; set; }
        [MaxLength(20)]
        public string? Telephone1 { get; set; }
        [MaxLength(20)]
        public string? Telephone2 { get; set; }
        [MaxLength(500)]
        public string? Address { get; set; }
        [MaxLength(200)]
        public string? Town { get; set; }
        [MaxLength(20)]
        public string? Province { get; set; }

    }
}
