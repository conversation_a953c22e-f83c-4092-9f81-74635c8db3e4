﻿using System.ComponentModel.DataAnnotations;

namespace OHMC.Core.Models.MediKredit
{
    public class PublicDomainProductDetail : BaseMediKreditRecord
    {
        [StringLength(7)]
        public string NAPPIProductCode { get; set; } = string.Empty;
        [StringLength(3)]
        public string NAPPISuffix { get; set; } = string.Empty;
        [StringLength(40)]
        public string ProductName { get; set; } = string.Empty;
        [StringLength(15)]
        public string ProductStrength { get; set; } = string.Empty;
        [StringLength(5)]
        public string DosageFormCode { get; set; } = string.Empty;
        [StringLength(9)]
        public string ProductPackSize { get; set; } = string.Empty;
        [StringLength(5)]
        public string ManufacturerCode { get; set; } = string.Empty;
        [StringLength(14)]
        public string EANProductCode { get; set;} = string.Empty;
        [StringLength(30)]
        public string CatalogueNumber { get; set;} = string.Empty;
        [StringLength(1)]
        public string ProductType { get; set; } = string.Empty;
        [StringLength(2)]
        public string NumberOfUses { get; set; } = string.Empty;
        [StringLength(41)]
        public string Filler1 { get; set; } = string.Empty;
        [StringLength(60)]
        public string FullProductName { get; set; } = string.Empty;
        [StringLength(8)]
        public string RecordUpdateDate { get; set; } = string.Empty;
        [StringLength(6)]
        public string RecordUpdateTime { get; set; } = string.Empty;
    }
}
