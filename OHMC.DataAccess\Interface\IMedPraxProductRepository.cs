﻿using OHMC.Core.Models;

namespace OHMC.DataAccess.Interface
{
    public interface IMedPraxProductRepository
    {
        Task<MedPraxProduct> GetMedPraxProductAsync(string nappi9);
        Task<IEnumerable<MedPraxProduct>> GetMedPraxProductsByInnAsync(string inn);
        Task<IEnumerable<MedPraxProduct>> SearchMedPraxProductsByProductNameAsync(string searchProductName);
        Task<IEnumerable<MedPraxProduct>> GetMedPraxProductsByInnStrengthAsync(string inn, string strength, string inn2 = null, string strength2 = null);
        Task<IEnumerable<MedPraxProduct>> GetPagedMedPraxProductAsync(int pageNumber, int pageSize);
    }
}