﻿using System.ComponentModel.DataAnnotations;

namespace OHMC.Core.Models
{
    public class RouteAudit : BaseAudit
    {
        [Key]
        public int RouteAuditId { get; set; }
        public int RouteId { get; set; }
        [MaxLength(10)]
        public string RouteName { get; set; } = string.Empty;
        [MaxLength(30)]
        public string RouteDescription { get; set; } = string.Empty;
    }
}
