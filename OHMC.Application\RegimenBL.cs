﻿using Microsoft.Extensions.Logging;
using OHMC.Core.Common.Const;
using OHMC.Core.Common.Response;
using OHMC.Core.Common.Utils;
using OHMC.Core.Models;
using OHMC.DataAccess.Interface;

namespace OHMC.Application
{
    public class RegimenBL : IRegimenBL
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<RegimenBL> _logger;
        private const int FILLER_COUNT = 5;
        public RegimenBL(IUnitOfWork unitOfWork, ILogger<RegimenBL> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<IEnumerable<Regimen>> GetRegimenAsync()
        {
            return await _unitOfWork.RegimenRepository.GetRegimensAsync();
        }

        public async Task<IEnumerable<Regimen>> GetPagedRegimensAsync(int pageNumber, int pageSize)
        {
            return await _unitOfWork.RegimenRepository.GetPagedRegimensAsync(pageNumber, pageSize);
        }

        public async Task<int> GetRegimenCountAsync()
        {
            return await _unitOfWork.RegimenRepository.GetRegimenCountAsync();
        }

        public async Task<IEnumerable<Regimen>> SearchRegimenAsync(string desc)
        {
            return await _unitOfWork.RegimenRepository.SearchRegimensAsync(desc);
        }

        public async Task<Regimen> GetRegimenAsync(int id)
        {
            return await _unitOfWork.RegimenRepository.GetRegimenAsync(id);
        }

        public async Task<Regimen> GetRegimenFullAsync(int id)
        {
            return await _unitOfWork.RegimenRepository.GetRegimenWithChildrenAsync(id);
        }

        public async Task<int> UpSertRegimenDosingRegimen(RegimenDosingRegimen regimenDosingRegimen, int userId)
        {
            regimenDosingRegimen.UpdatedDate = DateTime.Now;
            regimenDosingRegimen.UpdatedBy = userId;

            _unitOfWork.RegimenDosingRegimenRepository.Update(regimenDosingRegimen);
            return await SaveAsync();
        }
                        
        public async Task<int> UpSert(Regimen regimen, int userId)
        {
            bool _addingRegimen = true;
            regimen.UpdatedBy = userId;
            regimen.UpdatedDate = DateTime.Now;

            if (regimen?.RegimenDosingRegimens != null)
            {
                foreach (var item in regimen.RegimenDosingRegimens)
                {
                    item.UpdatedDate = DateTime.Now;
                    item.UpdatedBy = userId;

                    if (item.Id > 0)
                    {
                        _unitOfWork.RegimenDosingRegimenRepository.Update(item);
                    }
                    else
                    {
                        item.OrderNumber = await _unitOfWork.RegimenDosingRegimenRepository.GetRegimenDosingRegimenCountAsync(regimen.Id) + 1;
                        item.CreateBy = userId;
                        item.CreatedDate = DateTime.Now;
                        _unitOfWork.RegimenDosingRegimenRepository.Add(item);
                    }
                }
            }

            var longDescriptionOfItems = await GetAllRegimeDosingRegimenUsedDescription(regimen.Id);
            regimen.RegimenLongDescription = regimen.RegimenTail == null ? longDescriptionOfItems : longDescriptionOfItems + regimen.RegimenTail;

            if (regimen.Id > 0)
            {
                if (string.IsNullOrEmpty(regimen.Status))
                {
                    var latestRegimenStatus = await _unitOfWork.RegimenStatusRepository.GetLatestRegimenStatusAsync(regimen.Id);
                    if(latestRegimenStatus != null)
                    {
                        regimen.Status = latestRegimenStatus.Status;
                    }
                    else
                    {
                        regimen.Status = StrConst.REGIMEN_STATUS_ACTIVE;
                    }
                }

                _unitOfWork.RegimenRepository.Update(regimen);
                _logger.LogInformation($"{StrConst.LOG_TYPE_UPDATE}: {regimen.RegimenName}, USERID: {userId}");
                _addingRegimen = false;
            }
            else
            {
                regimen.CreateBy = userId;
                regimen.CreatedDate = DateTime.Now;
                regimen.Status = StrConst.REGIMEN_STATUS_ACTIVE;
                //regimen.Code = await GetNextRegimenCodeReference(regimen.RegimenName.Substring(0, 1));

                _unitOfWork.RegimenRepository.Add(regimen);
                _logger.LogInformation($"{StrConst.LOG_TYPE_ADDING}: {regimen.RegimenName}, USERID: {userId}");
            }

            await SaveAsync();

            if (_addingRegimen)
            {
                await UpSertRegimenStatus(new RegimenStatus()
                {
                    RegimenId = regimen.Id,
                    Status = StrConst.REGIMEN_STATUS_ACTIVE,
                }, userId);
            }

            return regimen.Id;
        }

        public async Task<string> GenerateRegimenCode(string prefix)
        {
            int increment = 1;
            var omCode = await GetNextRegimenCode(prefix, increment);

            while (await DoesCodeExists(omCode, 0))
            {
                increment++;
                omCode = await GetNextRegimenCode(prefix, increment);
            }

            return omCode;
        }

        private async Task<string> GetNextRegimenCode(string prefix, int increment)
        {
            return await GetNextRegimenCodeReference(prefix, increment);
        }

        public async Task<bool> DoesCodeExists(string code, int regimenId)
        {
            return await _unitOfWork.RegimenRepository.DoesCodeExists(code, regimenId);
        }

        private async Task<string> GetNextRegimenCodeReference(string prefix, int increment)
        {
            return StringHelper.AddDefaultPrefix(prefix, ((await _unitOfWork.RegimenRepository.GetNextRegimenCodeReference(prefix) + increment) * 10).ToString(), FILLER_COUNT);
        }

        public async Task<string> GetAllRegimeDosingRegimenUsedDescription(int regimeId)
        {
            return await _unitOfWork.RegimenDosingRegimenRepository.GetAllRegimeDosingRegimenUsedDescription(regimeId);
        }

        public async Task<RegimenDosingRegimen> GetRegimenDosingRegimenAsync(int id)
        {
            return await _unitOfWork.RegimenDosingRegimenRepository.GetRegimenDosingRegimenAsync(id);
        }

        public async Task<int> RemoveRegimenDosingRegimenAsync(RegimenDosingRegimen regimenDosingRegimen)
        {
            _unitOfWork.RegimenDosingRegimenRepository.RemoveRegimenDosingRegimenAsync(regimenDosingRegimen);
            _logger.LogInformation($"RegimenDosingRegimen {StrConst.LOG_TYPE_REMOVING}: Order Number: {regimenDosingRegimen.OrderNumber} Id: {regimenDosingRegimen.Id} RegimenDosingRegimenId: {regimenDosingRegimen.DosingRegimenId}");
            return await SaveAsync();
        }

        public async Task<RegimenStatus> GetLatestRegimenStatusAsync(int regimenId)
        {
            return await _unitOfWork.RegimenStatusRepository.GetLatestRegimenStatusAsync(regimenId);
        }

        public async Task<int> UpSertRegimenStatus(RegimenStatus status, int userId)
        {
            status.UpdatedBy = userId;
            status.UpdatedDate = DateTime.Now;

            if (status.Id > 0)
            {
                _unitOfWork.RegimenStatusRepository.Update(status);
            }
            else
            {
                status.CreateBy = userId;
                status.CreatedDate = DateTime.Now;

                _unitOfWork.RegimenStatusRepository.Add(status);
            }

            return await SaveAsync();
        }

        public async Task<CopyRegimenResponse> CopyRegimenAsync(int id, int userId)
        {
            var regimen = await _unitOfWork.RegimenRepository.GetRegimenAsync(id);
            var regimenDosingRegimen = await _unitOfWork.RegimenDosingRegimenRepository.GetRegimenDosingRegimenByRegimenAsync(regimen.Id);
            var regimenCosts = await _unitOfWork.RegimenCostRepository.GetRegimenCostsByRegimenAsync(regimen.Id);
            char regimenChar = char.Parse(regimen.RegimenName.Substring(0, 1));
            var omCodePrefix = "OM" + ((int)char.ToLower(regimenChar) % 32);

            var newRegimen = new Regimen()
            {
                RegimenName = regimen.RegimenName,
                Code = await GenerateRegimenCode(omCodePrefix),
                CycleInstruction = regimen.CycleInstruction,
                IsClass = regimen.IsClass,
                NumberOfCycles = regimen.NumberOfCycles,
                RegimenAdministeredCycles = regimen.RegimenAdministeredCycles,
                RegimenAdministeredCyclesFromValue = regimen.RegimenAdministeredCyclesFromValue,
                RegimenAdministeredCyclesToValue = regimen.RegimenAdministeredCyclesToValue,
                RegimenAdministeredNumber = regimen.RegimenAdministeredNumber,
                RegimenAdministeredTerm = regimen.RegimenAdministeredTerm,
                RegimenAdministeredUnits = regimen.RegimenAdministeredUnits,
                RegimenLongDescription = regimen.RegimenLongDescription,
                RegimenTail = regimen.RegimenTail,
                
                CreateBy = userId,
                CreatedDate = DateTime.Now,
                UpdatedBy = userId,
                UpdatedDate = DateTime.Now,
                Deleted = false,
            };

            _unitOfWork.RegimenRepository.Add(newRegimen);

            try
            {
                await SaveAsync();

                foreach(var item in regimenDosingRegimen)
                {
                    _unitOfWork.RegimenDosingRegimenRepository.Add(new RegimenDosingRegimen()
                    {
                        RegimenId = newRegimen.Id,
                        DosingRegimenId = item.DosingRegimenId,
                        OrderNumber = item.OrderNumber,
                        JoiningTerm = item.JoiningTerm,
                        CreateBy = userId,
                        CreatedDate = DateTime.Now,
                        UpdatedBy = userId,
                        UpdatedDate = DateTime.Now,
                        Deleted = false,
                    });
                }

                foreach(var item in regimenCosts)
                {
                    _unitOfWork.RegimenCostRepository.Add(new RegimenCost()
                    {
                        RegimenId = newRegimen.Id,
                        TariffId = item.TariffId,
                        Price = item.Price,
                        Quantity = item.Quantity,
                        Cost = item.Cost,
                        Comment = item.Comment,
                        CreateBy = userId,
                        CreatedDate = DateTime.Now,
                        UpdatedBy = userId,
                        UpdatedDate = DateTime.Now,
                        Deleted = false,
                    });
                }

                var newStatus = new RegimenStatus()
                {
                    RegimenId = newRegimen.Id,
                    Status = StrConst.REGIMEN_STATUS_ACTIVE,
                    CreateBy = userId,
                    CreatedDate = DateTime.Now,
                    UpdatedBy = userId,
                    UpdatedDate = DateTime.Now,
                    Deleted = false
                };

                _unitOfWork.RegimenStatusRepository.Add(newStatus);

                await SaveAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex.Message);

                return new CopyRegimenResponse()
                {
                    ResponseCode = -1,
                    ResponseMessage = $"{StrConst.ACTION_COPY_FAILED}. Protocol Code {regimen.Code}",
                    NewCode = string.Empty,
                    NewId = -1,
                };
            }

            return new CopyRegimenResponse()
            {
                ResponseCode = newRegimen.Id,
                ResponseMessage = $"{StrConst.ACTION_COPY_SUCCESSFUL} ",
                NewCode = newRegimen.Code,
                NewId = newRegimen.Id
            };
        }

        public async Task<int> SaveAsync()
        {
            return await _unitOfWork.SaveAsync();
        }
    }
}
