﻿using Microsoft.Extensions.Logging;
using OHMC.Core.Common.Const;
using OHMC.Core.Common.Response;
using OHMC.Core.Models;
using OHMC.DataAccess.Interface;

namespace OHMC.Application
{
    public class FormulationBL : IFormulationBL
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<FormulationBL> _logger;

        public FormulationBL(IUnitOfWork unitOfWork, ILogger<FormulationBL> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<Formulation> GetFormulationAsync(int id)
        {
            return await _unitOfWork.FormulationRepository.GetFormulationAsync(id);
        }

        public async Task<Formulation> GetFormulationOnlyAsync(int id)
        {
            return await _unitOfWork.FormulationRepository.GetFormulationOnlyAsync(id);
        }

        public async Task<IEnumerable<Formulation>> GetFormulationsAsync()
        {
            return await _unitOfWork.FormulationRepository.GetFormulationsAsync();
        }

        public async Task<IEnumerable<Formulation>> GetFormulationsSimpleAsync()
        {
            return await _unitOfWork.FormulationRepository.GetFormulationsSimpleAsync();
        }

        public async Task<IEnumerable<Formulation>> GetPagedFormulationsAsync(int pageNumber, int pageSize)
        {
            return await _unitOfWork.FormulationRepository.GetPagedFormulationsAsync(pageNumber, pageSize);
        }

        public async Task<IEnumerable<Formulation>> SearchFormulationsAsync(string searchString)
        {
            return await _unitOfWork.FormulationRepository.SearchFormulationsAsync(searchString);
        }

        public async Task<int> GetFormulationCountAsync()
        {
            return await _unitOfWork.FormulationRepository.GetFormulationsCountAsync();
        }
        
        public async Task<IEnumerable<Formulation>> GetFormulationsByINNAsync(string inn)
        {
            return await _unitOfWork.FormulationRepository.GetFormulationsByINNAsync(inn);
        }

        public async Task<GeneralResponse> Delete(int id, int userId)
        {
            var formulation = await _unitOfWork.FormulationRepository.GetFormulationOnlyAsync(id);

            if(formulation != null)
            {

                var usedProducts = await _unitOfWork.ProductRepository.GetProductsByFormulationAsync(formulation.Id);
                if(usedProducts.Any())
                {
                    return new GeneralResponse()
                    {
                        ResponseCode = -1,
                        ResponseMessage = $"{StrConst.ACTION_DELETE_FAILED}. Formulation is being used."
                    };
                }

                formulation.Deleted = true;
                formulation.DeleteOn = DateTime.Now;
                formulation.UpdatedBy = userId;
                formulation.UpdatedDate = DateTime.Now;

                _unitOfWork.FormulationRepository.Update(formulation);
                _logger.LogInformation($"{StrConst.LOG_TYPE_REMOVING}: {formulation.DosageFormDescriptionCalc}, USERID: {userId}");
            }

            return new GeneralResponse()
            {
                ResponseCode = await SaveAsync(),
                ResponseMessage = StrConst.ACTION_DELETE_SUCCESSFUL
            };                      
        }

        public async Task<int> UpSert(Formulation formulation, int userId)
        {
            formulation.UpdatedBy = userId;
            formulation.UpdatedDate = DateTime.Now;

            if (formulation.Id > 0)
            {
                _unitOfWork.FormulationRepository.Update(formulation);
                _logger.LogInformation($"{StrConst.LOG_TYPE_UPDATE}: {formulation.DosageFormDescriptionCalc}, USERID: {userId}");
            }
            else
            {
                formulation.CreateBy = userId;
                formulation.CreatedDate = DateTime.Now;

                _unitOfWork.FormulationRepository.Add(formulation);
                _logger.LogInformation($"{StrConst.LOG_TYPE_ADDING}: {formulation.DosageFormDescriptionCalc}, USERID: {userId}");
            }

            return await SaveAsync();
        }

        public async Task<int> AddRoute(int formulationId, Route route, int userId)
        {
            var dbFormulationRoute = await _unitOfWork.FormulationRoutesRepository.GetFormulationRoutesByFormulationRouteAsync(formulationId, route.RouteName);
            if (!dbFormulationRoute.Any())
            {
                var formulationRoute = new FormulationRoute()
                {
                    FormulationId = formulationId,
                    OrderNumber = await _unitOfWork.FormulationRoutesRepository.GetMaxOrderNumberRouteByFormulationAsync(formulationId) + 1,
                    RouteName = route.RouteName,
                    CreateBy = userId,
                    CreatedDate = DateTime.Now,
                    UpdatedBy = userId,
                    UpdatedDate = DateTime.Now,
                };

                _unitOfWork.FormulationRoutesRepository.Add(formulationRoute);
                return await _unitOfWork.SaveAsync();
            }

            return -1;
        }

        public async Task<int> SaveAsync()
        {
            return await _unitOfWork.SaveAsync();
        }

        public async Task<bool> IsFormulationDuplicate(int id, string dosageFormDescriptionCalc)
        {
            var formulation = await _unitOfWork.FormulationRepository.GetFormulationByDosageFormDescriptionCalcAsync(id, dosageFormDescriptionCalc);
            
            if(formulation.Any())
            {
                return true;
            }

            return false;
        }

        public async Task<CopyFormulationResponse> CopyFormulationAsync(int id, int userId)
        {
            var formulation = await _unitOfWork.FormulationRepository.GetFormulationAsync(id);
            var formulationRoutes = await _unitOfWork.FormulationRoutesRepository.GetFormulationRoutesByFormulationAsync(formulation.Id);

            var newFormulation = new Formulation()
            {
                CanBeAdministeredDivisibleUnits = formulation.CanBeAdministeredDivisibleUnits,
                DivisiblePerUnitVol = formulation.DivisiblePerUnitVol,
                DosageForm = formulation.DosageForm,
                DosageFormDescriptionCalc = formulation.DosageFormDescriptionCalc,
                DosageFormType = formulation.DosageFormType,
                DoseUnitId = formulation.DoseUnitId,
                INNId = formulation.INNId,
                IsTherapeuticClass = formulation.IsTherapeuticClass,
                PrimaryRoute = formulation.PrimaryRoute,
                Strength = formulation.Strength,
                StrengthDose = formulation.StrengthDose,
                StrengthUnits = formulation.StrengthUnits,
                StrengthVolume = formulation.StrengthVolume,
                StrengthVolumeUnits = formulation.StrengthVolumeUnits,
                TherapeuticClass = formulation.TherapeuticClass,
                TotalVolume = formulation.TotalVolume,
                CreateBy = userId,
                CreatedDate = DateTime.Now,
                UpdatedBy = userId,
                UpdatedDate = DateTime.Now,
                Deleted = false,
            };

            _unitOfWork.FormulationRepository.Add(newFormulation);

            try
            {
                await SaveAsync();

                foreach(var route in formulationRoutes)
                {
                    _unitOfWork.FormulationRoutesRepository.Add(new FormulationRoute()
                    {
                        FormulationId = newFormulation.Id,
                        OrderNumber = route.OrderNumber,
                        RouteName = route.RouteName,
                        CreateBy = userId,
                        CreatedDate = DateTime.Now,
                        UpdatedBy = userId,
                        UpdatedDate = DateTime.Now,
                        Deleted = false,
                    });
                }

                await SaveAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex.Message);

                return new CopyFormulationResponse()
                {
                    ResponseCode = -1,
                    ResponseMessage = $"{StrConst.ACTION_COPY_FAILED}. Formulation {formulation.DosageFormDescriptionCalc}",
                    NewCode = string.Empty,
                    NewId = -1,
                };
            }

            return new CopyFormulationResponse()
            {
                ResponseCode = newFormulation.Id,
                ResponseMessage = $"{StrConst.ACTION_COPY_SUCCESSFUL} ",
                NewCode = newFormulation.DosageFormDescriptionCalc,
                NewId = newFormulation.Id
            };
        }
    }
}
