﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace OHMC.Core.Models
{
    public class ProtocolEvidence: ModelBase
    {
        [Key]
        public int Id { get; set; }
        [ForeignKey("Protocol")]
        public int ProtocolId { get; set; }
        public Protocol Protocol { get; set; }

        [ForeignKey("Evidence")]
        public int? EvidenceId { get; set; }
        public Evidence? Evidence { get; set; }

        [MaxLength(250), DisplayName("Title")]
        public string? Title { get; set; } = string.Empty;

        [Required, DisplayName("Evidence")]
        public string EvidenceDescription { get; set; } = string.Empty;
        public string? EvidenceTrialName { get; set; }
        [MaxLength(6), DisplayName("Publication Year")]
        public string? EvidencePublicationYear { get; set; }

        [MaxLength(30), DisplayName("Evidence Type")]
        public string? EvidenceType { get; set; }

        [MaxLength(10), Display<PERSON><PERSON>("Phase")]
        public string? Phase { get; set; }

        [MaxLength(50), Display<PERSON><PERSON>("Blinding")]
        public string? Blinding { get; set; }

        [<PERSON><PERSON>ength(50), Display<PERSON>ame("Level of evidence")]
        public string? LevelOfEvidence { get; set; }

        [MaxLength(100), DisplayName("Sample size")]
        public string? SampleSize { get; set; }
        [MaxLength(100), DisplayName("Observation Period")]
        public string? ObservationPeriod { get; set; }
        [MaxLength(300)]
        public string? Outcome1 { get; set; }
        [MaxLength(300)]
        public string? Outcome1Effect { get; set; }
        [MaxLength(300)]
        public string? Outcome2 { get; set; }
        [MaxLength(300)]
        public string? Outcome2Effect { get; set; }
        [MaxLength(300)]
        public string? Outcome3 { get; set; }
        [MaxLength(300)]
        public string? Outcome3Effect { get; set; }
        [MaxLength(300)]
        public string? Outcome4 { get; set; }
        [MaxLength(300),]
        public string? Outcome4Effect { get; set; }
        [MaxLength(300)]
        public string? Outcome5 { get; set; }
        [MaxLength(300),]
        public string? Outcome5Effect { get; set; }
        [MaxLength(300)]
        public string? Comparator { get; set; }
        [MaxLength(300)]
        public string? CommentClinical { get; set; }
        [MaxLength(300)]
        public string? CommentCriticalAppraisal { get; set; }
        public int? OrderNumber { get; set; }
        public int? ReferenceNumber { get; set; }
    }
}
