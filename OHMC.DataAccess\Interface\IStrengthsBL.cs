﻿using OHMC.Core.Models;

namespace OHMC.DataAccess.Interface
{
    public interface IStrengthsBL
    {
        Task<Strength> GetStrengthAsync(int id);
        Task<IEnumerable<Strength>> GetStrengthsAsync();
        Task<int> RemoveStrength(int id, int userId);
        Task<int> SaveAsync();
        Task<IEnumerable<Strength>> SearchAsync(string desc);
        Task<int> UpSert(Strength strength, int userId);
    }
}