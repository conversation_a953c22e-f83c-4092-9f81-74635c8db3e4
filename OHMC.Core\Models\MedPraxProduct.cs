﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace OHMC.Core.Models
{
    public class MedPraxProduct
    {
        [Key]
        [MaxLength(20)]
        public string Nappi9 { get; set; }
        public string ProductName { get; set; }
        public decimal StandardPackSize { get; set; }
        public decimal StandardPackSizeORG { get; set; }

        public double? SEPExclVat { get; set; }
        public decimal? UnitPExclVAT { get; set; }
        public string? Level3Description { get; set; }
        public string? PacketIntake { get; set; }
        [Column("Presentation description")]
        public string? PresentationDescription { get; set; }
        public string? ManufacturerName { get; set; }
        public string? Strength {  get; set; }
        public string? SEPStartDate { get; set; }
        public string? SEPEndDate { get; set; }

        //public double? MPLPackPriceExVat { get; set; }
        //public double? MMAPPackPriceExlVat { get; set; }
        //public double? MRPPackPriceExclVAT { get; set; }
        //public DateTime? SEPStartDate { get; set; }
        //public DateTime? SEPEndDate { get; set; }

        public string ActiveORG { get; set; }
        //public DateTime? LaunchSEP { get; set; }
    }
}
