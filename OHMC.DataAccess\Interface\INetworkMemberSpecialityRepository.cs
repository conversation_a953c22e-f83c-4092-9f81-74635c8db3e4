﻿using OHMC.Core.Models;

namespace OHMC.DataAccess.Interface
{
    public interface INetworkMemberSpecialityRepository
    {
        void Add(NetworkMemberSpeciality speciality);
        Task<IEnumerable<NetworkMemberSpeciality>> GetNetworkMemberSpecialitiesAsync(int networkMemberId);
        Task<string> GetSpecialitiesDescription(int networkMemberId);
        Task<NetworkMemberSpeciality> GetNetworkMemberSpecialityAsync(int id);
        void Update(NetworkMemberSpeciality speciality);
    }
}