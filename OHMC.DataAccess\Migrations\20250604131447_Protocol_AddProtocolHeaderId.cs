﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace OHMC.DataAccess.Migrations
{
    public partial class Protocol_AddProtocolHeaderId : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "ProtocolHeaderId",
                table: "ProtocolsAudit",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ProtocolHeaderId",
                table: "Protocols",
                type: "int",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_Protocols_ProtocolHeaderId",
                table: "Protocols",
                column: "ProtocolHeaderId");

            migrationBuilder.AddForeignKey(
                name: "FK_Protocols_ProtocolHeaders_ProtocolHeaderId",
                table: "Protocols",
                column: "ProtocolHeaderId",
                principalTable: "ProtocolHeaders",
                principalColumn: "Id");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Protocols_ProtocolHeaders_ProtocolHeaderId",
                table: "Protocols");

            migrationBuilder.DropIndex(
                name: "IX_Protocols_ProtocolHeaderId",
                table: "Protocols");

            migrationBuilder.DropColumn(
                name: "ProtocolHeaderId",
                table: "ProtocolsAudit");

            migrationBuilder.DropColumn(
                name: "ProtocolHeaderId",
                table: "Protocols");
        }
    }
}
