﻿using OHMC.Core.Common.Response;
using OHMC.Core.Models;

namespace OHMC.DataAccess.Interface
{
    public interface IProtocolBL
    {
        Task<ProtocolStatus> GetLatestProtocolStatusAsync(int regimenId);
        Task<IEnumerable<Protocol>> GetPagedProtocolsAsync(int pageNumber, int pageSize);
        Task<Protocol> GetProtocolAsync(int id);
        Task<Protocol> GetProtocolFullAsync(int id);
        Task<int> GetProtocolCountAsync();
        Task<IEnumerable<Protocol>> GetProtocolsAsync();
        Task<int> SaveAsync();
        Task<IEnumerable<Protocol>> SearchProtocolAsync(string desc);
        Task<IEnumerable<Protocol>> SearchProtocolsByCancerTypeGroupAsync(string cancerType, string cancerGroup);
        Task<IEnumerable<Protocol>> SearchProtocolByStatusAsync(string desc);
        Task<int> UpSert(Protocol protocol, int userId);
        Task<int> UpSertProtocolStatus(ProtocolStatus status, int userId);
        Task<int> UpSertProtocolEvidence(ProtocolEvidence evidence, int userId);
        Task<int> UpSertProtocolICD10(ProtocolICD10Code newICD10, int userId);
        Task<IEnumerable<ProtocolEvidence>> GetProtocolEvidences(int protocolId);
        Task<IEnumerable<Protocol>> GetPagedProtocolItemsAsync(int pageNumber, int pageSize);
        Task<ProtocolEvidence> GetProtocolEvidenceAsync(int protocolEvidenceId);
        Task<int> RemoveProtocolEvidenceAsync(ProtocolEvidence evidence);
        Task<bool> IsProtocolReferenced(int protocolId);
        Task<bool> IsProtocolCompleteAsync(int protocolId);
        Task<CopyProtocolResponse> CopyProtocolAsync(int id, int userId);
        Task<IEnumerable<Protocol>> SearchProtocolsByChapterAsync(string chapter);
        Task<IEnumerable<Protocol>> SearchProtocolsByCancerGroupAsync(string cancerGroup);
        Task<IEnumerable<Protocol>> SearchProtocolsByCancerTypeAsync(string cancerType);
        Task<IEnumerable<ProtocolAudit>> GetProtocolAuditHistoryAsync(int protocolId);
        Task<GenerateRefNumbersResponse> GenerateRefNumbers();
        Task<IEnumerable<ProtocolStatus>> GetProtocolStatusesAsync(int id);
        Task<IEnumerable<ProtocolICD10Code>> GetProtocolICD10s(int protocolId);
        Task<ProtocolICD10Code> GetProtocolICD10Async(int id);
        Task<string> GetProtocolICD10sDescription(int protocolId);
        Task<int> UpSertProtocolUserReview(ProtocolUserReview review, int userId);
        Task<IEnumerable<ProtocolUserReview>> GetProtocolUserReviews(int protocolId);
        Task<ProtocolUserReview> GetProtocolUserReview(int id);
        Task<int> UpSertProtocolGuideline(ProtocolGuideline guideline, int userId);
        Task<string> GetProtocolGuidelinesDescription(int protocolId);
        Task<ProtocolHeader> GetProtocolHeaderAsync(int id);
        Task<IEnumerable<ProtocolHeader>> GetProtocolHeadersAsync();
        Task<int> UpSertProtocolHeader(ProtocolHeader header, int userId);
        Task<IEnumerable<Protocol>> GetProtocolPartsAsync(int id);
        Task<int> RemoveProtocolICD10Async(ProtocolICD10Code icd10);
        Task<CancerGroupICD10sToPotocolResponse> AddCancerGroupICD10s(int protocolId, int cancerGroupId, int userId);
        Task<ProtocolICD10Code> GetProtocolICD10Async(int protocolId, string icd10);
    }
}