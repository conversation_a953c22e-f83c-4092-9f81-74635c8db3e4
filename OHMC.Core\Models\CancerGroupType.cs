﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace OHMC.Core.Models
{
    public class CancerGroupType : ModelBase
    {
        [Key]
        public int Id { get; set; }
        [Required, <PERSON><PERSON><PERSON><PERSON>(20), Display<PERSON><PERSON>("Code")]
        public string Code { get; set; } = string.Empty;
        [Required, <PERSON><PERSON><PERSON><PERSON>(100), Disp<PERSON><PERSON><PERSON>("Group Name")]
        public string GroupName { get; set; } = string.Empty;
        [Required, <PERSON><PERSON><PERSON><PERSON>(200), <PERSON>sp<PERSON><PERSON><PERSON>("Cancer Type")]
        public string CancerType { get; set; } = string.Empty;
        public int? PrintOrder { get; set; }
    }
}
