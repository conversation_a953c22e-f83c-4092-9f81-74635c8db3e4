﻿using System.ComponentModel.DataAnnotations;

namespace OHMC.Core.Models
{
    public class ProtocolTreatmentIntentAudit : BaseAudit
    {
        [Key]
        public int ProtocolTreatmentIntentAuditId { get; set; }
        public int ProtocolTreatmentIntentId { get; set; }
        public int ProtocolId { get; set; }
        public int OrderNumber { get; set; }
        [MaxLength(50)]
        public string TreatmentIntent { get; set; } = string.Empty;
        [MaxLength(50)]
        public string? TreatmentIntentDetail { get; set; } = string.Empty;
    }
}
