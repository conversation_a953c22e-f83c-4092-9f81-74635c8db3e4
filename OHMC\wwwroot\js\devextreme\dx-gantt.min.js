/*! For license information please see dx-gantt.min.js.LICENSE.txt */
!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.DevExpress=t():(e.DevExpress=e.DevExpress||{},e.DevExpress.Gantt=t())}(this,(()=>(()=>{"use strict";var e={9279:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.<PERSON>rowser=void 0;var n=function(){function e(){}return e.IdentUserAgent=function(t,n){void 0===n&&(n=!1);var r=["Mozilla","IE","Firefox","Netscape","Safari","Chrome","Opera","Opera10","Edge"],i="IE",o="Win",s={Safari:2,Chrome:.1,Mozilla:1.9,Netscape:8,Firefox:2,Opera:9,IE:6,Edge:12};if(t&&0!==t.length){t=t.toLowerCase(),e.indentPlatformMajorVersion(t);try{for(var a={Windows:"Win",Macintosh:"Mac","Mac OS":"Mac",Mac_PowerPC:"Mac","cpu os":"MacMobile","cpu iphone os":"MacMobile",Android:"Android","!Windows Phone":"WinPhone","!WPDesktop":"WinPhone","!ZuneWP":"WinPhone"},l="(?:/|\\s*)?",c="(\\d+)(?:\\.((?:\\d+?[1-9])|\\d)0*?)?",u="(?:"+c+")?",d={Safari:"applewebkit(?:.*?(?:version/"+c+"[\\.\\w\\d]*?(?:\\s+mobile/\\S*)?\\s+safari))?",Chrome:"(?:chrome|crios)(?!frame)"+l+u,Mozilla:"mozilla(?:.*rv:"+u+".*Gecko)?",Netscape:"(?:netscape|navigator)\\d*/?\\s*"+u,Firefox:"firefox"+l+u,Opera:"(?:opera|\\sopr)"+l+u,Opera10:"opera.*\\s*version"+l+u,IE:"msie\\s*"+u,Edge:"edge"+l+u},p=null,h=-1,f=0;f<r.length;f++){var g=r[f],y=new RegExp(d[g],"i").exec(t);if(y&&y.index>=0){if("IE"===p&&h>=11&&"Safari"===g)continue;"Opera10"===(p=g)&&(p="Opera");var m="trident"+l+u;h=e.GetBrowserVersion(t,y,m,e.getIECompatibleVersionString()),"Mozilla"===p&&h>=11&&(p="IE")}}p||(p=i);var v=-1!==h;v||(h=s[p]);var T=null,b=Number.MAX_VALUE;for(var k in a)if(Object.prototype.hasOwnProperty.call(a,k)){var S="!"===k.substr(0,1),_=t.indexOf((S?k.substr(1):k).toLowerCase());_>=0&&(_<b||S)&&(b=S?0:_,T=a[k])}var w=t.toUpperCase().match("SM-[A-Z]"),E=w&&w.length>0;"WinPhone"===T&&h<9&&(h=Math.floor(e.getVersionFromTrident(t,"trident"+l+u))),!n&&"IE"===p&&h>7&&document.documentMode<h&&(h=document.documentMode),"WinPhone"===T&&(h=Math.max(9,h)),T||(T=o),T!==a["cpu os"]||v||(h=4),e.fillUserAgentInfo(r,p,h,T,E)}catch(t){e.fillUserAgentInfo(r,i,s[i],o)}}else e.fillUserAgentInfo(r,i,s[i],o)},e.GetBrowserVersion=function(t,n,r,i){var o=e.getVersionFromMatches(n);if(i){var s=e.getVersionFromTrident(t,r);if("edge"===i||parseInt(i)===s)return s}return o},e.getIECompatibleVersionString=function(){if(document.compatible)for(var e=0;e<document.compatible.length;e++)if("IE"===document.compatible[e].userAgent&&document.compatible[e].version)return document.compatible[e].version.toLowerCase();return""},e.isTouchEnabled=function(){return e.hasTouchStart()||e.hasMaxTouchPoints()||e.hasMsMaxTouchPoints()},e.hasTouchStart=function(){return"ontouchstart"in window},e.hasMaxTouchPoints=function(){return navigator.maxTouchPoints>0},e.hasMsMaxTouchPoints=function(){return navigator.msMaxTouchPoints>0},e.hasNavigator=function(){return"undefined"!=typeof navigator},e.fillUserAgentInfo=function(t,n,r,i,o){void 0===o&&(o=!1);for(var s=0;s<t.length;s++){var a=t[s];e[a]=a===n}e.Version=Math.floor(10*r)/10,e.MajorVersion=Math.floor(e.Version),e.WindowsPlatform="Win"===i||"WinPhone"===i,e.MacOSMobilePlatform="MacMobile"===i||"Mac"===i&&e.isTouchEnabled(),e.MacOSPlatform="Mac"===i&&!e.MacOSMobilePlatform,e.AndroidMobilePlatform="Android"===i,e.WindowsPhonePlatform="WinPhone"===i,e.WebKitFamily=e.Safari||e.Chrome||e.Opera&&e.MajorVersion>=15,e.NetscapeFamily=e.Netscape||e.Mozilla||e.Firefox,e.WebKitTouchUI=e.MacOSMobilePlatform||e.AndroidMobilePlatform;var l=e.IE&&e.MajorVersion>9&&e.WindowsPlatform&&e.UserAgent.toLowerCase().indexOf("touch")>=0;if(e.MSTouchUI=l||e.Edge&&!!window.navigator.maxTouchPoints,e.TouchUI=e.WebKitTouchUI||e.MSTouchUI,e.MobileUI=e.WebKitTouchUI||e.WindowsPhonePlatform,e.AndroidDefaultBrowser=e.AndroidMobilePlatform&&!e.Chrome,e.AndroidChromeBrowser=e.AndroidMobilePlatform&&e.Chrome,o&&(e.SamsungAndroidDevice=o),e.MSTouchUI){var c=e.UserAgent.toLowerCase().indexOf("arm;")>-1;e.VirtualKeyboardSupported=c||e.WindowsPhonePlatform}else e.VirtualKeyboardSupported=e.WebKitTouchUI;e.fillDocumentElementBrowserTypeClassNames(t)},e.indentPlatformMajorVersion=function(t){var n=/(?:(?:windows nt|macintosh|mac os|cpu os|cpu iphone os|android|windows phone|linux) )(\d+)(?:[-0-9_.])*/.exec(t);n&&(e.PlaformMajorVersion=n[1])},e.getVersionFromMatches=function(e){var t=-1,n="";return e&&(e[1]&&(n+=e[1],e[2]&&(n+="."+e[2])),""!==n&&(t=parseFloat(n),isNaN(t)&&(t=-1))),t},e.getVersionFromTrident=function(t,n){var r=new RegExp(n,"i").exec(t);return e.getVersionFromMatches(r)+4},e.fillDocumentElementBrowserTypeClassNames=function(t){for(var n="",r=t.concat(["WindowsPlatform","MacOSPlatform","MacOSMobilePlatform","AndroidMobilePlatform","WindowsPhonePlatform","WebKitFamily","WebKitTouchUI","MSTouchUI","TouchUI","AndroidDefaultBrowser"]),i=0;i<r.length;i++){var o=r[i];e[o]&&(n+="dx"+o+" ")}n+="dxBrowserVersion-"+e.MajorVersion,"undefined"!=typeof document&&document&&document.documentElement&&(""!==document.documentElement.className&&(n=" "+n),document.documentElement.className+=n,e.Info=n)},e.getUserAgent=function(){return e.hasNavigator()&&navigator.userAgent?navigator.userAgent.toLowerCase():""},e.UserAgent=e.getUserAgent(),e._foo=e.IdentUserAgent(e.UserAgent),e}();t.Browser=n},6799:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ExtendedMinMax=t.ExtendedMax=t.ExtendedMin=t.MinMaxNumber=t.MinMax=void 0;var r=n(655),i=function(e,t){this.minElement=e,this.maxElement=t};t.MinMax=i;var o=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r.__extends(t,e),Object.defineProperty(t.prototype,"length",{get:function(){return this.maxElement-this.minElement},enumerable:!1,configurable:!0}),t}(i);t.MinMaxNumber=o;var s=function(e,t){this.minElement=e,this.minValue=t};t.ExtendedMin=s;var a=function(e,t){this.maxElement=e,this.maxValue=t};t.ExtendedMax=a;var l=function(e){function t(t,n,r,i){var o=e.call(this,t,r)||this;return o.minValue=n,o.maxValue=i,o}return r.__extends(t,e),t}(i);t.ExtendedMinMax=l},3604:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Margins=void 0;var r=n(655),i=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r.__extends(t,e),t.empty=function(){return new t(0,0,0,0)},t.prototype.clone=function(){return new t(this.left,this.right,this.top,this.bottom)},t}(n(4125).Offsets);t.Margins=i},5596:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Metrics=void 0;var n=function(){function e(){}return e.euclideanDistance=function(e,t){var n=e.x-t.x,r=e.y-t.y;return Math.sqrt(n*n+r*r)},e.manhattanDistance=function(e,t){return Math.abs(e.x-t.x)+Math.abs(e.y-t.y)},e}();t.Metrics=n},4125:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Offsets=void 0;var n=function(){function e(e,t,n,r){this.left=e,this.right=t,this.top=n,this.bottom=r}return e.empty=function(){return new e(0,0,0,0)},Object.defineProperty(e.prototype,"horizontal",{get:function(){return this.left+this.right},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"vertical",{get:function(){return this.top+this.bottom},enumerable:!1,configurable:!0}),e.fromNumber=function(t){return new e(t,t,t,t)},e.fromOffsets=function(t){return new e(t.left,t.right,t.top,t.bottom)},e.fromSide=function(t,n){return new e(t,t,n,n)},e.prototype.normalize=function(){return this.left=Math.max(0,this.left),this.right=Math.max(0,this.right),this.top=Math.max(0,this.top),this.bottom=Math.max(0,this.bottom),this},e.prototype.toString=function(){return JSON.stringify(this)},e.prototype.isEmpty=function(){return 0===this.left&&0===this.right&&0===this.top&&0===this.bottom},e.prototype.offset=function(e){return this.left+=e.left,this.right+=e.right,this.top+=e.top,this.bottom+=e.bottom,this},e.prototype.multiply=function(e,t,n,r){switch(arguments.length){case 1:return this.left*=e,this.right*=e,this.top*=e,this.bottom*=e,this;case 2:return this.left*=e,this.right*=e,this.top*=t,this.bottom*=t,this;case 4:return this.left*=e,this.right*=t,this.top*=n,this.bottom*=r,this}return this},e.prototype.clone=function(){return new e(this.left,this.right,this.top,this.bottom)},e.prototype.copyFrom=function(e){this.left=e.left,this.right=e.right,this.top=e.top,this.bottom=e.bottom},e.prototype.equals=function(e){return this.top===e.top&&this.bottom===e.bottom&&this.right===e.right&&this.left===e.left},e.prototype.applyConverter=function(e){return this.left=e(this.left),this.right=e(this.right),this.top=e(this.top),this.bottom=e(this.bottom),this},e}();t.Offsets=n},8900:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Point=void 0;var n=function(){function e(e,t){this.x=e,this.y=t}return e.zero=function(){return new e(0,0)},e.fromNumber=function(t){return new e(t,t)},e.prototype.isZero=function(){return 0===this.x&&0===this.y},e.prototype.toString=function(){return JSON.stringify(this)},e.prototype.copyFrom=function(e){this.x=e.x,this.y=e.y},e.prototype.clone=function(){return new e(this.x,this.y)},e.prototype.equals=function(e){return this.x===e.x&&this.y===e.y},e.prototype.offset=function(e,t){return this.x+=e,this.y+=t,this},e.prototype.offsetByPoint=function(e){return this.x+=e.x,this.y+=e.y,this},e.prototype.multiply=function(e,t){return this.x*=e,this.y*=t,this},e.prototype.negative=function(){return this.x*=-1,this.y*=-1,this},e.prototype.applyConverter=function(e){return this.x=e(this.x),this.y=e(this.y),this},e.plus=function(t,n){return new e(t.x+n.x,t.y+n.y)},e.minus=function(t,n){return new e(t.x-n.x,t.y-n.y)},e.xComparer=function(e,t){return e.x-t.x},e.yComparer=function(e,t){return e.y-t.y},e.equals=function(e,t){return e.x===t.x&&e.y===t.y},e}();t.Point=n},6353:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Size=void 0;var n=function(){function e(e,t){this.width=e,this.height=t}return e.empty=function(){return new e(0,0)},e.fromNumber=function(t){return new e(t,t)},e.initByCommonAction=function(t){var n=function(e){return e.width},r=function(e){return e.height};return new e(t(n,r),t(r,n))},e.prototype.isEmpty=function(){return 0===this.width&&0===this.height},e.prototype.toString=function(){return JSON.stringify(this)},e.prototype.nonNegativeSize=function(){return this.width<0&&(this.width=0),this.height<0&&(this.height=0),this},e.prototype.offset=function(e,t){return this.width=this.width+e,this.height=this.height+t,this},e.prototype.multiply=function(e,t){return this.width*=e,this.height*=t,this},e.prototype.equals=function(e){return this.width===e.width&&this.height===e.height},e.prototype.clone=function(){return new e(this.width,this.height)},e.prototype.copyFrom=function(e){this.width=e.width,this.height=e.height},e.prototype.applyConverter=function(e){return this.width=e(this.width),this.height=e(this.height),this},e.equals=function(e,t){return e.width===t.width&&e.height===t.height},e}();t.Size=n},2217:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.AttrUtils=void 0;var r=n(9279),i=function(){function e(){}return e.setElementAttribute=function(e,t,n){e.setAttribute&&(r.Browser.IE&&r.Browser.MajorVersion>=11&&"src"===t.toLowerCase()&&e.setAttribute(t,""),e.setAttribute(t,n))},e.setStyleAttribute=function(e,t,n){e.setProperty&&e.setProperty(t,n,"")},e.getElementAttribute=function(e,t){return e.getAttribute(t)},e.getStyleAttribute=function(e,t){if(e.getPropertyValue){if(r.Browser.Firefox)try{return e.getPropertyValue(t)}catch(n){return e[t]}return e.getPropertyValue(t)}return null},e.removeElementAttribute=function(e,t){e.removeAttribute&&e.removeAttribute(t)},e.removeStyleAttribute=function(e,t){e.removeProperty&&e.removeProperty(t)},e.changeElementStyleAttribute=function(t,n,r){e.saveStyleAttributeInElement(t,n),e.setStyleAttribute(t.style,n,r)},e.restoreElementStyleAttribute=function(t,n){var r="dxwu_saved"+n,i=t.style;if(e.isExistsAttributeInElement(t,r)){var o=e.getElementAttribute(t,r);return o===e.emptyObject||null===o?e.removeStyleAttribute(i,n):e.setStyleAttribute(i,n,o),e.removeElementAttribute(t,r),!0}return!1},e.saveStyleAttributeInElement=function(t,n){var r="dxwu_saved"+n,i=t.style;if(!e.isExistsAttributeInElement(t,r)){var o=e.getStyleAttribute(i,n);e.setElementAttribute(t,r,e.isAttributeExists(o)?o:e.emptyObject)}},e.isExistsAttributeInElement=function(t,n){var r=e.getElementAttribute(t,n);return e.isAttributeExists(r)},e.isAttributeExists=function(e){return null!==e&&""!==e},e.emptyObject="DxEmptyValue",e}();t.AttrUtils=i},2491:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.numberToStringHex=t.numberToStringBin=t.isOdd=t.isEven=t.isNonNullString=t.isString=t.isNumber=t.boolToString=t.boolToInt=t.isDefined=void 0;var r=n(49);t.isDefined=function(e){return null!=e},t.boolToInt=function(e){return e?1:0},t.boolToString=function(e){return e?"1":"0"},t.isNumber=function(e){return"number"==typeof e},t.isString=function(e){return"string"==typeof e},t.isNonNullString=function(e){return!!e},t.isEven=function(e){return e%2!=0},t.isOdd=function(e){return e%2==0},t.numberToStringBin=function(e,t){return void 0===t&&(t=0),r.StringUtils.padLeft(e.toString(2),t,"0")},t.numberToStringHex=function(e,t){return void 0===t&&(t=0),r.StringUtils.padLeft(e.toString(16),t,"0")}},4170:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Equals=t.Comparers=void 0;var n=function(){function e(){}return e.number=function(e,t){return e-t},e.string=function(e,t){return e===t?0:e>t?1:-1},e.stringIgnoreCase=function(e,t){return(e=e.toLowerCase())===(t=t.toLowerCase())?0:e>t?1:-1},e}();t.Comparers=n;var r=function(){function e(){}return e.simpleType=function(e,t){return e===t},e.object=function(e,t){return e&&t&&(e===t||e.equals(t))},e}();t.Equals=r},6907:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DomUtils=void 0;var r=n(9279),i=n(2491),o=n(8679),s=n(49),a=function(){function e(){}return e.clearInnerHtml=function(e){for(;e.firstChild;)e.removeChild(e.firstChild)},e.setStylePosition=function(e,t){e.left=o.MathUtils.round(t.x,3)+"px",e.top=o.MathUtils.round(t.y,3)+"px"},e.setStyleSize=function(e,t){e.width=o.MathUtils.round(t.width,3)+"px",e.height=o.MathUtils.round(t.height,3)+"px"},e.setStyleSizeAndPosition=function(t,n){e.setStylePosition(t,n),e.setStyleSize(t,n)},e.hideNode=function(e){if(e){var t=e.parentNode;t&&t.removeChild(e)}},e.isHTMLElementNode=function(e){return e.nodeType===Node.ELEMENT_NODE},e.isTextNode=function(e){return e.nodeType===Node.TEXT_NODE},e.isElementNode=function(e){return e.nodeType===Node.ELEMENT_NODE},e.isHTMLTableRowElement=function(e){return"TR"===e.tagName},e.isItParent=function(e,t){if(!e||!t)return!1;for(;t;){if(t===e)return!0;if("BODY"===t.tagName)return!1;t=t.parentNode}return!1},e.getParentByTagName=function(e,t){for(t=t.toUpperCase();e;){if("BODY"===e.tagName)return null;if(e.tagName===t)return e;e=e.parentNode}return null},e.getDocumentScrollTop=function(){var t=r.Browser.IE&&"hidden"===e.getCurrentStyle(document.body).overflow&&document.body.scrollTop>0;return r.Browser.WebKitFamily||r.Browser.Edge||t?r.Browser.MacOSMobilePlatform?window.pageYOffset:r.Browser.WebKitFamily&&document.documentElement.scrollTop||document.body.scrollTop:document.documentElement.scrollTop},e.getDocumentScrollLeft=function(){var t=r.Browser.IE&&"hidden"===e.getCurrentStyle(document.body).overflow&&document.body.scrollLeft>0;return r.Browser.Edge||t?document.body?document.body.scrollLeft:document.documentElement.scrollLeft:r.Browser.WebKitFamily?document.documentElement.scrollLeft||document.body.scrollLeft:document.documentElement.scrollLeft},e.getCurrentStyle=function(e){if(e.currentStyle)return e.currentStyle;if(document.defaultView&&document.defaultView.getComputedStyle){var t=document.defaultView.getComputedStyle(e,null);if(!t&&r.Browser.Firefox&&window.frameElement){for(var n=[],o=window.frameElement;!(t=document.defaultView.getComputedStyle(e,null));)n.push([o,o.style.display]),o.style.setProperty("display","block","important"),o="BODY"===o.tagName?o.ownerDocument.defaultView.frameElement:o.parentNode;t=function(e){if("object"!=typeof e||!i.isDefined(e))return e;var t={};for(var n in e)t[n]=e[n];return t}(t);for(var s=void 0,a=0;s=n[a];a++)s[0].style.display=s[1];document.body.offsetWidth}return t}return window.getComputedStyle(e,null)},e.setFocus=function(e){function t(){try{e.focus(),r.Browser.IE&&document.activeElement!==e&&e.focus()}catch(e){}}r.Browser.MacOSMobilePlatform?t():setTimeout((function(){t()}),100)},e.hasClassName=function(e,t){try{var n=t.split(" "),r=e.classList;if(r){for(var i=n.length-1;i>=0;i--)if(!r.contains(n[i]))return!1}else{var o=e.getAttribute&&e.getAttribute("class");if(!o)return!1;var s=o.split(" ");for(i=n.length-1;i>=0;i--)if(s.indexOf(n[i])<0)return!1}return!0}catch(e){return!1}},e.addClassName=function(t,n){if(!e.hasClassName(t,n)){var r=t.getAttribute&&t.getAttribute("class");t.setAttribute("class",""===r?n:r+" "+n)}},e.removeClassName=function(e,t){var n=" "+(e.getAttribute&&e.getAttribute("class"))+" ",r=n.replace(" "+t+" "," ");n.length!==r.length&&e.setAttribute("class",s.StringUtils.trim(r))},e.toggleClassName=function(t,n,r){void 0===r?e.hasClassName(t,n)?e.removeClassName(t,n):e.addClassName(t,n):r?e.addClassName(t,n):e.removeClassName(t,n)},e.pxToInt=function(e){return l(e,parseInt)},e.pxToFloat=function(e){return l(e,parseFloat)},e.getAbsolutePositionY=function(t){function n(t){return Math.round(t.getBoundingClientRect().top+e.getDocumentScrollTop())}return t?r.Browser.IE?function(t){return r.Browser.IE&&null===t.parentNode?0:t.getBoundingClientRect().top+e.getDocumentScrollTop()}(t):r.Browser.Firefox&&r.Browser.Version>=3?n(t):r.Browser.NetscapeFamily&&(!r.Browser.Firefox||r.Browser.Version<3)?function(t){for(var n=c(t,!1),i=!0;null!=t;){if(n+=t.offsetTop,i||null==t.offsetParent||(n-=t.scrollTop),!i&&r.Browser.Firefox){var o=e.getCurrentStyle(t);"DIV"===t.tagName&&"visible"!==o.overflow&&(n+=e.pxToInt(o.borderTopWidth))}i=!1,t=t.offsetParent}return n}(t):r.Browser.WebKitFamily||r.Browser.Edge?n(t):function(e){for(var t=0,n=!0;null!=e;)t+=e.offsetTop,n||null==e.offsetParent||(t-=e.scrollTop),n=!1,e=e.offsetParent;return t}(t):0},e.getAbsolutePositionX=function(t){function n(t){return Math.round(t.getBoundingClientRect().left+e.getDocumentScrollLeft())}return t?r.Browser.IE?function(t){return r.Browser.IE&&null===t.parentNode?0:t.getBoundingClientRect().left+e.getDocumentScrollLeft()}(t):r.Browser.Firefox&&r.Browser.Version>=3?n(t):r.Browser.Opera&&r.Browser.Version<=12?function(e){for(var t=!0,n=c(e,!0);null!=e;)n+=e.offsetLeft,t||(n-=e.scrollLeft),e=e.offsetParent,t=!1;return n+=document.body.scrollLeft}(t):r.Browser.NetscapeFamily&&(!r.Browser.Firefox||r.Browser.Version<3)?function(t){for(var n=c(t,!0),i=!0;null!=t;){if(n+=t.offsetLeft,i||null==t.offsetParent||(n-=t.scrollLeft),!i&&r.Browser.Firefox){var o=e.getCurrentStyle(t);"DIV"===t.tagName&&"visible"!==o.overflow&&(n+=e.pxToInt(o.borderLeftWidth))}i=!1,t=t.offsetParent}return n}(t):r.Browser.WebKitFamily||r.Browser.Edge?n(t):function(e){for(var t=0,n=!0;null!=e;)t+=e.offsetLeft,n||null==e.offsetParent||(t-=e.scrollLeft),n=!1,e=e.offsetParent;return t}(t):0},e.isInteractiveControl=function(e){return["A","INPUT","SELECT","OPTION","TEXTAREA","BUTTON","IFRAME"].indexOf(e.tagName)>-1},e.getClearClientHeight=function(t){return t.offsetHeight-(e.getTopBottomPaddings(t)+e.getVerticalBordersWidth(t))},e.getTopBottomPaddings=function(t,n){var r=n||e.getCurrentStyle(t);return e.pxToInt(r.paddingTop)+e.pxToInt(r.paddingBottom)},e.getVerticalBordersWidth=function(t,n){i.isDefined(n)||(n=r.Browser.IE&&9!==r.Browser.MajorVersion&&window.getComputedStyle?window.getComputedStyle(t):e.getCurrentStyle(t));var o=0;return"none"!==n.borderTopStyle&&(o+=e.pxToFloat(n.borderTopWidth)),"none"!==n.borderBottomStyle&&(o+=e.pxToFloat(n.borderBottomWidth)),o},e.getNodes=function(e,t){for(var n=e.all||e.getElementsByTagName("*"),r=[],i=0;i<n.length;i++){var o=n[i];t(o)&&r.push(o)}return r},e.getChildNodes=function(e,t){for(var n=e.childNodes,r=[],i=0;i<n.length;i++){var o=n[i];t(o)&&r.push(o)}return r},e.getNodesByClassName=function(t,n){if(t.querySelectorAll){var r=t.querySelectorAll("."+n),i=[];return r.forEach((function(e){return i.push(e)})),i}return e.getNodes(t,(function(t){return e.hasClassName(t,n)}))},e.getChildNodesByClassName=function(t,n){return t.querySelectorAll?function(e,t){for(var n=[],r=0;r<e.length;r++){var i=e[r];t(i)&&n.push(i)}return n}(t.querySelectorAll("."+n),(function(e){return e.parentNode===t})):e.getChildNodes(t,(function(t){return!!e.isElementNode(t)&&(i.isNonNullString(t.className)&&e.hasClassName(t,t.className))}))},e.getVerticalScrollBarWidth=function(){if(void 0===e.verticalScrollBarWidth){var t=document.createElement("DIV");t.style.cssText="position: absolute; top: 0px; left: 0px; visibility: hidden; width: 200px; height: 150px; overflow: hidden; box-sizing: content-box",document.body.appendChild(t);var n=document.createElement("P");t.appendChild(n),n.style.cssText="width: 100%; height: 200px;";var r=n.offsetWidth;t.style.overflow="scroll";var i=n.offsetWidth;r===i&&(i=t.clientWidth),e.verticalScrollBarWidth=r-i,document.body.removeChild(t)}return e.verticalScrollBarWidth},e.getHorizontalBordersWidth=function(t,n){i.isDefined(n)||(n=r.Browser.IE&&window.getComputedStyle?window.getComputedStyle(t):e.getCurrentStyle(t));var o=0;return"none"!==n.borderLeftStyle&&(o+=e.pxToFloat(n.borderLeftWidth)),"none"!==n.borderRightStyle&&(o+=e.pxToFloat(n.borderRightWidth)),o},e.getFontFamiliesFromCssString=function(e){return e.split(",").map((function(e){return s.StringUtils.trim(e.replace(/'|"/gi,""))}))},e.getInnerText=function(t){if(r.Browser.Safari&&r.Browser.MajorVersion<=5){null===e.html2PlainTextFilter&&(e.html2PlainTextFilter=document.createElement("DIV"),e.html2PlainTextFilter.style.width="0",e.html2PlainTextFilter.style.height="0",e.html2PlainTextFilter.style.overflow="visible",e.html2PlainTextFilter.style.display="none",document.body.appendChild(e.html2PlainTextFilter));var n=e.html2PlainTextFilter;n.innerHTML=t.innerHTML,n.style.display="";var i=n.innerText;return n.style.display="none",i}return r.Browser.NetscapeFamily||r.Browser.WebKitFamily||r.Browser.IE&&r.Browser.Version>=9||r.Browser.Edge?t.textContent:t.innerText},e.html2PlainTextFilter=null,e.verticalScrollBarWidth=void 0,e}();function l(e,t){var n=0;if(i.isDefined(e)&&""!==e)try{var r=e.indexOf("px");r>-1&&(n=t(e.substr(0,r)))}catch(e){}return n}function c(e,t){for(var n=0,r=!0;null!=e&&"BODY"!==e.tagName;){var i=a.getCurrentStyle(e);if("absolute"===i.position)break;r||"DIV"!==e.tagName||""!==i.position&&"static"!==i.position||(n-=t?e.scrollLeft:e.scrollTop),e=e.parentNode,r=!1}return n}t.DomUtils=a},9712:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.EncodeUtils=void 0;var n=function(){function e(){}return e.encodeHtml=function(e){return e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;")},e.decodeHtml=function(e){return e.replace(/&amp;/g,"&").replace(/&lt;/g,"<").replace(/&gt;/g,">")},e.prepareTextForRequest=function(e){return e.replace(/%/g,"%25").replace(/&/g,"%26amp;").replace(/\+/g,"%2B").replace(/</g,"%26lt;").replace(/>/g,"%26gt;").replace(/"/g,"%26quot;")},e.prepareTextForCallBackRequest=function(e){return e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;")},e.decodeViaTextArea=function(e){var t=document.createElement("TEXTAREA");return t.innerHTML=e,t.value},e}();t.EncodeUtils=n},3714:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.EvtUtils=void 0;var r=n(9279),i=n(2491),o=n(6907),s=n(1632),a=function(){function e(){}return e.preventEvent=function(e){e.cancelable&&(e.preventDefault?e.preventDefault():e.returnValue=!1)},e.getEventSource=function(t){return i.isDefined(t)?e.getEventSourceCore(t):null},e.getEventSourceByPosition=function(t){if(!i.isDefined(t))return null;if(!document.elementFromPoint)return e.getEventSourceCore(t);var n=e.getEventX(t)-(e.clientEventRequiresDocScrollCorrection()?o.DomUtils.getDocumentScrollLeft():0),r=e.getEventY(t)-(e.clientEventRequiresDocScrollCorrection()?o.DomUtils.getDocumentScrollTop():0);return void 0===n||void 0===r?e.getEventSourceCore(t):document.elementFromPoint(n,r)},e.getEventSourceCore=function(e){return e.srcElement?e.srcElement:e.target},e.getMouseWheelEventName=function(){return r.Browser.Safari?"mousewheel":r.Browser.NetscapeFamily&&r.Browser.MajorVersion<17?"DOMMouseScroll":"wheel"},e.isLeftButtonPressed=function(t){return!!s.TouchUtils.isTouchEvent(t)||!!(t=r.Browser.IE&&i.isDefined(event)?event:t)&&(r.Browser.IE&&r.Browser.Version<11?!!r.Browser.MSTouchUI||t.button%2==1:r.Browser.WebKitFamily?"pointermove"===t.type||"pointerenter"===t.type||"pointerleave"===t.type?1===t.buttons:1===t.which:r.Browser.NetscapeFamily||r.Browser.Edge||r.Browser.IE&&r.Browser.Version>=11?e.isMoveEventName(t.type)?1===t.buttons:1===t.which:!r.Browser.Opera||0===t.button)},e.isMoveEventName=function(t){return t===s.TouchUtils.touchMouseMoveEventName||t===e.getMoveEventName()},e.getMoveEventName=function(){return window.PointerEvent?"pointermove":r.Browser.TouchUI?"touchmove":"mousemove"},e.preventEventAndBubble=function(t){e.preventEvent(t),t.stopPropagation&&t.stopPropagation(),t.cancelBubble=!0},e.clientEventRequiresDocScrollCorrection=function(){var e=r.Browser.Safari&&r.Browser.Version<3,t=r.Browser.MacOSMobilePlatform&&r.Browser.Version<5.1;return r.Browser.AndroidDefaultBrowser||r.Browser.AndroidChromeBrowser||!(e||t)},e.getEventX=function(t){return s.TouchUtils.isTouchEvent(t)?s.TouchUtils.getEventX(t):t.clientX+(e.clientEventRequiresDocScrollCorrection()?o.DomUtils.getDocumentScrollLeft():0)},e.getEventY=function(t){return s.TouchUtils.isTouchEvent(t)?s.TouchUtils.getEventY(t):t.clientY+(e.clientEventRequiresDocScrollCorrection()?o.DomUtils.getDocumentScrollTop():0)},e.cancelBubble=function(e){e.cancelBubble=!0},e.getWheelDelta=function(e){var t;return t=r.Browser.NetscapeFamily&&r.Browser.MajorVersion<17?-e.detail:r.Browser.Safari?e.wheelDelta:-e.deltaY,r.Browser.Opera&&r.Browser.Version<9&&(t=-t),t},e}();t.EvtUtils=a},9937:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.JsonUtils=void 0;var n=function(){function e(){}return e.isValid=function(e){return!/[^,:{}[\]0-9.\-+Eaeflnr-u \n\r\t]/.test(e.replace(/"(\\.|[^"\\])*"/g,""))},e}();t.JsonUtils=n},2153:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.KeyCode=t.ModifierKey=t.KeyUtils=void 0;var r,i,o=n(9279),s=n(9712),a=n(49),l=function(){function e(){}return e.getKeyModifiers=function(e){var t=0;return e.altKey&&(t|=r.Alt),e.ctrlKey&&(t|=r.Ctrl),e.shiftKey&&(t|=r.Shift),e.metaKey&&(t|=r.Meta),t},e.getShortcutCode=function(e,t,n,i,o){var s=e;return s|=t?r.Ctrl:0,s|=n?r.Shift:0,s|=i?r.Alt:0,s|=o?r.Meta:0},e.getShortcutCodeByEvent=function(t){return e.getShortcutCode(e.getEventKeyCode(t),t.ctrlKey,t.shiftKey,t.altKey,!!o.Browser.MacOSPlatform&&t.metaKey)},e.getEventKeyCode=function(e){return o.Browser.NetscapeFamily||o.Browser.Opera?e.which:e.keyCode},e.parseShortcutString=function(t){if(!t)return 0;var n=!1,r=!1,o=!1,l=!1,c=null,u=t.toString().split("+");if(u.length>0)for(var d=0;d<u.length;d++){var p=a.StringUtils.trim(u[d].toUpperCase());switch(p){case"CONTROL":case"CONTROLKEY":case"CTRL":n=!0;break;case"SHIFT":case"SHIFTKEY":r=!0;break;case"ALT":o=!0;break;case"CMD":l=!0;break;case"F1":c=i.F1;break;case"F2":c=i.F2;break;case"F3":c=i.F3;break;case"F4":c=i.F4;break;case"F5":c=i.F5;break;case"F6":c=i.F6;break;case"F7":c=i.F7;break;case"F8":c=i.F8;break;case"F9":c=i.F9;break;case"F10":c=i.F10;break;case"F11":c=i.F11;break;case"F12":c=i.F12;break;case"RETURN":case"ENTER":c=i.Enter;break;case"HOME":c=i.Home;break;case"END":c=i.End;break;case"LEFT":c=i.Left;break;case"RIGHT":c=i.Right;break;case"UP":c=i.Up;break;case"DOWN":c=i.Down;break;case"PAGEUP":c=i.PageUp;break;case"PAGEDOWN":c=i.PageDown;break;case"SPACE":c=i.Space;break;case"TAB":c=i.Tab;break;case"BACKSPACE":case"BACK":c=i.Backspace;break;case"CONTEXT":c=i.ContextMenu;break;case"ESCAPE":case"ESC":c=i.Esc;break;case"DELETE":case"DEL":c=i.Delete;break;case"INSERT":case"INS":c=i.Insert;break;case"PLUS":c="+".charCodeAt(0);break;default:c=p.charCodeAt(0)}}else alert(s.EncodeUtils.decodeViaTextArea("Invalid shortcut"));return e.getShortcutCode(c,n,r,o,l)},e}();t.KeyUtils=l,function(e){e[e.None=0]="None",e[e.Ctrl=65536]="Ctrl",e[e.Shift=262144]="Shift",e[e.Alt=1048576]="Alt",e[e.Meta=16777216]="Meta"}(r=t.ModifierKey||(t.ModifierKey={})),function(e){e[e.Backspace=8]="Backspace",e[e.Tab=9]="Tab",e[e.Enter=13]="Enter",e[e.Pause=19]="Pause",e[e.CapsLock=20]="CapsLock",e[e.Esc=27]="Esc",e[e.Space=32]="Space",e[e.PageUp=33]="PageUp",e[e.PageDown=34]="PageDown",e[e.End=35]="End",e[e.Home=36]="Home",e[e.Left=37]="Left",e[e.Up=38]="Up",e[e.Right=39]="Right",e[e.Down=40]="Down",e[e.Insert=45]="Insert",e[e.Delete=46]="Delete",e[e.Key_0=48]="Key_0",e[e.Key_1=49]="Key_1",e[e.Key_2=50]="Key_2",e[e.Key_3=51]="Key_3",e[e.Key_4=52]="Key_4",e[e.Key_5=53]="Key_5",e[e.Key_6=54]="Key_6",e[e.Key_7=55]="Key_7",e[e.Key_8=56]="Key_8",e[e.Key_9=57]="Key_9",e[e.Key_a=65]="Key_a",e[e.Key_b=66]="Key_b",e[e.Key_c=67]="Key_c",e[e.Key_d=68]="Key_d",e[e.Key_e=69]="Key_e",e[e.Key_f=70]="Key_f",e[e.Key_g=71]="Key_g",e[e.Key_h=72]="Key_h",e[e.Key_i=73]="Key_i",e[e.Key_j=74]="Key_j",e[e.Key_k=75]="Key_k",e[e.Key_l=76]="Key_l",e[e.Key_m=77]="Key_m",e[e.Key_n=78]="Key_n",e[e.Key_o=79]="Key_o",e[e.Key_p=80]="Key_p",e[e.Key_q=81]="Key_q",e[e.Key_r=82]="Key_r",e[e.Key_s=83]="Key_s",e[e.Key_t=84]="Key_t",e[e.Key_u=85]="Key_u",e[e.Key_v=86]="Key_v",e[e.Key_w=87]="Key_w",e[e.Key_x=88]="Key_x",e[e.Key_y=89]="Key_y",e[e.Key_z=90]="Key_z",e[e.Windows=91]="Windows",e[e.ContextMenu=93]="ContextMenu",e[e.Numpad_0=96]="Numpad_0",e[e.Numpad_1=97]="Numpad_1",e[e.Numpad_2=98]="Numpad_2",e[e.Numpad_3=99]="Numpad_3",e[e.Numpad_4=100]="Numpad_4",e[e.Numpad_5=101]="Numpad_5",e[e.Numpad_6=102]="Numpad_6",e[e.Numpad_7=103]="Numpad_7",e[e.Numpad_8=104]="Numpad_8",e[e.Numpad_9=105]="Numpad_9",e[e.Multiply=106]="Multiply",e[e.Add=107]="Add",e[e.Subtract=109]="Subtract",e[e.Decimal=110]="Decimal",e[e.Divide=111]="Divide",e[e.F1=112]="F1",e[e.F2=113]="F2",e[e.F3=114]="F3",e[e.F4=115]="F4",e[e.F5=116]="F5",e[e.F6=117]="F6",e[e.F7=118]="F7",e[e.F8=119]="F8",e[e.F9=120]="F9",e[e.F10=121]="F10",e[e.F11=122]="F11",e[e.F12=123]="F12",e[e.NumLock=144]="NumLock",e[e.ScrollLock=145]="ScrollLock",e[e.Semicolon=186]="Semicolon",e[e.Equals=187]="Equals",e[e.Comma=188]="Comma",e[e.Dash=189]="Dash",e[e.Period=190]="Period",e[e.ForwardSlash=191]="ForwardSlash",e[e.GraveAccent=192]="GraveAccent",e[e.OpenBracket=219]="OpenBracket",e[e.BackSlash=220]="BackSlash",e[e.CloseBracket=221]="CloseBracket",e[e.SingleQuote=222]="SingleQuote"}(i=t.KeyCode||(t.KeyCode={}))},2940:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ListUtils=void 0;var r=n(6799),i=n(4170),o=function(){function e(){}return e.remove=function(e,t){var n=e.indexOf(t,0);n>=0&&e.splice(n,1)},e.removeBy=function(e,t){for(var n=e.length,r=0;r<n;r++)if(t(e[r],r))return e.splice(r,1)[0];return null},e.shallowCopy=function(e){return e.slice()},e.deepCopy=function(t){return e.map(t,(function(e){return e.clone()}))},e.initByValue=function(e,t){for(var n=[];e>0;e--)n.push(t);return n},e.initByCallback=function(e,t){for(var n=[],r=0;r<e;r++)n.push(t(r));return n},e.forEachOnInterval=function(e,t){for(var n=e.end,r=e.start;r<n;r++)t(r)},e.reverseForEachOnInterval=function(e,t){for(var n=e.start,r=e.end-1;r>=n;r--)t(r)},e.reducedMap=function(e,t,n,r){void 0===n&&(n=0),void 0===r&&(r=e.length);for(var i=[],o=n;o<r;o++){var s=t(e[o],o);null!==s&&i.push(s)}return i},e.filter=function(e,t,n,r){void 0===n&&(n=0),void 0===r&&(r=e.length);for(var i=[],o=n;o<r;o++){var s=e[o];t(s,o)&&i.push(s)}return i},e.map=function(e,t,n,r){void 0===n&&(n=0),void 0===r&&(r=e.length);for(var i=[],o=n;o<r;o++)i.push(t(e[o],o));return i},e.indexBy=function(e,t,n,r){void 0===n&&(n=0),void 0===r&&(r=e.length);for(var i=n;i<r;i++)if(t(e[i],i))return i;return-1},e.reverseIndexBy=function(e,t,n,r){void 0===n&&(n=e.length-1),void 0===r&&(r=0);for(var i=n;i>=r;i--)if(t(e[i],i))return i;return-1},e.elementBy=function(t,n,r,i){void 0===r&&(r=0),void 0===i&&(i=t.length);var o=e.indexBy(t,n,r,i);return o<0?null:t[o]},e.reverseElementBy=function(t,n,r,i){void 0===r&&(r=t.length-1),void 0===i&&(i=0);var o=e.reverseIndexBy(t,n,r,i);return o<0?null:t[o]},e.last=function(e){return e[e.length-1]},e.setLast=function(e,t){return e[e.length-1]=t},e.incLast=function(e){return++e[e.length-1]},e.decLast=function(e){return--e[e.length-1]},e.equals=function(t,n){return t.length===n.length&&e.allOf2(t,n,(function(e,t){return e.equals(t)}))},e.equalsByReference=function(e,t){var n=e.length;if(n!==e.length)return!1;for(var r=0;r<n;r++)if(e[r]!==t[r])return!1;return!0},e.unique=function(t,n,r,i){void 0===r&&(r=n),void 0===i&&(i=function(){});var o=t.length;if(0===o)return[];var s=(t=t.sort(n))[0],a=e.reducedMap(t,(function(e){return 0!==r(s,e)?(s=e,e):(i(e),null)}),1,o);return a.unshift(t[0]),a},e.uniqueNumber=function(e){e=e.sort(i.Comparers.number);for(var t=Number.NaN,n=e.length-1;n>=0;n--)t===e[n]?e.splice(n,1):t=e[n];return e},e.forEach=function(e,t,n,r){void 0===n&&(n=0),void 0===r&&(r=e.length);for(var i=n;i<r;i++)t(e[i],i)},e.forEach2=function(e,t,n,r,i){void 0===r&&(r=0),void 0===i&&(i=e.length);for(var o=r;o<i;o++)n(e[o],t[o],o)},e.reverseForEach=function(e,t,n,r){void 0===n&&(n=e.length-1),void 0===r&&(r=0);for(var i=n;i>=r;i--)t(e[i],i)},e.reverseIndexOf=function(e,t,n,r){void 0===n&&(n=e.length-1),void 0===r&&(r=0);for(var i=n;i>=r;i--)if(e[i]===t)return i;return-1},e.accumulate=function(e,t,n,r,i){void 0===r&&(r=0),void 0===i&&(i=e.length);for(var o=t,s=r;s<i;s++)o=n(o,e[s],s);return o},e.accumulateNumber=function(e,t,n,r,i){void 0===n&&(n=0),void 0===r&&(r=0),void 0===i&&(i=e.length);for(var o=n,s=r;s<i;s++)o+=t(e[s],s,o);return o},e.anyOf=function(e,t,n,r){void 0===n&&(n=0),void 0===r&&(r=e.length);for(var i=n;i<r;i++)if(t(e[i],i))return!0;return!1},e.unsafeAnyOf=function(e,t,n,r){void 0===n&&(n=0),void 0===r&&(r=e.length);for(var i=n;i<r;i++){var o=t(e[i],i);if(o)return o}return null},e.reverseAnyOf=function(e,t,n,r){void 0===n&&(n=e.length-1),void 0===r&&(r=0);for(var i=n;i>=r;i--)if(t(e[i],i))return!0;return!1},e.unsafeReverseAnyOf=function(e,t,n,r){void 0===n&&(n=e.length-1),void 0===r&&(r=0);for(var i=n;i>=r;i--){var o=t(e[i],i);if(o)return o}return null},e.anyOf2=function(e,t,n,r,i){void 0===r&&(r=0),void 0===i&&(i=e.length);for(var o=r;o<i;o++)if(n(e[o],t[o],o))return!0;return!1},e.allOf=function(e,t,n,r){void 0===n&&(n=0),void 0===r&&(r=e.length);for(var i=n;i<r;i++)if(!t(e[i],i))return!1;return!0},e.allOf2=function(e,t,n,r,i){void 0===r&&(r=0),void 0===i&&(i=e.length);for(var o=r;o<i;o++)if(!n(e[o],t[o],o))return!1;return!0},e.allOfOnInterval=function(e,t){for(var n=e.end,r=e.start;r<n;r++)if(!t(r))return!1;return!0},e.addListOnTail=function(e,t){for(var n=0,r=void 0;r=t[n];n++)e.push(r);return e},e.joinLists=function(t){for(var n=[],r=1;r<arguments.length;r++)n[r-1]=arguments[r];return e.accumulate(n,[],(function(n,r){return e.addListOnTail(n,t(r)),n}))},e.push=function(e,t){return e.push(t),e},e.countIf=function(t,n){return e.accumulateNumber(t,(function(e,t){return n(e,t)?1:0}))},e.clear=function(e){e.splice(0)},e.merge=function(e,t,n,r,i,o){if(void 0===i&&(i=0),void 0===o&&(o=e.length),e=e.slice(i,o),o-i<2)return e;for(var s=(e=e.sort(t))[i],a=[s],l=i+1;l<o;l++){var c=e[l];n(s,c)?r(s,c):(s=c,a.push(s))}return a},e.min=function(t,n,r,i){void 0===r&&(r=0),void 0===i&&(i=t.length);var o=e.minExtended(t,n,r,i);return o?o.minElement:null},e.max=function(t,n,r,i){void 0===r&&(r=0),void 0===i&&(i=t.length);var o=e.maxExtended(t,n,r,i);return o?o.maxElement:null},e.minMax=function(t,n,i,o){void 0===i&&(i=0),void 0===o&&(o=t.length);var s=e.minMaxExtended(t,n,i,o);return s?new r.MinMax(s.minElement,s.maxElement):null},e.minExtended=function(e,t,n,i){if(void 0===n&&(n=0),void 0===i&&(i=e.length),0===e.length)return null;for(var o=e[n],s=t(o),a=n+1;a<i;a++){var l=e[a],c=t(l);c<s&&(s=c,o=l)}return new r.ExtendedMin(o,s)},e.maxExtended=function(e,t,n,i){if(void 0===n&&(n=0),void 0===i&&(i=e.length),0===e.length)return null;for(var o=e[n],s=t(o),a=n+1;a<i;a++){var l=e[a],c=t(l);c>s&&(s=c,o=l)}return new r.ExtendedMax(o,s)},e.minMaxExtended=function(e,t,n,i){if(void 0===n&&(n=0),void 0===i&&(i=e.length),0===e.length)return null;for(var o=e[n],s=o,a=t(o),l=a,c=n+1;c<i;c++){var u=e[c],d=t(u);d<a?(a=d,o=u):d>l&&(l=d,s=u)}return new r.ExtendedMinMax(o,a,s,l)},e.minByCmp=function(e,t,n,r){if(void 0===n&&(n=0),void 0===r&&(r=e.length),0===e.length)return null;for(var i=e[n],o=n+1;o<r;o++){var s=e[o];t(s,i)<0&&(i=s)}return i},e.maxByCmp=function(e,t,n,r){if(void 0===n&&(n=0),void 0===r&&(r=e.length),0===e.length)return null;for(var i=e[n],o=n+1;o<r;o++){var s=e[o];t(s,i)>0&&(i=s)}return i},e.minMaxByCmp=function(e,t,n,i){if(void 0===n&&(n=0),void 0===i&&(i=e.length),0===e.length)return null;for(var o=e[n],s=o,a=n+1;a<i;a++){var l=e[a],c=t(l,o);c>0?s=l:c<0&&(o=l)}return new r.MinMax(o,s)},e}();t.ListUtils=o},8679:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.MathUtils=void 0;var r=n(2940),i=function(){function e(){}return e.round=function(t,n){void 0===n&&(n=0);var r=e.powFactor[n];return Math.round(t*r)/r},e.numberCloseTo=function(e,t,n){return void 0===n&&(n=1e-5),Math.abs(e-t)<n},e.restrictValue=function(e,t,n){return n<t&&(n=t),e>n?n:e<t?t:e},e.getRandomInt=function(e,t){return Math.floor(Math.random()*(t-e+1))+e},e.generateGuid=function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)}))},e.powFactor=r.ListUtils.initByCallback(20,(function(e){return Math.pow(10,e)})),e.somePrimes=[1009,1013,1019,1021,1031,1033,1039,1049,1051,1061,1063,1069,1087,1091,1093,1097,1103,1109,1117,1123,1129,1151,1153,1163,1171,1181,1187,1193,1201,1213,1217,1223,1229,1231,1237,1249,1259,1277,1279,1283,1289,1291,1297,1301,1303,1307,1319,1321,1327,1361,1367,1373,1381,1399,1409,1423,1427,1429,1433,1439,1447,1451,1453,1459,1471,1481,1483,1487,1489,1493,1499,1511,1523,1531,1543,1549,1553,1559,1567,1571,1579,1583,1597,1601,1607,1609,1613,1619,1621,1627,1637,1657,1663,1667,1669,1693,1697,1699,1709,1721,1723,1733,1741,1747,1753,1759,1777,1783,1787,1789,1801,1811,1823,1831,1847,1861,1867,1871,1873,1877,1879,1889,1901,1907,1913,1931,1933,1949,1951,1973,1979,1987,1993,1997,1999,2003],e}();t.MathUtils=i},49:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.StringUtils=void 0;var n=function(){function e(){}return e.isAlpha=function(e){return e>="a"&&e<="z"||e>="A"&&e<="Z"},e.isDigit=function(e){return e>="0"&&e<="9"},e.stringHashCode=function(e){var t=0;if(0===e.length)return t;for(var n=e.length,r=0;r<n;r++)t=(t<<5)-t+e.charCodeAt(r),t|=0;return t},e.endsAt=function(e,t){var n=e.length-1,r=t.length-1,i=n-r;if(i<0)return!1;for(;n>=i;n--,r--)if(e[n]!==t[r])return!1;return!0},e.startsAt=function(e,t){return e.substr(0,t.length)===t},e.stringInLowerCase=function(e){return e.toLowerCase()===e},e.stringInUpperCase=function(e){return e.toUpperCase()===e},e.atLeastOneSymbolInUpperCase=function(t){for(var n=0,r=void 0;r=t[n];n++)if(e.stringInUpperCase(r)&&!e.stringInLowerCase(r))return!0;return!1},e.getSymbolFromEnd=function(e,t){return e[e.length-t]},e.trim=function(t,n){if(void 0===n)return e.trimInternal(t,!0,!0);var r=n.join("");return t.replace(new RegExp("(^["+r+"]*)|(["+r+"]*$)","g"),"")},e.trimStart=function(t,n){if(void 0===n)return e.trimInternal(t,!0,!1);var r=n.join("");return t.replace(new RegExp("^["+r+"]*","g"),"")},e.trimEnd=function(t,n){if(void 0===n)return e.trimInternal(t,!1,!0);var r=n.join("");return t.replace(new RegExp("["+r+"]*$","g"),"")},e.getDecimalSeparator=function(){return 1.1.toLocaleString().substr(1,1)},e.repeat=function(e,t){return new Array(t<=0?0:t+1).join(e)},e.isNullOrEmpty=function(e){return!e||!e.length},e.padLeft=function(t,n,r){return e.repeat(r,Math.max(0,n-t.length))+t},e.trimInternal=function(e,t,n){var r=e.length;if(!r)return e;if(r<764833){var i=e;return t&&(i=i.replace(/^\s+/,"")),n&&(i=i.replace(/\s+$/,"")),i}var o=0;if(n)for(;r>0&&/\s/.test(e[r-1]);)r--;if(t&&r>0)for(;o<r&&/\s/.test(e[o]);)o++;return e.substring(o,r)},e}();t.StringUtils=n},1632:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TouchUtils=void 0;var r=n(9279),i=n(2491),o=function(){function e(){}return e.onEventAttachingToDocument=function(t,n){return!r.Browser.MacOSMobilePlatform||!e.isTouchEventName(t)||(e.documentTouchHandlers[t]||(e.documentTouchHandlers[t]=[]),e.documentTouchHandlers[t].push(n),e.documentEventAttachingAllowed)},e.isTouchEventName=function(e){return r.Browser.WebKitTouchUI&&(e.indexOf("touch")>-1||e.indexOf("gesture")>-1)},e.isTouchEvent=function(e){return r.Browser.WebKitTouchUI&&i.isDefined(e.changedTouches)},e.getEventX=function(e){return r.Browser.IE?e.pageX:e.changedTouches[0].pageX},e.getEventY=function(e){return r.Browser.IE?e.pageY:e.changedTouches[0].pageY},e.touchMouseDownEventName=r.Browser.WebKitTouchUI?"touchstart":r.Browser.Edge&&r.Browser.MSTouchUI&&window.PointerEvent?"pointerdown":"mousedown",e.touchMouseUpEventName=r.Browser.WebKitTouchUI?"touchend":r.Browser.Edge&&r.Browser.MSTouchUI&&window.PointerEvent?"pointerup":"mouseup",e.touchMouseMoveEventName=r.Browser.WebKitTouchUI?"touchmove":r.Browser.Edge&&r.Browser.MSTouchUI&&window.PointerEvent?"pointermove":"mousemove",e.msTouchDraggableClassName="dxMSTouchDraggable",e.documentTouchHandlers={},e.documentEventAttachingAllowed=!0,e}();t.TouchUtils=o},8721:(e,t,n)=>{n.r(t)},639:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.BarManager=void 0;var n=function(){function e(e,t){this.commandManager=e,this.bars=t}return e.prototype.updateContextMenu=function(){for(var e=0,t=void 0;t=this.bars[e];e++)if(t.isContextMenu()){t.updateItemsList();for(var n=t.getCommandKeys(),r=0;r<n.length;r++)this.updateBarItem(t,n[r])}},e.prototype.updateItemsState=function(e){for(var t=!!e.length,n=function(n,i){if(i.isVisible()){for(var o=i.getCommandKeys(),s=function(n){if(t&&!e.filter((function(e){return e==o[n]})).length)return"continue";r.updateBarItem(i,o[n])},a=0;a<o.length;a++)s(a);i.completeUpdate()}},r=this,i=0,o=void 0;o=this.bars[i];i++)n(0,o)},e.prototype.updateBarItem=function(e,t){var n=this.commandManager.getCommand(t);if(n){var r=n.getState();e.setItemVisible(t,r.visible),r.visible&&(e.setItemEnabled(t,r.enabled),e.setItemValue(t,r.value))}},e}();t.BarManager=n},3290:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.GanttClientCommand=void 0,function(e){e[e.CreateTask=0]="CreateTask",e[e.CreateSubTask=1]="CreateSubTask",e[e.RemoveTask=2]="RemoveTask",e[e.RemoveDependency=3]="RemoveDependency",e[e.TaskInformation=4]="TaskInformation",e[e.TaskAddContextItem=5]="TaskAddContextItem",e[e.Undo=6]="Undo",e[e.Redo=7]="Redo",e[e.ZoomIn=8]="ZoomIn",e[e.ZoomOut=9]="ZoomOut",e[e.FullScreen=10]="FullScreen",e[e.CollapseAll=11]="CollapseAll",e[e.ExpandAll=12]="ExpandAll",e[e.ResourceManager=13]="ResourceManager",e[e.ToggleResources=14]="ToggleResources",e[e.ToggleDependencies=15]="ToggleDependencies"}(t.GanttClientCommand||(t.GanttClientCommand={}))},3756:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.CollapseAllCommand=void 0;var r=n(655),i=n(9687),o=n(4409),s=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r.__extends(t,e),t.prototype.getState=function(){return new o.SimpleCommandState(this.isEnabled())},t.prototype.execute=function(){return e.prototype.execute.call(this)},t.prototype.executeInternal=function(){return this.control.collapseAll(),!0},t.prototype.isEnabled=function(){return!0},t}(i.CommandBase);t.CollapseAllCommand=s},6585:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ExpandAllCommand=void 0;var r=n(655),i=n(9687),o=n(4409),s=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r.__extends(t,e),t.prototype.getState=function(){return new o.SimpleCommandState(this.isEnabled())},t.prototype.execute=function(){return e.prototype.execute.call(this)},t.prototype.executeInternal=function(){return this.control.expandAll(),!0},t.prototype.isEnabled=function(){return!0},t}(i.CommandBase);t.ExpandAllCommand=s},9687:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.CommandBase=void 0;var n=function(){function e(e){this.control=e}return Object.defineProperty(e.prototype,"modelManipulator",{get:function(){return this.control.modelManipulator},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"history",{get:function(){return this.control.history},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"validationController",{get:function(){return this.control.validationController},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"state",{get:function(){return this._state||(this._state=this.getState()),this._state},enumerable:!1,configurable:!0}),e.prototype.execute=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];if(!this.state.enabled)return!1;var n=this.executeInternal.apply(this,e);return n&&this.control.barManager.updateItemsState([]),n},e.prototype.isEnabled=function(){return this.control.settings.editing.enabled},e.prototype.executeInternal=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];throw new Error("Not implemented")},e}();t.CommandBase=n},7156:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.CommandManager=void 0;var r=n(5237),i=n(126),o=n(5477),s=n(6152),a=n(3290),l=n(3756),c=n(6585),u=n(4118),d=n(3044),p=n(9762),h=n(7823),f=n(3250),g=n(9072),y=n(1955),m=n(1757),v=n(7977),T=n(9793),b=n(358),k=n(9791),S=n(3936),_=n(5258),w=n(2018),E=n(4195),D=n(2714),I=n(299),C=n(2231),P=function(){function e(e){this.control=e,this.commands={},this.createCommand(a.GanttClientCommand.CreateTask,this.createTaskCommand),this.createCommand(a.GanttClientCommand.CreateSubTask,this.createSubTaskCommand),this.createCommand(a.GanttClientCommand.RemoveTask,this.removeTaskCommand),this.createCommand(a.GanttClientCommand.RemoveDependency,this.removeDependencyCommand),this.createCommand(a.GanttClientCommand.TaskInformation,this.showTaskEditDialog),this.createCommand(a.GanttClientCommand.ResourceManager,this.showResourcesDialog),this.createCommand(a.GanttClientCommand.TaskAddContextItem,new D.TaskAddContextItemCommand(this.control)),this.createCommand(a.GanttClientCommand.Undo,new g.UndoCommand(this.control)),this.createCommand(a.GanttClientCommand.Redo,new f.RedoCommand(this.control)),this.createCommand(a.GanttClientCommand.ZoomIn,new I.ZoomInCommand(this.control)),this.createCommand(a.GanttClientCommand.ZoomOut,new C.ZoomOutCommand(this.control)),this.createCommand(a.GanttClientCommand.FullScreen,new h.ToggleFullScreenCommand(this.control)),this.createCommand(a.GanttClientCommand.CollapseAll,new l.CollapseAllCommand(this.control)),this.createCommand(a.GanttClientCommand.ExpandAll,new c.ExpandAllCommand(this.control)),this.createCommand(a.GanttClientCommand.ToggleResources,this.toggleResources),this.createCommand(a.GanttClientCommand.ToggleDependencies,this.toggleDependencies)}return Object.defineProperty(e.prototype,"createTaskCommand",{get:function(){return new _.CreateTaskCommand(this.control)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"createSubTaskCommand",{get:function(){return new S.CreateSubTaskCommand(this.control)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"removeTaskCommand",{get:function(){return new E.RemoveTaskCommand(this.control)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"updateTaskCommand",{get:function(){return new w.UpdateTaskCommand(this.control)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"createDependencyCommand",{get:function(){return new u.CreateDependencyCommand(this.control)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"removeDependencyCommand",{get:function(){return new d.RemoveDependencyCommand(this.control)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"createResourceCommand",{get:function(){return new m.CreateResourceCommand(this.control)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"removeResourceCommand",{get:function(){return new b.RemoveResourceCommand(this.control)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"assignResourceCommand",{get:function(){return new y.AssignResourceCommand(this.control)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"deassignResourceCommand",{get:function(){return new v.DeassignResourceCommand(this.control)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"changeResourceColorCommand",{get:function(){return new T.ResourceColorCommand(this.control)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"showTaskEditDialog",{get:function(){return new s.TaskEditDialogCommand(this.control)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"showConstraintViolationDialog",{get:function(){return new i.ConstraintViolationDialogCommand(this.control)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"showConfirmationDialog",{get:function(){return new r.ConfirmationDialog(this.control)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"showResourcesDialog",{get:function(){return new o.ResourcesDialogCommand(this.control)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"toggleResources",{get:function(){return new k.ToggleResourceCommand(this.control)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"toggleDependencies",{get:function(){return new p.ToggleDependenciesCommand(this.control)},enumerable:!1,configurable:!0}),e.prototype.getCommand=function(e){return this.commands[e]},e.prototype.createCommand=function(e,t){this.commands[e]=t},e}();t.CommandManager=P},4118:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.CreateDependencyCommand=void 0;var r=n(655),i=n(5950),o=n(3279),s=n(1211),a=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r.__extends(t,e),t.prototype.execute=function(t,n,r){return e.prototype.execute.call(this,t,n,r)},t.prototype.executeInternal=function(e,t,n){if(this.control.viewModel.dependencies.items.filter((function(n){return n.predecessorId===e&&n.successorId===t||n.successorId===e&&n.predecessorId===t})).length)return!1;var r=new o.DependencyInsertingArguments(e,t,n);if(this.modelManipulator.dispatcher.notifyDependencyInserting(r),r.cancel)return!1;if(e=r.predecessorId,t=r.successorId,n=r.type,this.control.history.beginTransaction(),this.history.addAndRedo(new s.InsertDependencyHistoryItem(this.modelManipulator,e,t,n)),this.control.isValidateDependenciesRequired()){var a=this.control.viewModel.tasks.getItemById(e);n===i.DependencyType.SF||n===i.DependencyType.SS?this.control.validationController.moveStartDependTasks(e,a.start):this.control.validationController.moveEndDependTasks(e,a.end)}return this.control.history.endTransaction(),!0},t.prototype.isEnabled=function(){return e.prototype.isEnabled.call(this)&&this.control.settings.editing.allowDependencyInsert},t}(n(2291).DependencyCommandBase);t.CreateDependencyCommand=a},2291:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DependencyCommandBase=void 0;var r=n(655),i=n(9687),o=n(4409),s=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r.__extends(t,e),t.prototype.getState=function(){return new o.SimpleCommandState(this.isEnabled())},t}(i.CommandBase);t.DependencyCommandBase=s},3044:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.RemoveDependencyCommand=void 0;var r=n(655),i=n(9544),o=n(5139),s=n(4797),a=n(5865),l=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r.__extends(t,e),t.prototype.execute=function(t,n){var r=this;return void 0===n&&(n=!0),n?(this.control.commandManager.showConfirmationDialog.execute(new o.ConfirmationDialogParameters(i.ConfirmationType.DependencyDelete,(function(){r.executeInternal(t)}))),!1):e.prototype.execute.call(this,t)},t.prototype.executeInternal=function(e){if(null!=(e=e||this.control.taskEditController.dependencyId)){var t=this.control.viewModel.dependencies.items.filter((function(t){return t.internalId===e}))[0];if(t){var n=new s.DependencyRemovingArguments(t);if(this.modelManipulator.dispatcher.notifyDependencyRemoving(n),!n.cancel)return this.history.addAndRedo(new a.RemoveDependencyHistoryItem(this.modelManipulator,e)),e===this.control.taskEditController.dependencyId&&this.control.taskEditController.selectDependency(null),this.control.barManager.updateItemsState([]),!0}}return!1},t.prototype.isEnabled=function(){return e.prototype.isEnabled.call(this)&&this.control.settings.editing.allowDependencyDelete},t.prototype.getState=function(){var t=e.prototype.getState.call(this);return t.visible=t.enabled&&null!=this.control.taskEditController.dependencyId,t},t}(n(2291).DependencyCommandBase);t.RemoveDependencyCommand=l},9762:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ToggleDependenciesCommand=void 0;var r=n(655),i=n(9687),o=n(4409),s=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r.__extends(t,e),t.prototype.getState=function(){return new o.SimpleCommandState(!0)},t.prototype.execute=function(){return e.prototype.execute.call(this)},t.prototype.executeInternal=function(){return this.control.toggleDependencies(),!0},t}(i.CommandBase);t.ToggleDependenciesCommand=s},7823:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ToggleFullScreenCommand=void 0;var r=n(655),i=n(9687),o=n(4409),s=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.isInFullScreenMode=!1,t.fullScreenTempVars={},t}return r.__extends(t,e),t.prototype.getState=function(){var e=new o.SimpleCommandState(!0);return e.value=this.control.fullScreenModeHelper.isInFullScreenMode,e},t.prototype.execute=function(){return e.prototype.execute.call(this)},t.prototype.executeInternal=function(){return this.control.fullScreenModeHelper.toggle(),!0},t}(i.CommandBase);t.ToggleFullScreenCommand=s},3250:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.RedoCommand=void 0;var r=n(655),i=n(9687),o=n(4409),s=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r.__extends(t,e),t.prototype.getState=function(){var e=new o.SimpleCommandState(this.isEnabled());return e.visible=this.control.settings.editing.enabled,e},t.prototype.execute=function(){return e.prototype.execute.call(this)},t.prototype.executeInternal=function(){return this.history.redo(),!0},t.prototype.isEnabled=function(){return e.prototype.isEnabled.call(this)&&this.history.canRedo()},t}(i.CommandBase);t.RedoCommand=s},9072:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.UndoCommand=void 0;var r=n(655),i=n(9687),o=n(4409),s=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r.__extends(t,e),t.prototype.getState=function(){var e=new o.SimpleCommandState(this.isEnabled());return e.visible=this.control.settings.editing.enabled,e},t.prototype.execute=function(){return e.prototype.execute.call(this)},t.prototype.executeInternal=function(){return this.history.undo(),!0},t.prototype.isEnabled=function(){return e.prototype.isEnabled.call(this)&&this.history.canUndo()},t}(i.CommandBase);t.UndoCommand=s},1955:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.AssignResourceCommand=void 0;var r=n(655),i=n(1389),o=n(3683),s=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r.__extends(t,e),t.prototype.execute=function(t,n){return e.prototype.execute.call(this,t,n)},t.prototype.executeInternal=function(e,t){if(!this.control.viewModel.assignments.items.filter((function(n){return n.resourceId===e&&n.taskId===t}))[0]){var n=this.control.viewModel,r=new i.ResourceAssigningArguments(n.convertInternalToPublicKey("resource",e),n.convertInternalToPublicKey("task",t));if(this.modelManipulator.dispatcher.notifyResourceAssigning(r),!r.cancel)return this.history.addAndRedo(new o.AssignResourceHistoryItem(this.modelManipulator,n.convertPublicToInternalKey("resource",r.resourceId),n.convertPublicToInternalKey("task",r.taskId))),!0}return!1},t.prototype.isEnabled=function(){return e.prototype.isEnabled.call(this)&&this.control.settings.editing.allowTaskResourceUpdate},t}(n(200).ResourceCommandBase);t.AssignResourceCommand=s},1757:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.CreateResourceCommand=void 0;var r=n(655),i=n(990),o=n(2961),s=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r.__extends(t,e),t.prototype.execute=function(t,n,r){return void 0===n&&(n=""),e.prototype.execute.call(this,t,n,r)},t.prototype.executeInternal=function(e,t,n){void 0===t&&(t="");var r=new i.ResourceInsertingArguments(e,t);return this.modelManipulator.dispatcher.notifyResourceCreating(r),r.cancel||this.history.addAndRedo(new o.CreateResourceHistoryItem(this.modelManipulator,r.text,r.color,n)),!r.cancel},t.prototype.isEnabled=function(){return e.prototype.isEnabled.call(this)&&this.control.settings.editing.allowResourceInsert},t}(n(200).ResourceCommandBase);t.CreateResourceCommand=s},7977:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DeassignResourceCommand=void 0;var r=n(655),i=n(1493),o=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r.__extends(t,e),t.prototype.execute=function(t){return e.prototype.execute.call(this,t)},t.prototype.executeInternal=function(e){var t=this.control.viewModel.assignments.items.filter((function(t){return t.internalId===e}))[0];return!(!t||!this.modelManipulator.dispatcher.fireResourceUnassigning(t))&&(this.history.addAndRedo(new i.DeassignResourceHistoryItem(this.modelManipulator,e)),!0)},t.prototype.isEnabled=function(){return e.prototype.isEnabled.call(this)&&this.control.settings.editing.allowTaskResourceUpdate},t}(n(200).ResourceCommandBase);t.DeassignResourceCommand=o},9793:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ResourceColorCommand=void 0;var r=n(655),i=n(4641),o=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r.__extends(t,e),t.prototype.execute=function(t,n){return e.prototype.execute.call(this,t,n)},t.prototype.executeInternal=function(e,t){return this.control.viewModel.resources.getItemById(e).color!==t&&(this.history.addAndRedo(new i.ResourceColorHistoryItem(this.modelManipulator,e,t)),!0)},t}(n(9876).ResourcePropertyCommandBase);t.ResourceColorCommand=o},9876:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ResourcePropertyCommandBase=void 0;var r=n(655),i=n(9687),o=n(4409),s=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r.__extends(t,e),t.prototype.getState=function(){return new o.SimpleCommandState(this.isEnabled())},t.prototype.isEnabled=function(){return e.prototype.isEnabled.call(this)&&this.control.settings.editing.allowResourceUpdate},t}(i.CommandBase);t.ResourcePropertyCommandBase=s},358:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.RemoveResourceCommand=void 0;var r=n(655),i=n(9748),o=n(7466),s=n(1493),a=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r.__extends(t,e),t.prototype.execute=function(t){return e.prototype.execute.call(this,t)},t.prototype.executeInternal=function(e){var t=this,n=this.control.viewModel.resources.items.filter((function(t){return t.internalId===e}))[0];if(n){var r=new i.ResourceRemovingArguments(n);if(this.modelManipulator.dispatcher.notifyResourceRemoving(r),!r.cancel){var a=new o.RemoveResourceHistoryItem(this.modelManipulator,e);return this.control.viewModel.assignments.items.filter((function(t){return t.resourceId===e})).forEach((function(e){t.modelManipulator.dispatcher.fireResourceUnassigning(e)&&a.add(new s.DeassignResourceHistoryItem(t.modelManipulator,e.internalId))})),this.history.addAndRedo(a),!0}}return!1},t.prototype.isEnabled=function(){return e.prototype.isEnabled.call(this)&&this.control.settings.editing.allowResourceDelete},t}(n(200).ResourceCommandBase);t.RemoveResourceCommand=a},200:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ResourceCommandBase=void 0;var r=n(655),i=n(9687),o=n(4409),s=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r.__extends(t,e),t.prototype.getState=function(){return new o.SimpleCommandState(this.isEnabled())},t}(i.CommandBase);t.ResourceCommandBase=s},9791:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ToggleResourceCommand=void 0;var r=n(655),i=n(9687),o=n(4409),s=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r.__extends(t,e),t.prototype.getState=function(){return new o.SimpleCommandState(!0)},t.prototype.execute=function(){return e.prototype.execute.call(this)},t.prototype.executeInternal=function(){return this.control.toggleResources(),!0},t}(i.CommandBase);t.ToggleResourceCommand=s},4409:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SimpleCommandState=void 0;var n=function(e,t){this.visible=!0,this.enabled=e,this.value=t};t.SimpleCommandState=n},3936:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.CreateSubTaskCommand=void 0;var r=n(655),i=n(4605),o=n(1284),s=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r.__extends(t,e),t.prototype.execute=function(t){return e.prototype.execute.call(this,t)},t.prototype.executeInternal=function(t){t=t||this.control.currentSelectedTaskID;var n=this.control.viewModel.findItem(t);if(n.selected){var r={start:new Date(n.task.start.getTime()),end:new Date(n.task.end.getTime()),title:"New task",progress:0,parentId:t},s=new i.TaskInsertingArguments(null,r);if(this.modelManipulator.dispatcher.notifyTaskCreating(s),!s.cancel){this.history.addAndRedo(new o.CreateTaskHistoryItem(this.modelManipulator,s));var a=this.control.viewModel.findItem(r.parentId);e.prototype.updateParent.call(this,a)}return!s.cancel}return!1},t.prototype.isEnabled=function(){var t=this.control,n=t.viewModel.findItem(t.currentSelectedTaskID);return e.prototype.isEnabled.call(this)&&!!n&&n.selected},t.prototype.getState=function(){var t=e.prototype.getState.call(this),n=this.control;return t.visible=t.visible&&n.settings.editing.allowTaskInsert,t},t}(n(9254).TaskCommandBase);t.CreateSubTaskCommand=s},5258:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.CreateTaskCommand=void 0;var r=n(655),i=n(4605),o=n(1284),s=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r.__extends(t,e),t.prototype.execute=function(t){return e.prototype.execute.call(this,t)},t.prototype.executeInternal=function(t){var n,r;if(null!=t||(t={}),!t.parentId){var s=this.control.viewModel.findItem(this.control.currentSelectedTaskID),a=s&&s.task;a&&(t.parentId=a.parentId)}var l=this.control.viewModel.findItem(t.parentId)||this.control.viewModel.items[0],c=l&&l.task;t.start||(t.start=c?new Date(c.start.getTime()):new Date(this.control.range.start.getTime())),t.end||(t.end=c?new Date(c.end.getTime()):new Date(this.control.range.end.getTime())),null!==(n=t.title)&&void 0!==n||(t.title="New task"),null!==(r=t.progress)&&void 0!==r||(t.progress=0);var u=new i.TaskInsertingArguments(null,t);if(this.modelManipulator.dispatcher.notifyTaskCreating(u),!u.cancel){this.history.addAndRedo(new o.CreateTaskHistoryItem(this.modelManipulator,u));var d=this.control.viewModel.findItem(t.parentId);e.prototype.updateParent.call(this,d)}return!u.cancel},t.prototype.getState=function(){var t=e.prototype.getState.call(this);return t.visible=t.visible&&this.control.settings.editing.allowTaskInsert,t},t}(n(9254).TaskCommandBase);t.CreateTaskCommand=s},4195:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.RemoveTaskCommand=void 0;var r=n(655),i=n(9544),o=n(5139),s=n(4642),a=n(5865),l=n(1493),c=n(9599),u=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r.__extends(t,e),t.prototype.execute=function(t,n,r,s,a,l){var c=this;return void 0===n&&(n=!0),void 0===r&&(r=!1),void 0===s&&(s=!0),this.isApiCall=r,this.isUpdateParentTaskRequired=s,n?(this.control.commandManager.showConfirmationDialog.execute(new o.ConfirmationDialogParameters(i.ConfirmationType.TaskDelete,(function(){c.executeInternal(t,a,l)}))),!1):e.prototype.execute.call(this,t,a,l)},t.prototype.executeInternal=function(n,r,i){var o=this,u=i||[];n=n||this.control.currentSelectedTaskID;var d=this.control.viewModel.findItem(n),p=d?d.task:this.control.viewModel.tasks.getItemById(n),h=new s.TaskRemovingArguments(p);if(this.modelManipulator.dispatcher.notifyTaskRemoving(h),h.cancel)return!1;var f=this.history,g=this.control.viewModel;f.beginTransaction(),g.beginUpdate();var y=!!r,m=new c.RemoveTaskHistoryItem(this.modelManipulator,n),v=g.tasks.items.filter((function(e){return e.parentId===n})),T=v.map((function(e){return e.internalId})),b=g.dependencies.items.filter((function(e){return-1===u.indexOf(e.internalId)&&(e.predecessorId===n||e.successorId===n)&&!T.some((function(t){return e.predecessorId===t||e.successorId===t}))}));if(b.length){if(!this.control.settings.editing.allowDependencyDelete)return!1;b.forEach((function(e){m.add(new a.RemoveDependencyHistoryItem(o.modelManipulator,e.internalId)),u.push(e.internalId)}))}if(g.assignments.items.filter((function(e){return e.taskId===n})).forEach((function(e){o.modelManipulator.dispatcher.fireResourceUnassigning(e)&&m.add(new l.DeassignResourceHistoryItem(o.modelManipulator,e.internalId))})),v.reverse().forEach((function(e){return new t(o.control).execute(e.internalId,!1,!0,!1,m,u)})),y?r.add(m):f.addAndRedo(m),this.isUpdateParentTaskRequired){var k=this.control.viewModel.findItem(p.parentId);e.prototype.updateParent.call(this,k)}return f.endTransaction(),g.endUpdate(),!0},t.prototype.isEnabled=function(){var t=this.control,n=t.viewModel.findItem(t.currentSelectedTaskID);return e.prototype.isEnabled.call(this)&&(!!n&&n.selected||this.isApiCall)},t.prototype.getState=function(){var t=e.prototype.getState.call(this),n=this.control;return t.visible=t.visible&&n.settings.editing.allowTaskDelete,t},t}(n(9254).TaskCommandBase);t.RemoveTaskCommand=u},2714:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TaskAddContextItemCommand=void 0;var r=n(655),i=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r.__extends(t,e),t.prototype.getState=function(){var t=e.prototype.getState.call(this);return t.visible=t.visible&&this.control.settings.editing.allowTaskInsert,t},t.prototype.execute=function(){return!1},t}(n(9254).TaskCommandBase);t.TaskAddContextItemCommand=i},9254:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TaskCommandBase=void 0;var r=n(655),i=n(9687),o=n(4409),s=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.isApiCall=!1,t}return r.__extends(t,e),t.prototype.getState=function(){var e=new o.SimpleCommandState(this.isEnabled());return e.visible=this.control.settings.editing.enabled&&!this.control.taskEditController.dependencyId,e},t.prototype.updateParent=function(e){this.validationController._parentAutoCalc&&e&&e.children.length>0&&this.control.validationController.updateParentsIfRequired(e.children[0].task.internalId)},t}(i.CommandBase);t.TaskCommandBase=s},2018:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.UpdateTaskCommand=void 0;var r=n(655),i=n(2491),o=n(9544),s=n(3433),a=n(5865),l=n(9496),c=n(9201),u=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r.__extends(t,e),t.prototype.execute=function(t,n){return e.prototype.execute.call(this,t,n)},t.prototype.executeInternal=function(e,t){var n=this.control.viewModel.tasks.getItemById(e);if(!n)return!1;var r=this.control.modelManipulator.dispatcher.raiseTaskUpdating(n,t,(function(e){t.title=e.title,t.progress=e.progress,t.start=e.start,t.end=e.end,t.color=e.color}));if(r){(0,i.isDefined)(t.start)&&(0,i.isDefined)(t.end)&&t.end.getTime()<t.start.getTime()&&(t.end=t.start),(0,i.isDefined)(t.progress)&&(t.progress=Math.max(Math.min(t.progress,100),0));var o=this.filterChangedValues(t,n);this.processDependecyValidation(o,n)}return r},t.prototype.isEnabled=function(){return e.prototype.isEnabled.call(this)&&this.control.settings.editing.allowTaskUpdate},t.prototype.filterChangedValues=function(e,t){if(!e)return null;var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(0,i.isDefined)(e[r])&&t[r]!==e[r]&&(n[r]=e[r]);return n},t.prototype.processDependecyValidation=function(e,t){var n=this,r=function(r){n.onAfterValidationCallback(e,t,r)},o=this.control.isValidateDependenciesRequired();if(o){var a=[],l=(0,i.isDefined)(e.start)&&e.start!==t.start,c=(0,i.isDefined)(e.end)&&e.end!==t.end;l&&o&&(a=a.concat(this.control.validationController.checkStartDependencies(t.internalId,e.start))),c&&o&&(a=a.concat(this.control.validationController.checkEndDependencies(t.internalId,e.end))),a.length>0?this.control.commandManager.showConstraintViolationDialog.execute(new s.ConstraintViolationDialogParameters(a,r)):r(null)}else r(null)},t.prototype.onAfterValidationCallback=function(e,t,n){var r=this;if(!n||n.option!==o.ConstraintViolationOption.DoNothing||(delete e.start,delete e.end),Object.keys(e).length>0){this.history.beginTransaction(),(null==n?void 0:n.option)===o.ConstraintViolationOption.RemoveDependency&&n.validationErrors.forEach((function(e){return r.history.addAndRedo(new a.RemoveDependencyHistoryItem(r.modelManipulator,e.dependencyId))}));var s=this.control.isValidateDependenciesRequired(),c=t.internalId,u=t.start,d=t.end;this.history.addAndRedo(new l.UpdateTaskHistoryItem(this.modelManipulator,c,e)),(0,i.isDefined)(e.start)&&s&&this.control.validationController.moveStartDependTasks(c,u),(0,i.isDefined)(e.end)&&s&&this.control.validationController.moveEndDependTasks(c,d),this.processAutoParentUpdate(c,e,u,d),this.history.endTransaction(),(null==n?void 0:n.option)!==o.ConstraintViolationOption.RemoveDependency&&(null==n?void 0:n.option)!==o.ConstraintViolationOption.KeepDependency||this.control.updateBarManager(),this.control.updateViewDataRange()}},t.prototype.processAutoParentUpdate=function(e,t,n,r){var o=(0,i.isDefined)(t.start),s=(0,i.isDefined)(t.end),a=(0,i.isDefined)(t.progress)||o||s,l=o?t.start.getTime()-n.getTime():null,u=s?t.end.getTime()-r.getTime():null,d=o&&0!==c.DateUtils.getTimezoneOffsetDiff(n,t.start),p=s&&0!==c.DateUtils.getTimezoneOffsetDiff(r,t.end),h=(d||p)&&Math.abs(u-l)===c.DateUtils.msPerHour;a?0!==l&&(l===u||h)?this.validationController.correctParentsOnChildMoving(e,l):this.validationController.updateParentsIfRequired(e):this.control.updateOwnerInAutoParentMode()},t}(n(9254).TaskCommandBase);t.UpdateTaskCommand=u},299:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ZoomInCommand=void 0;var r=n(655),i=n(9687),o=n(4409),s=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r.__extends(t,e),t.prototype.getState=function(){return new o.SimpleCommandState(!0)},t.prototype.execute=function(){return e.prototype.execute.call(this)},t.prototype.executeInternal=function(){return this.control.zoomIn(),!0},t}(i.CommandBase);t.ZoomInCommand=s},2231:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ZoomOutCommand=void 0;var r=n(655),i=n(9687),o=n(4409),s=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r.__extends(t,e),t.prototype.getState=function(){return new o.SimpleCommandState(!0)},t.prototype.execute=function(){return e.prototype.execute.call(this)},t.prototype.executeInternal=function(){return this.control.zoomOut(),!0},t}(i.CommandBase);t.ZoomOutCommand=s},5237:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ConfirmationDialog=void 0;var r=n(655),i=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r.__extends(t,e),t.prototype.applyParameters=function(e,t){return this.history.beginTransaction(),t.callback(),this.history.endTransaction(),this.control.barManager.updateItemsState([]),!0},t.prototype.createParameters=function(e){return e},t.prototype.getDialogName=function(){return"Confirmation"},t}(n(4730).DialogBase);t.ConfirmationDialog=i},126:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ConstraintViolationDialogCommand=void 0;var r=n(655),i=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r.__extends(t,e),t.prototype.applyParameters=function(e,t){return t.callback(e),!0},t.prototype.createParameters=function(e){return e},t.prototype.getDialogName=function(){return"ConstraintViolation"},t}(n(4730).DialogBase);t.ConstraintViolationDialogCommand=i},4730:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DialogBase=void 0;var r=n(655),i=n(9687),o=n(4409),s=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.isApiCall=!1,t._canRefresh=!0,t}return r.__extends(t,e),t.prototype.execute=function(t,n){return void 0===t&&(t=void 0),void 0===n&&(n=!1),this.isApiCall=n,e.prototype.execute.call(this,t)},t.prototype.executeInternal=function(e){return this.options=e,this.showDialog(e)},Object.defineProperty(t.prototype,"canRefresh",{get:function(){return this._canRefresh},enumerable:!1,configurable:!0}),t.prototype.refresh=function(){this.showDialog(this.options)},t.prototype.showDialog=function(e){var n=this,r=this.createParameters(e),i=r.clone();return!!this.onBeforeDialogShow(r)&&(t.activeInstance=this,this.control.showDialog(this.getDialogName(),r,(function(e){e&&(n._canRefresh=!1,n.applyParameters(e,i),n._canRefresh=!0)}),(function(){delete t.activeInstance,n.afterClosing()})),!0)},t.prototype.onBeforeDialogShow=function(e){return!0},t.prototype.applyParameters=function(e,t){return!1},t.prototype.afterClosing=function(){},t.prototype.getState=function(){return new o.SimpleCommandState(this.isEnabled())},t.activeInstance=null,t}(i.CommandBase);t.DialogBase=s},9544:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ConstraintViolationOption=t.ConfirmationType=void 0,function(e){e[e.TaskDelete=0]="TaskDelete",e[e.DependencyDelete=1]="DependencyDelete",e[e.ResourcesDelete=2]="ResourcesDelete"}(t.ConfirmationType||(t.ConfirmationType={})),function(e){e[e.DoNothing=0]="DoNothing",e[e.RemoveDependency=1]="RemoveDependency",e[e.KeepDependency=2]="KeepDependency"}(t.ConstraintViolationOption||(t.ConstraintViolationOption={}))},5139:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ConfirmationDialogParameters=void 0;var r=n(655),i=function(e){function t(t,n){var r=e.call(this)||this;return r.type=t,r.callback=n,r}return r.__extends(t,e),t.prototype.clone=function(){var e=new t(this.type,this.callback);return e.message=this.message,e},t}(n(9705).DialogParametersBase);t.ConfirmationDialogParameters=i},3433:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ConstraintViolationDialogParameters=void 0;var r=n(655),i=function(e){function t(t,n){var r=e.call(this)||this;return r.validationErrors=t,r.callback=n,r}return r.__extends(t,e),t.prototype.clone=function(){var e=new t(this.validationErrors,this.callback);return e.option=this.option,e},Object.defineProperty(t.prototype,"hasCriticalErrors",{get:function(){var e;return null===(e=this.validationErrors)||void 0===e?void 0:e.some((function(e){return e.critical}))},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"errorsCount",{get:function(){var e;return null===(e=this.validationErrors)||void 0===e?void 0:e.length},enumerable:!1,configurable:!0}),t}(n(9705).DialogParametersBase);t.ConstraintViolationDialogParameters=i},9705:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DialogParametersBase=void 0;var n=function(){};t.DialogParametersBase=n},6711:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ResourcesDialogParameters=void 0;var r=n(655),i=n(8828),o=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r.__extends(t,e),t.prototype.clone=function(){var e=new t;return e.resources=new i.ResourceCollection,e.resources.addRange(this.resources.items),e},t}(n(9705).DialogParametersBase);t.ResourcesDialogParameters=o},1563:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TaskEditParameters=void 0;var r=n(655),i=n(8828),o=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.enableEdit=!0,t.enableRangeEdit=!0,t.isValidationRequired=!1,t.hiddenFields=[],t.readOnlyFields=[],t}return r.__extends(t,e),t.prototype.clone=function(){var e=new t;return e.id=this.id,e.title=this.title,e.progress=this.progress,e.start=this.start,e.end=this.end,e.assigned=new i.ResourceCollection,e.assigned.addRange(this.assigned.items),e.resources=new i.ResourceCollection,e.resources.addRange(this.resources.items),e.showResourcesDialogCommand=this.showResourcesDialogCommand,e.showTaskEditDialogCommand=this.showTaskEditDialogCommand,e.enableEdit=this.enableEdit,e.enableRangeEdit=this.enableRangeEdit,e.hiddenFields=this.hiddenFields.slice(),e.readOnlyFields=this.readOnlyFields.slice(),e.isValidationRequired=this.isValidationRequired,e.getCorrectDateRange=this.getCorrectDateRange,e},t}(n(9705).DialogParametersBase);t.TaskEditParameters=o},5477:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ResourcesDialogCommand=void 0;var r=n(655),i=n(8828),o=n(4730),s=n(9544),a=n(5139),l=n(6711),c=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.resourcesForDelete=[],t}return r.__extends(t,e),t.prototype.onBeforeDialogShow=function(e){return this.modelManipulator.dispatcher.raiseResourceManagerDialogShowing(e,(function(t){e.resources=t.values.resources}))},t.prototype.applyParameters=function(e,t){this.history.beginTransaction();for(var n=0;n<e.resources.length;n++){t.resources.getItemById(e.resources.getItem(n).internalId)||this.control.commandManager.createResourceCommand.execute(e.resources.getItem(n).text)}for(n=0;n<t.resources.length;n++){e.resources.getItemById(t.resources.getItem(n).internalId)||this.resourcesForDelete.push(t.resources.getItem(n))}return this.history.endTransaction(),!1},t.prototype.createParameters=function(e){this.callBack=e;var t=new l.ResourcesDialogParameters;return t.resources=new i.ResourceCollection,t.resources.addRange(this.control.viewModel.resources.items),t},t.prototype.afterClosing=function(){var e=this;if(this.resourcesForDelete.length){var t=this.control.commandManager.showConfirmationDialog,n=new a.ConfirmationDialogParameters(s.ConfirmationType.ResourcesDelete,(function(){e.history.beginTransaction();for(var t=0;t<e.resourcesForDelete.length;t++)e.control.commandManager.removeResourceCommand.execute(e.resourcesForDelete[t].internalId);e.history.endTransaction()}));n.message=this.resourcesForDelete.reduce((function(e,t){return r.__spreadArray(r.__spreadArray([],e,!0),[t.text],!1)}),[]).join(", "),this.callBack&&(t.afterClosing=function(){delete o.DialogBase.activeInstance,e.callBack()}),t.execute(n)}else this.callBack&&this.callBack()},t.prototype.getDialogName=function(){return"Resources"},t}(o.DialogBase);t.ResourcesDialogCommand=c},6152:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TaskEditDialogCommand=void 0;var r=n(655),i=n(8828),o=n(1389),s=n(3683),a=n(1493),l=n(4730),c=n(1563),u=n(2491),d=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r.__extends(t,e),t.prototype.onBeforeDialogShow=function(e){return this.modelManipulator.dispatcher.raiseTaskTaskEditDialogShowing(e,(function(t){var n=t.values;e.start=n.start,e.end=n.end,e.progress=n.progress,e.title=n.title,e.readOnlyFields=t.readOnlyFields,e.hiddenFields=t.hiddenFields}))},t.prototype.applyParameters=function(e,t){var n=this;this.history.beginTransaction();var r=this.getUpdatedTaskData(e,t);Object.keys(r).length>0&&setTimeout((function(){return n.control.commandManager.updateTaskCommand.execute(t.id,r)}),0);for(var i=0;i<e.assigned.length;i++){if(!t.assigned.getItemById(e.assigned.getItem(i).internalId)){var l=e.assigned.getItem(i).internalId,c=t.id,u=new o.ResourceAssigningArguments(l,c);this.modelManipulator.dispatcher.notifyResourceAssigning(u),u.cancel||this.history.addAndRedo(new s.AssignResourceHistoryItem(this.modelManipulator,u.resourceId,u.taskId))}}var d=function(n){var r=t.assigned.getItem(n);if(!e.assigned.getItemById(r.internalId)){var i=p.control.viewModel.assignments.items.filter((function(e){return e.resourceId===r.internalId&&e.taskId===t.id}))[0];p.modelManipulator.dispatcher.fireResourceUnassigning(i)&&p.history.addAndRedo(new a.DeassignResourceHistoryItem(p.modelManipulator,i.internalId))}},p=this;for(i=0;i<t.assigned.length;i++)d(i);return this.history.endTransaction(),!1},t.prototype.getUpdatedTaskData=function(e,t){var n={};return(0,u.isDefined)(e.title)&&t.title!==e.title&&(n.title=e.title),(0,u.isDefined)(e.progress)&&t.progress!==e.progress&&(n.progress=e.progress),(0,u.isDefined)(e.start)&&t.start!==e.start&&(n.start=e.start),(0,u.isDefined)(e.end)&&t.end!==e.end&&(n.end=e.end),n},t.prototype.createParameters=function(e){var t=this;e=e||this.control.viewModel.tasks.getItemById(this.control.currentSelectedTaskID);var n=new c.TaskEditParameters;return n.id=e.internalId,n.title=e.title,n.progress=e.progress,n.start=e.start,n.end=e.end,n.assigned=this.control.viewModel.getAssignedResources(e),n.resources=new i.ResourceCollection,n.resources.addRange(this.control.viewModel.resources.items),n.showResourcesDialogCommand=this.control.commandManager.showResourcesDialog,n.showTaskEditDialogCommand=this.control.commandManager.showTaskEditDialog,n.enableEdit=this.isTaskEditEnabled(),n.enableRangeEdit=this.isTaskRangeEditEnabled(e),n.isValidationRequired=this.control.isValidateDependenciesRequired(),n.getCorrectDateRange=function(e,n,r){return t.control.validationController.getCorrectDateRange(e,n,r)},n},t.prototype.isTaskEditEnabled=function(){var e=this.control.settings;return e.editing.enabled&&e.editing.allowTaskUpdate},t.prototype.isTaskRangeEditEnabled=function(e){return!this.control.viewModel.isTaskToCalculateByChildren(e.internalId)},t.prototype.isEnabled=function(){var e=this.control,t=e.viewModel.findItem(e.currentSelectedTaskID);return!!t&&t.selected||this.isApiCall},t.prototype.getState=function(){var t=e.prototype.getState.call(this);return t.visible=t.visible&&!this.control.taskEditController.dependencyId,t},t.prototype.getDialogName=function(){return"TaskEdit"},t}(l.DialogBase);t.TaskEditDialogCommand=d},6893:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.GanttExportCalculator=void 0;var r=n(8900),i=n(6907),o=n(2449),s=n(1855),a=n(4991),l=n(2485),c=n(3917),u=n(405),d=n(6995),p=n(5063),h=n(6032),f=n(8603),g=n(6997),y=n(8935),m=n(6353),v=n(3424),T=n(5763),b=n(7802),k=n(9895),S=n(2642),_=function(){function e(t,n){var r,i;this._owner=t,this._props=new g.GanttPdfExportProps(n),null!==(r=(i=this._props).margins)&&void 0!==r||(i.margins=new p.Margin(e._defaultPageMargin))}return Object.defineProperty(e.prototype,"chartTableScaleTopMatrix",{get:function(){var e;return null!==(e=this._chartTableScaleTopMatrix)&&void 0!==e||(this._chartTableScaleTopMatrix=this.calculateChartScaleMatrix(0)),this._chartTableScaleTopMatrix},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"chartTableScaleBottomMatrix",{get:function(){var e;return null!==(e=this._chartTableScaleBottomMatrix)&&void 0!==e||(this._chartTableScaleBottomMatrix=this.calculateChartScaleMatrix(1)),this._chartTableScaleBottomMatrix},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"chartTableBodyMatrix",{get:function(){return this._chartTableBodyMatrix||this.calculateChartTableBodyMatrix(),this._chartTableBodyMatrix},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"treeListHeaderMatrix",{get:function(){return this._treeListHeaderMatrix||this.calculateTreeListTableHeaderMatrix(),this._treeListHeaderMatrix},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"treeListBodyMatrix",{get:function(){return this._treeListBodyMatrix||this.calculateTreeListTableBodyMatrix(),this._treeListBodyMatrix},enumerable:!1,configurable:!0}),e.prototype.getPages=function(e){return new v.PdfGanttPaginator(e,this.settings,this.createGlobalInfo()).getPages()},Object.defineProperty(e.prototype,"settings",{get:function(){return this.settingsForPaging},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"layoutCalculator",{get:function(){return this._taskAreaHelper.layoutCalculator},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"taskAreaHelper",{get:function(){var e;return null!==(e=this._taskAreaHelper)&&void 0!==e||(this._taskAreaHelper=new f.TaskAreaExportHelper(this._owner,this._props)),this._taskAreaHelper},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"scalingHelper",{get:function(){var e,t;return null!==(e=this._scalingHelper)&&void 0!==e||(this._scalingHelper=new T.ScalingHelper(null===(t=this._props)||void 0===t?void 0:t.pdfDoc)),this._scalingHelper},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"visibleTaskIndices",{get:function(){return this.taskAreaHelper.visibleTaskIndices},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"baseCellWidth",{get:function(){return this.taskAreaHelper.baseCellSize.width},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"baseCellHeight",{get:function(){return this.taskAreaHelper.baseCellHeight},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"chartScaleTableStyle",{get:function(){var e;return null!==(e=this._chartScaleTableStyle)&&void 0!==e||(this._chartScaleTableStyle=this.getChartScaleTableStyle()),this._chartScaleTableStyle},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"chartMainTableStyle",{get:function(){var e;return null!==(e=this._chartMainTableStyle)&&void 0!==e||(this._chartMainTableStyle=this.getChartMainTableStyle()),this._chartMainTableStyle},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"treeListTableStyle",{get:function(){return this._treeListTableStyle||this.calculateTreeListTableStyle(),this._treeListTableStyle},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"pageTopMargin",{get:function(){return this._props.margins.top},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"pageLeftMargin",{get:function(){return this._props.margins.left},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"pageRightMargin",{get:function(){return this._props.margins.right},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"pageBottomMargin",{get:function(){return this._props.margins.bottom},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"headerTableTop",{get:function(){var e;return null!==(e=this._headerTableTop)&&void 0!==e||(this._headerTableTop=this.pageTopMargin),this._headerTableTop},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"mainTableTop",{get:function(){var e;return null!==(e=this._mainTableTop)&&void 0!==e||(this._mainTableTop=this.getMainTableTop()),this._mainTableTop},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"exportDataMode",{get:function(){return this._props.exportDataMode},enumerable:!1,configurable:!0}),e.prototype.getMainTableTop=function(){return this.headerTableTop+this.headerTableHeight-this.taskAreaHelper.offsetTop},Object.defineProperty(e.prototype,"chartLeft",{get:function(){var e;return null!==(e=this._chartLeft)&&void 0!==e||(this._chartLeft=this.getChartLeft()),this._chartLeft},enumerable:!1,configurable:!0}),e.prototype.getChartLeft=function(){var e;return(((null===(e=this._props)||void 0===e?void 0:e.exportMode)||k.ExportMode.all)===k.ExportMode.chart?this.pageLeftMargin:this.treeListLeft+this.treeListWidth)-this.taskAreaHelper.offsetLeft},Object.defineProperty(e.prototype,"treeListLeft",{get:function(){var e;return null!==(e=this._treeListLeft)&&void 0!==e||(this._treeListLeft=this.pageLeftMargin),this._treeListLeft},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"headerTableHeight",{get:function(){return 2*this.taskAreaHelper.headerRowHeight},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"mainTableHeight",{get:function(){var e;return null!==(e=this._mainTableHeight)&&void 0!==e||(this._mainTableHeight=this.taskAreaHelper.taskAreaHeight),this._mainTableHeight},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"treeListWidth",{get:function(){var e;return null!==(e=this._treeListWidth)&&void 0!==e||(this._treeListWidth=this.getTreeListTableWidth()),this._treeListWidth},enumerable:!1,configurable:!0}),e.prototype.getTreeListTableWidth=function(){var e,t=this,n=this.treeListHeaderMatrix[0].map((function(e,n){return t.getTreeListColumnWidth(n)}));return null!==(e=null==n?void 0:n.reduce((function(e,t){return e+t}),0))&&void 0!==e?e:0},Object.defineProperty(e.prototype,"chartWidth",{get:function(){var e=this;if(!this._chartWidth){var t=this.chartTableScaleBottomMatrix[0];this._chartWidth=t.reduce((function(t,n){return t+(n.styles.cellWidth.hasValue()?n.styles.cellWidth.getValue():e.baseCellWidth)}),0)}return this._chartWidth},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"settingsForPaging",{get:function(){return this._settingsForPaging||(this._settingsForPaging=new g.GanttPdfExportProps(this._props),this.prepareAutoFormat(this._settingsForPaging),this.scalingHelper.scalePageMargins(this._settingsForPaging)),this._settingsForPaging},enumerable:!1,configurable:!0}),e.prototype.prepareAutoFormat=function(e){if(e.format===g.GanttPdfExportProps.autoFormatKey){e.format=null;var t=e.landscape,n=this.autoFormatWidth,r=this.autoFormatHeight;(t&&r>n||!t&&r<n)&&(e.landscape=!t),e.pageSize=new m.Size(n,r)}},Object.defineProperty(e.prototype,"autoFormatWidth",{get:function(){var t,n=((null===(t=this._props)||void 0===t?void 0:t.exportMode)||k.ExportMode.all)!==k.ExportMode.treeList,r=this.pageRightMargin;return(r+=n?this.chartLeft+this.chartWidth:this.treeListLeft+this.treeListWidth)+e._autoFormatWidthAddStock},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"autoFormatHeight",{get:function(){return this.mainTableTop+this.mainTableHeight+this.pageBottomMargin},enumerable:!1,configurable:!0}),e.prototype.createGlobalInfo=function(){var e={objects:this._canExportChart()?this.getGanttObjectsInfo():null,tables:this.getTablesInfo()};return this.scalingHelper.scaleSizes(e),e},e.prototype.getTablesInfo=function(){var e={};return this._canExportTreelist()&&(e[y.PdfPageTableNames.treeListHeader]=this.createTreeListHeaderTableInfo(),e[y.PdfPageTableNames.treeListMain]=this.createTreeListMainTableInfo()),this._canExportChart()&&(e[y.PdfPageTableNames.chartMain]=this.createChartMainTableInfo(),e[y.PdfPageTableNames.chartScaleTop]=this._createChartScaleTopInfo(),e[y.PdfPageTableNames.chartScaleBottom]=this._createChartScaleBottomInfo()),e},Object.defineProperty(e.prototype,"exportMode",{get:function(){var e,t;return null!==(t=null===(e=this._props)||void 0===e?void 0:e.exportMode)&&void 0!==t?t:k.ExportMode.all},enumerable:!1,configurable:!0}),e.prototype._canExportTreelist=function(){return this.exportMode===k.ExportMode.all||this.exportMode===k.ExportMode.treeList},e.prototype._canExportChart=function(){return this.exportMode===k.ExportMode.all||this.exportMode===k.ExportMode.chart},Object.defineProperty(e.prototype,"_predefinedFont",{get:function(){var e,t,n,r,i=null===(t=null===(e=this._props)||void 0===e?void 0:e.pdfDoc)||void 0===t?void 0:t.getFont();return(null==i?void 0:i.fontName)||(null===(r=null===(n=this._props)||void 0===n?void 0:n.font)||void 0===r?void 0:r.name)},enumerable:!1,configurable:!0}),e.prototype._createChartScaleTopInfo=function(){return{name:y.PdfPageTableNames.chartScaleTop,size:new m.Size(this.chartWidth,this.taskAreaHelper.headerRowHeight),position:new r.Point(this.chartLeft,this.headerTableTop),style:this.chartScaleTableStyle,baseCellSize:new m.Size(this.baseCellWidth,this.taskAreaHelper.headerRowHeight),cells:this.chartTableScaleTopMatrix}},e.prototype._createChartScaleBottomInfo=function(){var e=this.taskAreaHelper.headerRowHeight;return{name:y.PdfPageTableNames.chartScaleBottom,size:new m.Size(this.chartWidth,e),position:new r.Point(this.chartLeft,this.headerTableTop+e),style:this.chartScaleTableStyle,baseCellSize:new m.Size(this.baseCellWidth,e),cells:this.chartTableScaleBottomMatrix}},e.prototype.createChartMainTableInfo=function(){return{name:y.PdfPageTableNames.chartMain,size:new m.Size(this.chartWidth,this.mainTableHeight),position:new r.Point(this.chartLeft,this.mainTableTop),style:this.chartMainTableStyle,baseCellSize:new m.Size(this.baseCellWidth,this.baseCellHeight),cells:this.chartTableBodyMatrix,hideRowLines:!this._owner.settings.areHorizontalBordersEnabled}},e.prototype.createTreeListHeaderTableInfo=function(){return{name:y.PdfPageTableNames.treeListHeader,size:new m.Size(this.treeListWidth,this.headerTableHeight),position:new r.Point(this.treeListLeft,this.headerTableTop),style:this.treeListTableStyle,baseCellSize:new m.Size(null,this.headerTableHeight),cells:this.treeListHeaderMatrix}},e.prototype.createTreeListMainTableInfo=function(){return{name:y.PdfPageTableNames.treeListMain,size:new m.Size(this.treeListWidth,this.mainTableHeight),position:new r.Point(this.treeListLeft,this.mainTableTop),style:this.treeListTableStyle,baseCellSize:new m.Size(null,this.baseCellHeight),cells:this.treeListBodyMatrix,hideRowLines:!this._owner.settings.areHorizontalBordersEnabled}},e.prototype.calculateChartScaleMatrix=function(e){for(var t=this.taskAreaHelper,n=t.scaleRanges,r=new Array,i=n[e][0],o=n[e][1],s=i;s<=o;s++){var a=this.layoutCalculator.getScaleItemStart(s,t.scales[e]),l=new h.CellDef(this._owner.renderHelper.getScaleItemTextByStart(a,t.scales[e]));l.styles.cellPadding.assign(0),l.styles.minCellHeight=this.taskAreaHelper.headerRowHeight;var c=0===e?t.scaleTopWidths[s]:t.scaleBottomWidths[s];l.styles.cellWidth.assign(c),r.push(l)}return[r]},e.prototype.calculateChartTableBodyMatrix=function(){var e=this;this._chartTableBodyMatrix=new Array,this.visibleTaskIndices.length>0?this.visibleTaskIndices.forEach((function(t){return e._chartTableBodyMatrix.push(e.createChartTableBodyRow(t))})):this._chartTableBodyMatrix.push(this.createChartTableBodyRow(-1))},e.prototype.createChartTableBodyRow=function(e){var t=new h.CellDef;return this.rowHasChildren(e)&&t.styles.fillColor.assign(this.taskAreaHelper.parentRowBackColor),this.chartTableScaleBottomMatrix[0].map((function(e){var n=new h.CellDef(t);return n.styles.cellWidth.assign(e.styles.cellWidth),n}))},e.prototype.rowHasSelection=function(e){return this._owner.rowHasSelection(e)},e.prototype.rowHasChildren=function(e){return this._owner.rowHasChildren(e)},e.prototype.calculateTreeListTableHeaderMatrix=function(){this._treeListHeaderMatrix=new Array;for(var e=this._owner,t=e.getTreeListColCount(),n=new Array,r=0;r<t;r++){var i=new h.CellDef(e.getTreeListHeaderInfo(r));i.styles.minCellHeight=2*this.taskAreaHelper.headerRowHeight,n.push(i)}this._treeListHeaderMatrix.push(n)},e.prototype.calculateTreeListTableBodyMatrix=function(){this._treeListBodyMatrix=new Array,this.visibleTaskIndices.length>0?this.fillTreeListTableBodyMatrix(this._treeListBodyMatrix):this.fillTreeListEmptyTableBodyMatrix(this._treeListBodyMatrix)},e.prototype.fillTreeListTableBodyMatrix=function(e){for(var t,n=this.visibleTaskIndices,r=this.treeListHeaderMatrix[0].length,i=0;i<n.length;i++){for(var o=new Array,s=n[i],a=null===(t=this._owner.getTask(s))||void 0===t?void 0:t.id,l=0;l<r;l++){var c=new h.CellDef(this._owner.getTreeListCellInfo(s,l,a));c.styles.cellWidth.hasValue()||c.styles.cellWidth.assign(this.getTreeListColumnWidth(l)),this.rowHasChildren(n[i])&&c.styles.fillColor.assign(this.taskAreaHelper.parentRowBackColor),o.push(c)}e.push(o)}},e.prototype.fillTreeListEmptyTableBodyMatrix=function(e){var t=new Array,n=new h.CellDef(this._owner.getTreeListEmptyDataCellInfo());n.styles.cellWidth.assign(this.treeListWidth),n.styles.halign=S.PredefinedStyles.horizontalAlign[1],n.styles.valign=S.PredefinedStyles.verticalAlign[1],t.push(n),e.push(t)},e.prototype.getTreeListColumnWidth=function(e){var t=this.treeListHeaderMatrix[0][e],n=t&&t.styles;return n.cellWidth.getValue()||n.minCellWidth||0},e.prototype.getObjectsLeftOffset=function(e){void 0===e&&(e=!1);var t=this.dataObjectLeftDelta;return e||(t+=this.taskAreaHelper.customRangeLeftOffset),t},Object.defineProperty(e.prototype,"dataObjectLeftDelta",{get:function(){var e;return null!==(e=this._dataObjectLeftDelta)&&void 0!==e||(this._dataObjectLeftDelta=this.getDataObjectLeftDelta()),this._dataObjectLeftDelta},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"dataObjectTopDelta",{get:function(){var e;return null!==(e=this._dataObjectTopDelta)&&void 0!==e||(this._dataObjectTopDelta=this.getDataObjectTopDelta()),this._dataObjectTopDelta},enumerable:!1,configurable:!0}),e.prototype.getChartScaleTableStyle=function(){var e=new d.StyleDef(this.taskAreaHelper.scaleTableStyle);return this._predefinedFont&&(e.font=this._predefinedFont),e},e.prototype.getChartMainTableStyle=function(){var e=new d.StyleDef(this.taskAreaHelper.chartMainTableStyle);return this._predefinedFont&&(e.font=this._predefinedFont),e},e.prototype.calculateTreeListTableStyle=function(){this._treeListTableStyle=new d.StyleDef(this._owner.getTreeListTableStyle()),this._treeListTableStyle.fillColor.assign(this.chartMainTableStyle.fillColor),this._treeListTableStyle.lineColor.assign(this.chartMainTableStyle.lineColor),this._predefinedFont&&(this._treeListTableStyle.font=this._predefinedFont)},e.prototype.getGanttObjectsInfo=function(){return{tasks:this.tasksInfo,dependencies:this.dependenciesInfo,resources:this.resourcesInfo,timeMarkers:this.timeMarkersInfo}},Object.defineProperty(e.prototype,"tasksInfo",{get:function(){var e;return null!==(e=this._tasksInfo)&&void 0!==e||(this._tasksInfo=this.calculateTasksInfo()),this._tasksInfo},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"dependenciesInfo",{get:function(){var e;return null!==(e=this._dependenciesInfo)&&void 0!==e||(this._dependenciesInfo=this.calculateDependenciesInfo()),this._dependenciesInfo},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"resourcesInfo",{get:function(){var e;return null!==(e=this._resourcesInfo)&&void 0!==e||(this._resourcesInfo=this.calculateResourcesInfo()),this._resourcesInfo},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"timeMarkersInfo",{get:function(){var e;return null!==(e=this._timeMarkersInfo)&&void 0!==e||(this._timeMarkersInfo=this.calculateTimeMarkersInfoInfo()),this._timeMarkersInfo},enumerable:!1,configurable:!0}),e.prototype.getDataObjectLeftDelta=function(){return this.chartLeft-this.taskAreaHelper.objectsLeftDelta},e.prototype.getDataObjectTopDelta=function(){return this.headerTableTop+this.headerTableHeight-this.taskAreaHelper.objectsTopDelta},e.prototype.calculateTasksInfo=function(){var e=this,t=new Array;return this.visibleTaskIndices.forEach((function(n){return t.push(e.calculateTaskInfo(n))})),t},e.prototype.calculateTaskInfo=function(e){var t=new c.PdfTaskInfo,n=this.layoutCalculator.getTaskElementInfo(e);return t.taskColor=this.getTaskColor(e),t.sidePoints=this.getTaskSidePoints(e),t.isMilestone=n.className.indexOf(s.GridLayoutCalculator.milestoneClassName)>0,t.isMilestone||(t.isSmallTask=n.className.indexOf(s.GridLayoutCalculator.smallTaskClassName)>0,t.isParent=n.className.indexOf(s.GridLayoutCalculator.parentTaskClassName)>0,this.appendTaskTitle(t,e),this.appendTaskProgress(t,e)),t},e.prototype.appendTaskTitle=function(e,t){var n=this._owner.settings.taskTitlePosition;e.isSmallTask&&n!==o.TaskTitlePosition.Outside||n===o.TaskTitlePosition.None||(e.text=this._owner.getTaskText(t),e.textPosition=n,e.textStyle=this.getTaskTextStyle(t))},e.prototype.appendTaskProgress=function(e,t){var n=this.layoutCalculator.getTaskProgressElementInfo(t);e.progressWidth=n.size.width,e.progressColor=this.getTaskProgressColor(t),e.progressColor.applyOpacityToBackground(e.taskColor)},e.prototype.getTaskSidePoints=function(e){var t=this,n=this.layoutCalculator.getTaskSidePoints(e);return n.forEach((function(e){e.x+=t.getObjectsLeftOffset(!0),e.y+=t.dataObjectTopDelta})),n},e.prototype.getTaskColor=function(e){var t=this.taskAreaHelper.getTaskElementBackColor(e,s.GridLayoutCalculator.taskClassName);return new u.Color(t)},e.prototype.getTaskProgressColor=function(e){return new u.Color(this.taskAreaHelper.getTaskElementBackColor(e,s.GridLayoutCalculator.taskProgressClassName))},e.prototype.getTaskTextStyle=function(e){var t=new d.StyleDef;return t.cellPadding.assign(0),t.assign(this.taskAreaHelper.getTaskElementStyle(e,s.GridLayoutCalculator.taskTitleClassName)),t},e.prototype.calculateDependenciesInfo=function(){var e=this,t=new Array,n=this.taskAreaHelper,r=new u.Color(n.dependencyColor);return n.connectorLines.forEach((function(i){return t.push(e.createLineInfo(i,r,n.arrowWidth))})),t},e.prototype.createLineInfo=function(e,t,n){var r=new a.PdfDependencyLineInfo;if(r.fillColor=t,e.className.indexOf(s.GridLayoutCalculator.arrowClassName)>-1){var i=this.layoutCalculator.getArrowPositionByClassName(e.className);r.arrowInfo={position:i,width:n},r.points=[this.getArrowTopCorner(e,i,n)]}else r.points=this.getLinePoints(e);return r},e.prototype.getArrowTopCorner=function(e,t,n){var i=e.position.x+this.getObjectsLeftOffset(),s=e.position.y+this.dataObjectTopDelta;switch(t){case o.Position.Left:i+=n;break;case o.Position.Top:s+=n}return new r.Point(i,s)},e.prototype.getLinePoints=function(e){var t=e.position.x+this.getObjectsLeftOffset(),n=e.position.y+this.dataObjectTopDelta,i=t+e.size.width,o=n+e.size.height;return[new r.Point(t,n),new r.Point(i,o)]},e.prototype.calculateResourcesInfo=function(){var e=this,t=new Array;return this.taskAreaHelper.resourcesElements.forEach((function(n){return t=t.concat(e.calculateResourcesInLine(n))})),t},e.prototype.calculateResourcesInLine=function(e){var t=new Array;if(e)for(var n=i.DomUtils.pxToInt(e.style.left)+this.getObjectsLeftOffset(),r=i.DomUtils.pxToInt(e.style.top)+this.dataObjectTopDelta,o=e.getElementsByClassName(s.GridLayoutCalculator.taskResourceClassName),a=0;a<o.length;a++){var c=o[a];if(this.taskAreaHelper.isElementVisible(c)){var u=getComputedStyle(c);n+=this.getMargin(u).left,t.push(new l.PdfTaskResourcesInfo(c.textContent,new d.StyleDef(u),n,r)),n+=i.DomUtils.pxToInt(u.width)}}return t},e.prototype.calculateTimeMarkersInfoInfo=function(){var e=this,t=new Array;return this.taskAreaHelper.stripLinesElements.forEach((function(n){return t.push(e.createTimeMarkerInfo(n,!0))})),this.taskAreaHelper.noWorkingIntervalsElements.forEach((function(n){return t.push(e.createTimeMarkerInfo(n,!1))})),t},e.prototype.createTimeMarkerInfo=function(e,t){var n=getComputedStyle(e),o=i.DomUtils.pxToInt(n.left)+this.getObjectsLeftOffset(),s=i.DomUtils.pxToInt(n.top)+this.dataObjectTopDelta,a=i.DomUtils.pxToInt(n.width),l=i.DomUtils.pxToInt(n.height);return new b.PdfTimeMarkerInfo(new r.Point(o,s),new m.Size(a,l),new u.Color(n.backgroundColor),new u.Color(n.borderLeftColor),t)},e.prototype.getMargin=function(e){var t=new p.Margin(0);if(e){var n=e.margin;n||(n+=e.marginTop||"0",n+=" "+e.marginRight||0,n+=" "+e.marginBottom||0,n+=" "+e.marginLeft||0),t.assign(n)}return t},e._defaultPageMargin=10,e._autoFormatWidthAddStock=1,e}();t.GanttExportCalculator=_},4991:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PdfDependencyLineInfo=void 0;var r=n(8900),i=n(405),o=function(){function e(){}return e.prototype.assign=function(e){var t;this._copyPoints(e.points),this.arrowInfo=e.arrowInfo,null!==(t=this.fillColor)&&void 0!==t||(this.fillColor=new i.Color),this.fillColor.assign(e.fillColor)},e.prototype._copyPoints=function(e){var t=this;this.points=new Array,null==e||e.forEach((function(e){return t.points.push(new r.Point(e.x,e.y))}))},e}();t.PdfDependencyLineInfo=o},266:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PdfObjectDrawer=void 0;var r=n(2449),i=n(817),o=n(3917),s=function(){function e(e,t){this._FONT_ROW_RATIO=1.15,this._info=t,this._pdfDoc=e}return e.prototype.draw=function(){this.drawTimeMarkers(),this.drawDependencies(),this.drawTasks(),this.drawResources()},e.prototype.drawTasks=function(){var e,t=this,n=null===(e=this._info)||void 0===e?void 0:e.tasks;n&&n.forEach((function(e){return t.drawTask(e)}))},e.prototype.drawTask=function(e){var t=this._pdfDoc;t.setFillColor.apply(t,e.taskColor.getRBGColor()),t.setDrawColor.apply(t,e.taskColor.getRBGColor()),e.isMilestone?this.drawMilestone(e):this.drawRegularTask(e)},e.prototype.drawMilestone=function(e){var t=this._pdfDoc,n=e.sidePoints[0].x,r=e.sidePoints[0].y,i=e.sidePoints[1].x,o=e.sidePoints[1].y,s=e.sidePoints[2].x,a=e.sidePoints[2].y,l=e.sidePoints[3].x,c=e.sidePoints[3].y;t.triangle(n,r,i,o,s,a,"FD"),t.triangle(n,r,l,c,s,a,"FD")},e.prototype.drawRegularTask=function(e){var t=this._pdfDoc;t.rect(e.left,e.top,e.width,e.height,"FD"),e.isParent&&this.drawParentBorder(e),e.progressWidth&&(t.setFillColor.apply(t,e.progressColor.getRBGColor()),t.rect(e.left,e.top,e.progressWidth,e.height,"F")),e.text&&this.printTaskTitle(e)},e.prototype.drawParentBorder=function(e){var t=this._pdfDoc,n=e.sidePoints[0].x,r=e.sidePoints[1].y,i=e.sidePoints[3].y,o=e.sidePoints[2].x,s=e.sidePoints[3].y-e.sidePoints[1].y,a=e.progressWidth>s?e.progressColor.getRBGColor():e.taskColor.getRBGColor();t.setFillColor.apply(t,a),t.triangle(n,r,n,i,n+s,r,"FD"),t.setFillColor.apply(t,e.taskColor.getRBGColor()),t.triangle(o,r,o,i,o-s,r,"FD")},e.prototype.printTaskTitle=function(e){var t,n=this._pdfDoc,s=e.textStyle,a=s&&s.textColor.getRBGColor(),l=s&&s.fontSize;n.setTextColor.apply(n,a),n.setFontSize(l);var c=e.top+l*this._FONT_ROW_RATIO/n.internal.scaleFactor;e.isParent&&(c-=o.PdfTaskInfo.defaultParentHeightCorrection);var u=s&&s.cellPadding.left||0,d=s&&s.cellPadding.right||0;if(e.textPosition===r.TaskTitlePosition.Inside){var p=e.width-u-d;t=e.left+u,n.text(i.EllipsisHelper.limitPdfTextWithEllipsis(e.text,n,p),t,c)}else t=e.left-d,n.text(e.text,t,c,{align:"right"})},e.prototype.drawDependencies=function(){var e,t=this,n=null===(e=this._info)||void 0===e?void 0:e.dependencies;n&&n.forEach((function(e){return t.drawDependencyLine(e)}))},e.prototype.drawDependencyLine=function(e){var t,n;if((t=this._pdfDoc).setFillColor.apply(t,e.fillColor.getRBGColor()),(n=this._pdfDoc).setDrawColor.apply(n,e.fillColor.getRBGColor()),e.arrowInfo)this.drawArrow(e);else{var r=e.points;this._pdfDoc.line(r[0].x,r[0].y,r[1].x,r[1].y)}},e.prototype.isValidLine=function(e){var t=e.points;return!(isNaN(t[0].x)||isNaN(t[0].y)||isNaN(t[1].x)||isNaN(t[1].y))},e.prototype.drawArrow=function(e){var t=e.arrowInfo.width||0,n=e.points[0].x,i=e.points[0].y;switch(e.arrowInfo.position){case r.Position.Left:this._pdfDoc.triangle(n,i+t,n+t,i,n+t,i+2*t,"FD");break;case r.Position.Right:this._pdfDoc.triangle(n,i,n,i+2*t,n+t,i+t,"FD");break;case r.Position.Top:this._pdfDoc.triangle(n,i+t,n+t,i,n+2*t,i+t,"FD");break;case r.Position.Bottom:this._pdfDoc.triangle(n,i,n+t,i+t,n+2*t,i,"FD")}},e.prototype.drawResources=function(){var e,t=this,n=this._pdfDoc,r=null===(e=this._info)||void 0===e?void 0:e.resources;r&&r.forEach((function(e){var r,i,o;n.setFontSize(null!==(r=e.style.fontSize)&&void 0!==r?r:11);var s=e.y+e.style.fontSize*t._FONT_ROW_RATIO/n.internal.scaleFactor,a=null!==(i=e.style.cellPadding.left)&&void 0!==i?i:0,l=null!==(o=e.style.cellPadding.right)&&void 0!==o?o:1,c=Math.max(e.style.cellWidth.getValue(),a+n.getTextWidth(e.text)+l);n.setFillColor.apply(n,e.style.fillColor.getRBGColor()),n.rect(e.x,e.y,c,e.style.minCellHeight,"F"),n.setTextColor.apply(n,e.style.textColor.getRBGColor()),n.text(e.text,e.x+a,s)}))},e.prototype.drawTimeMarkers=function(){var e,t=this,n=null===(e=this._info)||void 0===e?void 0:e.timeMarkers;null==n||n.forEach((function(e){return t.drawTimeMarker(e)}))},e.prototype.drawTimeMarker=function(e){var t,n,r=this._pdfDoc,i=e.size.width>1,o=e.start.x,s=e.start.y,a=e.size.width,l=e.size.height,c=e.isStripLine;i&&(r.setFillColor.apply(r,e.color.getRBGColor()),r.saveGraphicsState(),r.setGState(new r.GState({opacity:null!==(n=e.color.opacity)&&void 0!==n?n:1})),r.rect(o,s,a,l,"F"),r.restoreGraphicsState()),c&&(this._pdfDoc.setLineDashPattern([3]),(t=this._pdfDoc).setDrawColor.apply(t,e.lineColor.getRBGColor()),i&&this._pdfDoc.line(o+a,s,o+a,s+l,"S"),this._pdfDoc.line(o,s,o,s+l,"S"),this._pdfDoc.setLineDashPattern())},e}();t.PdfObjectDrawer=s},3917:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PdfTaskInfo=void 0;var r=n(8900),i=n(405),o=n(6995),s=function(){function e(){}return Object.defineProperty(e.prototype,"left",{get:function(){var e;return(null===(e=this.sidePoints)||void 0===e?void 0:e.length)>3?this.sidePoints[0].x:0},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"top",{get:function(){var e;return(null===(e=this.sidePoints)||void 0===e?void 0:e.length)>3?this.sidePoints[1].y:0},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"right",{get:function(){var e;return(null===(e=this.sidePoints)||void 0===e?void 0:e.length)>3?this.sidePoints[2].x:0},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"bottom",{get:function(){var e;return(null===(e=this.sidePoints)||void 0===e?void 0:e.length)>3?this.sidePoints[3].y:0},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"width",{get:function(){return this.right-this.left},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"height",{get:function(){var t=this.bottom-this.top;return this.isParent&&(t-=e.defaultParentHeightCorrection),t},enumerable:!1,configurable:!0}),e.prototype.assign=function(e){var t,n,r;this.isMilestone=e.isMilestone,this._copyPoints(e.sidePoints),this.progressWidth=e.progressWidth,this.isSmallTask=e.isSmallTask,this.text=e.text,this.textPosition=e.textPosition,null!==(t=this.progressColor)&&void 0!==t||(this.progressColor=new i.Color),this.progressColor.assign(e.progressColor),null!==(n=this.taskColor)&&void 0!==n||(this.taskColor=new i.Color),this.taskColor.assign(e.taskColor),null!==(r=this.textStyle)&&void 0!==r||(this.textStyle=new o.StyleDef),this.textStyle.assign(e.textStyle),this.isParent=e.isParent},e.prototype._copyPoints=function(e){var t=this;this.sidePoints=new Array,null==e||e.forEach((function(e){return t.sidePoints.push(new r.Point(e.x,e.y))}))},e.defaultParentHeightCorrection=4,e}();t.PdfTaskInfo=s},2485:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PdfTaskResourcesInfo=void 0;var r=n(2491),i=n(6995),o=function(){function e(e,t,n,o){e&&(this.text=e),t&&(this.style=new i.StyleDef(t)),(0,r.isDefined)(n)&&(this.x=n),(0,r.isDefined)(o)&&(this.y=o)}return e.prototype.assign=function(e){this.text=e.text,this.style=new i.StyleDef(e.style),this.x=e.x,this.y=e.y},e}();t.PdfTaskResourcesInfo=o},7802:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PdfTimeMarkerInfo=void 0;var r=n(8900),i=n(6353),o=n(2491),s=n(405),a=function(){function e(e,t,n,a,l){this.lineColor=new s.Color,this.color=new s.Color,e&&(this.start=new r.Point(e.x,e.y)),t&&(this.size=new i.Size(t.width,t.height)),n&&this.color.assign(n),a&&this.lineColor.assign(a),(0,o.isDefined)(l)&&(this.isStripLine=l)}return e.prototype.assign=function(e){var t,n,o,s;e&&(this.start=new r.Point(null===(t=e.start)||void 0===t?void 0:t.x,null===(n=e.start)||void 0===n?void 0:n.y),this.size=new i.Size(null===(o=e.size)||void 0===o?void 0:o.width,null===(s=e.size)||void 0===s?void 0:s.height),this.isStripLine=e.isStripLine,this.color.assign(e.color),this.lineColor.assign(e.lineColor))},e}();t.PdfTimeMarkerInfo=a},2978:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PdfGanttExporter=void 0;var r=n(4970),i=function(){function e(e){if(!e.settings.pdfDoc&&!e.settings.docCreateMethod)throw new Error("Cannot convert gantt to pdf without document instance!");this._info=e}return e.prototype.export=function(){var e,t,n=this.pdfDoc;this.applyCustomFont();for(var i=this._info,o=new r.PdfGanttPageDrawer(n,i.settings),s=i.getPages(n),a=s.length,l=0;l<a;l++){l>0&&n.addPage(this.getDocumentFormat(),this.getOrientation());var c=s[l];o.drawPage(c)}return(null===(e=this.props)||void 0===e?void 0:e.fileName)&&n.save(null===(t=this.props)||void 0===t?void 0:t.fileName),n},Object.defineProperty(e.prototype,"pdfDoc",{get:function(){var e,t;return null!==(e=this._pdfDoc)&&void 0!==e||(this._pdfDoc=null!==(t=this._info.settings.pdfDoc)&&void 0!==t?t:this.createDoc()),this._pdfDoc},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"props",{get:function(){return this._info.settings},enumerable:!1,configurable:!0}),e.prototype.createDoc=function(){var e=this.getJsPDFProps();return this._info.settings.docCreateMethod(e)},e.prototype.getJsPDFProps=function(){var e={putOnlyUsedFonts:!0,unit:"px",hotfixes:["px_scaling"]};return e.orientation=this.getOrientation(),e.format=this.getDocumentFormat(),e},e.prototype.getOrientation=function(){var e;return(null===(e=this.props)||void 0===e?void 0:e.landscape)?"l":"p"},e.prototype.getDocumentFormat=function(){var e,t,n,r;return(null===(e=this.props)||void 0===e?void 0:e.format)||(null===(t=this.props)||void 0===t?void 0:t.pageSize)?(null===(n=this.props)||void 0===n?void 0:n.pageSize)?[this.props.pageSize.height,this.props.pageSize.width]:null===(r=this.props)||void 0===r?void 0:r.format:"a4"},e.prototype.applyCustomFont=function(){this.props.font&&this.props.font.applyToDoc(this.pdfDoc)},e}();t.PdfGanttExporter=i},8935:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PdfPageTableNames=void 0;var n=function(){function e(){}return e.treeListHeader="treeListHeader",e.treeListMain="treeListMain",e.chartMain="chartMain",e.chartScaleTop="chartScaleTop",e.chartScaleBottom="chartScaleBottom",e}();t.PdfPageTableNames=n},4970:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PdfGanttPageDrawer=void 0;var r=n(8900),i=n(266),o=n(8935),s=n(9895),a=n(5510),l=function(){function e(e,t){this._pdfDoc=e,this._props=t}return e.prototype.drawPage=function(e){var t=this._pdfDoc,n=new a.PdfGanttTableDrawer(t);this.needDrawChart()&&(n.drawTable(e.tables[o.PdfPageTableNames.chartMain]),new i.PdfObjectDrawer(t,e.objects).draw(),n.drawTable(e.tables[o.PdfPageTableNames.chartScaleTop]),n.drawTable(e.tables[o.PdfPageTableNames.chartScaleBottom]));return this.needDrawTreeList()&&(n.drawTable(e.tables[o.PdfPageTableNames.treeListMain]),n.drawTable(e.tables[o.PdfPageTableNames.treeListHeader])),this.drawMargins(e),t},e.prototype.needDrawChart=function(){return!this._props||this._props.exportMode===s.ExportMode.all||this._props.exportMode===s.ExportMode.chart},e.prototype.needDrawTreeList=function(){return!this._props||this._props.exportMode===s.ExportMode.all||this._props.exportMode===s.ExportMode.treeList},e.prototype.getContentRightBottom=function(e){var t=new r.Point(0,0);for(var n in e.tables)if(Object.prototype.hasOwnProperty.call(e.tables,n)){var i=e.tables[n];t.x=Math.max(t.x,i.position.x+i.size.width),t.y=Math.max(t.y,i.position.y+i.size.height)}return t},e.prototype.drawMargins=function(e){var t=this._pdfDoc,n=this._props,r=t.getPageWidth(),i=t.getPageHeight(),o=this.getContentRightBottom(e);t.setFillColor(255,255,255),t.rect(0,0,n.margins.left,i,"F"),t.rect(0,0,r,n.margins.top,"F"),t.rect(o.x,0,r,i,"F"),t.rect(0,o.y,r,i,"F")},e}();t.PdfGanttPageDrawer=l},4429:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PageNavigation=void 0;var n=function(){function e(e,t,n,r,i,o){this._correctedBottoms=new Array,this.vIndex=0,this.hIndex=0,this.pageX=0,this.pageY=0,this._top=null==e?void 0:e.top,this._left=null==e?void 0:e.left,this._bottom=null==e?void 0:e.bottom,this._right=null==e?void 0:e.right,this.vIndex=null!=t?t:this.vIndex,this.hIndex=null!=n?n:this.hIndex,this.pageX=null!=r?r:this.pageX,this.pageY=null!=i?i:this.pageY,o&&(this._correctedBottoms=o)}return e.prototype.offset=function(e,t){e&&this.offsetOneD(e),t&&this.offsetOneD(t,!0)},e.prototype.offsetOneD=function(e,t){for(var n=e,r=this.getSpaceToBorder(t);r<n;)t?(this.vIndex++,this.pageY=this._top):(this.hIndex++,this.pageX=this._left),n-=r,r=this.getSpaceToBorder(t);t?this.pageY+=n:this.pageX+=n},Object.defineProperty(e.prototype,"defaultPageHeight",{get:function(){return this.getCurrentPageBottom()-this._top},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"defaultPageWidth",{get:function(){return this._right-this._left},enumerable:!1,configurable:!0}),e.prototype.getPageEnd=function(e){return e?this.getCurrentPageBottom():this._right},e.prototype.getPageStart=function(e){return e?this._top:this._left},e.prototype.getPageSize=function(e,t){return e?this.getPageHeight(t):this.defaultPageWidth},e.prototype.getSpaceToBorder=function(e){return e?this.getCurrentPageBottom()-this.pageY:this._right-this.pageX},e.prototype.getPageGlobalOffset=function(e,t){if(!t)return e*this.defaultPageWidth;for(var n=0,r=1;r<=e;r++)n+=this.getPageHeight(r-1);return n},e.prototype.assign=function(e){this._top=e._top,this._left=e._left,this._bottom=e._bottom,this._right=e._right,this._correctedBottoms=e._correctedBottoms,this.vIndex=e.vIndex,this.hIndex=e.hIndex,this.pageX=e.pageX,this.pageY=e.pageY},e.createFrom=function(t){var n=new e;return n.assign(t),n},e.prototype.clone=function(){var t=new e;return t.assign(this),t},e.prototype.getCurrentPageBottom=function(){return this.getPageBottom(this.vIndex)},e.prototype.getPageBottom=function(e){var t;return null!==(t=this._correctedBottoms[e])&&void 0!==t?t:this._bottom},e.prototype.getPageHeight=function(e){return this.getPageBottom(e)-this._top},e}();t.PageNavigation=n},3424:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PdfGanttPaginator=void 0;var r=n(8900),i=n(6353),o=n(4991),s=n(3917),a=n(2485),l=n(7802),c=n(8935),u=n(6032),d=n(4429),p=function(e,t,n,r,i){this.pageVerIndex=t,this.pageHorIndex=e,this.cellRowIndexOnPage=n,this.cellColIndexOnPage=r,this.cell=i},h=function(){function e(e,t,n,r){this.pageIndex=e,this.globalCellIndex=t,this.pageOffset=n,this.cutSize=r}return Object.defineProperty(e.prototype,"isCutted",{get:function(){return this.cutSize>0},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"cellIndexOnPage",{get:function(){return this.globalCellIndex-this.pageOffset},enumerable:!1,configurable:!0}),e}(),f=function(){function e(e,t,n){this._pdfDoc=e,this._props=t,this._globalInfo=n}return e.prototype.getPages=function(){return delete this._pages,this._paginateTables(),this._paginateObjects(),this.pageMatrixToArray},e.prototype._paginateTables=function(){this._paginateTable(c.PdfPageTableNames.treeListHeader),this._paginateTable(c.PdfPageTableNames.treeListMain),this._paginateTable(c.PdfPageTableNames.chartScaleBottom),this._paginateTable(c.PdfPageTableNames.chartScaleTop),this._paginateTable(c.PdfPageTableNames.chartMain)},e.prototype._paginateObjects=function(){this._paginateTasks(),this._paginateDependencies(),this._paginateResources(),this._paginateTimeMarkers()},Object.defineProperty(e.prototype,"pageMatrixToArray",{get:function(){var e,t=new Array;return null===(e=this._pages)||void 0===e||e.forEach((function(e){t=t.concat(e)})),t},enumerable:!1,configurable:!0}),e.prototype._paginateTasks=function(){var e,t,n=this;null===(t=null===(e=this._globalInfo.objects)||void 0===e?void 0:e.tasks)||void 0===t||t.forEach((function(e){return n._paginateTask(e)}))},e.prototype._paginateDependencies=function(){var e,t,n=this;null===(t=null===(e=this._globalInfo.objects)||void 0===e?void 0:e.dependencies)||void 0===t||t.forEach((function(e){e.arrowInfo?n._paginateArrow(e):n._paginateDependencyLine(e)}))},e.prototype._paginateResources=function(){var e,t,n=this;null===(t=null===(e=this._globalInfo.objects)||void 0===e?void 0:e.resources)||void 0===t||t.forEach((function(e){return n._paginateResource(e)}))},e.prototype._paginateTimeMarkers=function(){var e,t,n=this;null===(t=null===(e=this._globalInfo.objects)||void 0===e?void 0:e.timeMarkers)||void 0===t||t.forEach((function(e){return n._paginateTimeMarker(e)}))},e.prototype._paginateTable=function(e){var t,n=null===(t=this._globalInfo)||void 0===t?void 0:t.tables[e];if(n){for(var r=this._getTableStart(n),i=this._preparePagesNavigationMatrixForTable(r,n),o=i.length,s=0;s<o;s++)for(var a=i[s].length,l=0;l<a;l++){var c=i[s][l],u=this._getPage(c.pageVerIndex,c.pageHorIndex,!0),d=c.pageHorIndex===r.hIndex?r.pageX:this.pageLeft,p=c.pageVerIndex===r.vIndex?r.pageY:this.pageTop;this._setTablePositionOnPage(u,e,d,p),this._addCellToPage(u,e,c)}this._updateTableSizeOnPages(e)}},e.prototype._paginateTask=function(e){for(var t=this._getTaskPagination(e),n=this._getTaskPagination(e,!0),r=0;r<n.length;r++)for(var i=0;i<t.length;i++){var o=new s.PdfTaskInfo;o.assign(e),this._offsetPoints(o.sidePoints,t[i].offset,n[r].offset),this._addTaskToPage(n[r].pageIndex,t[i].pageIndex,o)}},e.prototype._paginateArrow=function(e){var t=this._getPointPageInfo(e.points[0]),n=new o.PdfDependencyLineInfo;n.assign(e),this._offsetPoints(n.points,t.offsetX,t.offsetY),this._addDependencyToPage(t.pageVerIndex,t.pageHorIndex,n)},e.prototype._paginateDependencyLine=function(e){for(var t=this._getDependencyLinePagination(e),n=this._getDependencyLinePagination(e,!0),r=0;r<n.length;r++)for(var i=0;i<t.length;i++){var s=new o.PdfDependencyLineInfo;s.assign(e),this._offsetPoints(s.points,t[i].offset,n[r].offset),this._addDependencyToPage(n[r].pageIndex,t[i].pageIndex,s)}},e.prototype._paginateResource=function(e){var t=this._getPointPageInfo(new r.Point(e.x,e.y)),n=new a.PdfTaskResourcesInfo;n.assign(e),n.x-=t.offsetX,n.y-=t.offsetY,this._addResourceToPage(t.pageVerIndex,t.pageHorIndex,n)},e.prototype._paginateTimeMarker=function(e){for(var t=this._getTimeMarkerPagination(e),n=this._getTimeMarkerPagination(e,!0),r=0;r<n.length;r++)for(var i=0;i<t.length;i++){var o=new l.PdfTimeMarkerInfo;o.assign(e),o.start.x-=t[i].offset,o.start.y-=n[r].offset,this._addTimeMarkerToPage(n[r].pageIndex,t[i].pageIndex,o)}},e.prototype._getTableStart=function(e){var t=new d.PageNavigation(this.pageBorders,0,0,0,0,this.correctedPageBottoms);return t.offset(e.position.x,e.position.y),t},e.prototype._getPage=function(e,t,n){return n&&this._extendPageMatrixIfRequired(e,t),this._pages[e]&&this._pages[e][t]},e.prototype._getTableOrCreate=function(e,t){var n,r;return null!==(n=(r=e.tables)[t])&&void 0!==n||(r[t]=this._createTable(t)),e.tables[t]},e.prototype._preparePagesNavigationMatrixForTable=function(e,t){for(var n=new Array,r=this._getTableNavigationVector(e,t,!0),i=r.length,o=0;o<i;o++){for(var s=new Array,a=r[o],l=this._getTableNavigationVector(e,t,!1,a.globalCellIndex),u=l.length,d=0;d<u;d++){var h=l[d],f=t.name===c.PdfPageTableNames.chartScaleTop,g=this._prepareCuttedCell(t.cells[a.globalCellIndex][h.globalCellIndex],h,a,f),y=new p(h.pageIndex,a.pageIndex,a.cellIndexOnPage,h.cellIndexOnPage,g);s.push(y)}n.push(s)}return n},e.prototype._setTablePositionOnPage=function(e,t,n,i){this._getTableOrCreate(e,t).position=new r.Point(n,i)},e.prototype._extendPageMatrixIfRequired=function(e,t){var n;null!==(n=this._pages)&&void 0!==n||(this._pages=new Array);for(var r=this._pages.length;r<=e;r++)this._pages.push(new Array);var i=this._pages[e];for(r=i.length;r<=t;r++)i.push(this._createPage())},e.prototype._getTableAndExtendIfRequired=function(e,t,n,r){for(var i=this._getTableOrCreate(e,t),o=i.cells,s=o.length;s<=n;s++)o.push(new Array);var a=o[n];for(s=a.length;s<=r;s++)a.push(new u.CellDef);return i},e.prototype._createPage=function(){return{objects:{tasks:null,dependencies:null,resources:null,timeMarkers:null},tables:{}}},e.prototype._createTable=function(e){var t,n=null===(t=this._globalInfo)||void 0===t?void 0:t.tables[e];return{name:e,size:null,position:null,style:n.style,baseCellSize:n.baseCellSize,cells:new Array,hideRowLines:n.hideRowLines}},e.prototype._addCellToPage=function(e,t,n){var r=n.cellRowIndexOnPage,i=n.cellColIndexOnPage;this._getTableAndExtendIfRequired(e,t,r,i).cells[r][i].assign(n.cell)},e.prototype._updateTableSizeOnPages=function(e){for(var t,n=null===(t=this._pages[0])||void 0===t?void 0:t.length,r=this._pages.length,i=0;i<r;i++)for(var o=0;o<n;o++)this._updateTableSizeOnPage(this._pages[i][o],e)},e.prototype._updateTableSizeOnPage=function(e,t){var n,r=this,o=null==e?void 0:e.tables[t];if(o){var s=o.cells.length*o.baseCellSize.height||0,a=(null===(n=o.cells[0])||void 0===n?void 0:n.reduce((function(e,t,n){return e+r._getCellWidth(o,0,n)}),0))||0;o.size=new i.Size(a,s)}},e.prototype._getTableNavigationVector=function(e,t,n,r){var i,o;void 0===n&&(n=!1),void 0===r&&(r=0);for(var s=new Array,a=d.PageNavigation.createFrom(e),l=n?null===(i=t.cells)||void 0===i?void 0:i.length:null===(o=t.cells[r])||void 0===o?void 0:o.length,c=0;c<l;c++){var u=n?t.baseCellSize.height:this._getCellWidth(t,r,c);this._placeCell(s,a,c,u,n)}return s},e.prototype._placeCell=function(e,t,n,r,i){var o,s,a=i?t.vIndex:t.hIndex,l=null!==(s=null===(o=e[e.length-1])||void 0===o?void 0:o.pageOffset)&&void 0!==s?s:n,c=r,u=t.getSpaceToBorder(i);t.offsetOneD(r,i);var d=i?t.vIndex:t.hIndex;if(!i)for(var p=a;p<d;p++){var f=new h(p,n,l,u);l=n,e.push(f),c-=u,u=t.getPageSize(i)}d!==a&&(l=n);var g=new h(d,n,l,c!==r?c:null);e.push(g)},e.prototype._prepareCuttedCell=function(e,t,n,r){var i=new u.CellDef(e);if(t.isCutted){var o=t.cutSize;if(!r){var s=i.content,a=e.styles,l=o-(a&&a.cellPadding.left||0)-(a&&a.cellPadding.right||0),c=this._pdfDoc.splitTextToSize(s,l);e.content=s.replace(c[0],""),i.content=c[0]}i.styles.cellWidth.assign(o)}return n.isCutted&&(i.styles.minCellHeight=n.cutSize),i},e.prototype._getCellWidth=function(e,t,n){var r,i=e.cells[t][n],o=i.styles,s=o.cellWidth.getValue(),a=null!=s?s:o.minCellWidth;return null!=a?a:e.baseCellSize.width*(null!==(r=i.colSpan)&&void 0!==r?r:1)},e.prototype._getTaskPagination=function(e,t){var n=t?e.height:e.width,r=t?e.top:e.left;return this._getLinePagination(r,n,t)},e.prototype._getDependencyLinePagination=function(e,t){var n=e.points[0],r=e.points[1],i=t?r.y-n.y:r.x-n.x,o=t?n.y:n.x;return this._getLinePagination(o,i,t)},e.prototype._getTimeMarkerPagination=function(e,t){var n=t?e.size.height:e.size.width,r=t?e.start.y:e.start.x;return this._getLinePagination(r,n,t)},e.prototype._getLinePagination=function(e,t,n){var r=new Array,i=this.pageNavigator.clone();i.offsetOneD(e,n);var o=n?i.vIndex:i.hIndex;i.offsetOneD(t,n);for(var s=n?i.vIndex:i.hIndex,a=o;a<=s;a++)r.push({offset:i.getPageGlobalOffset(a,n),pageIndex:a});return r},e.prototype._getPointPageInfo=function(e){var t=this.pageNavigator.clone();return t.offset(e.x,e.y),{offsetX:t.getPageGlobalOffset(t.hIndex),offsetY:t.getPageGlobalOffset(t.vIndex,!0),pageHorIndex:t.hIndex,pageVerIndex:t.vIndex}},Object.defineProperty(e.prototype,"pageWidth",{get:function(){var e;return null===(e=this._pdfDoc)||void 0===e?void 0:e.getPageWidth()},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"pageHeight",{get:function(){var e;return null===(e=this._pdfDoc)||void 0===e?void 0:e.getPageHeight()},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"pageLeftMargin",{get:function(){var e;return null===(e=this._props)||void 0===e?void 0:e.margins.left},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"pageTopMargin",{get:function(){var e;return null===(e=this._props)||void 0===e?void 0:e.margins.top},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"pageRightMargin",{get:function(){var e;return null===(e=this._props)||void 0===e?void 0:e.margins.right},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"pageBottomMargin",{get:function(){var e;return null===(e=this._props)||void 0===e?void 0:e.margins.bottom},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"pageLeft",{get:function(){var e;return null!==(e=this._pageLeft)&&void 0!==e||(this._pageLeft=this.pageLeftMargin),this._pageLeft},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"pageRight",{get:function(){var e;return null!==(e=this._pageRight)&&void 0!==e||(this._pageRight=this.pageWidth-this.pageRightMargin),this._pageRight},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"pageTop",{get:function(){var e;return null!==(e=this._pageTop)&&void 0!==e||(this._pageTop=this.pageTopMargin),this._pageTop},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"pageBottom",{get:function(){var e;return null!==(e=this._pageBottom)&&void 0!==e||(this._pageBottom=this.pageHeight-this.pageBottomMargin),this._pageBottom},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"pageBorders",{get:function(){return{left:this.pageLeft,top:this.pageTop,bottom:this.pageBottom,right:this.pageRight}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"correctedPageBottoms",{get:function(){var e;return null!==(e=this._correctedPageBottoms)&&void 0!==e||(this._correctedPageBottoms=this._getCorrectedPagesBottom()),this._correctedPageBottoms},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"pageNavigator",{get:function(){var e;return null!==(e=this._pageNavigator)&&void 0!==e||(this._pageNavigator=new d.PageNavigation(this.pageBorders,0,0,0,0,this.correctedPageBottoms)),this._pageNavigator},enumerable:!1,configurable:!0}),e.prototype._getCorrectedPagesBottom=function(){var e,t,n,r,i=new Array,o=null===(e=this._globalInfo)||void 0===e?void 0:e.tables,s=null!==(t=o[c.PdfPageTableNames.treeListMain])&&void 0!==t?t:o[c.PdfPageTableNames.chartMain],a=new d.PageNavigation(this.pageBorders);a.pageY=s.position.y;for(var l=0;l<s.cells.length;l++){var u=null!==(r=null===(n=s.cells[l][0].styles)||void 0===n?void 0:n.minCellHeight)&&void 0!==r?r:s.baseCellSize.height,p=a.vIndex,h=a.pageY;a.offsetOneD(u,!0),p!==a.vIndex&&(i.push(h),a.pageY=a.getPageStart(!0)+u)}return i},e.prototype._addTaskToPage=function(e,t,n){var r,i,o=this._getPage(e,t);o&&(null!==(r=(i=o.objects).tasks)&&void 0!==r||(i.tasks=new Array),o.objects.tasks.push(n))},e.prototype._addDependencyToPage=function(e,t,n){var r,i,o=this._getPage(e,t);o&&(null!==(r=(i=o.objects).dependencies)&&void 0!==r||(i.dependencies=new Array),o.objects.dependencies.push(n))},e.prototype._addResourceToPage=function(e,t,n){var r,i,o=this._getPage(e,t);o&&(null!==(r=(i=o.objects).resources)&&void 0!==r||(i.resources=new Array),o.objects.resources.push(n))},e.prototype._addTimeMarkerToPage=function(e,t,n){var r,i,o=this._getPage(e,t);o&&(null!==(r=(i=o.objects).timeMarkers)&&void 0!==r||(i.timeMarkers=new Array),o.objects.timeMarkers.push(n))},e.prototype._offsetPoints=function(e,t,n){e.forEach((function(e){e.x-=t,e.y-=n}))},e}();t.PdfGanttPaginator=f},5763:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ScalingHelper=void 0;var r=n(8935),i=function(){function e(e){this._doc=e}return Object.defineProperty(e.prototype,"_docScaleFactor",{get:function(){var e,t;return null===(t=null===(e=this._doc)||void 0===e?void 0:e.internal)||void 0===t?void 0:t.scaleFactor},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"_correctScaleNeeded",{get:function(){return this._docScaleFactor&&Math.abs(this._docScaleFactor-e._defaultScaleFactor)>Number.EPSILON},enumerable:!1,configurable:!0}),e.prototype.getScaledSize=function(t){return t*e._defaultScaleFactor/this._docScaleFactor},e.prototype.scalePageMargins=function(e){var t,n,r,i;this._correctScaleNeeded&&((null===(t=null==e?void 0:e.margins)||void 0===t?void 0:t.left)&&(e.margins.left=this.getScaledSize(e.margins.left)),(null===(n=null==e?void 0:e.margins)||void 0===n?void 0:n.right)&&(e.margins.right=this.getScaledSize(e.margins.right)),(null===(r=null==e?void 0:e.margins)||void 0===r?void 0:r.top)&&(e.margins.top=this.getScaledSize(e.margins.top)),(null===(i=null==e?void 0:e.margins)||void 0===i?void 0:i.bottom)&&(e.margins.bottom=this.getScaledSize(e.margins.bottom)))},e.prototype.scaleSizes=function(e){this._correctScaleNeeded&&(this.scaleTables(e),this.scaleObjects(e.objects))},e.prototype.scaleTables=function(e){(null==e?void 0:e.tables)&&(this.scaleTable(e.tables[r.PdfPageTableNames.treeListHeader]),this.scaleTable(e.tables[r.PdfPageTableNames.treeListMain]),this.scaleTable(e.tables[r.PdfPageTableNames.chartMain]),this.scaleTable(e.tables[r.PdfPageTableNames.chartScaleTop]),this.scaleTable(e.tables[r.PdfPageTableNames.chartScaleBottom]))},e.prototype.scaleTable=function(e){var t,n,r,i,o,s;if(e&&((null===(t=e.size)||void 0===t?void 0:t.width)&&(e.size.width=this.getScaledSize(e.size.width)),(null===(n=e.size)||void 0===n?void 0:n.height)&&(e.size.height=this.getScaledSize(e.size.height)),(null===(r=e.position)||void 0===r?void 0:r.x)&&(e.position.x=this.getScaledSize(e.position.x)),(null===(i=e.position)||void 0===i?void 0:i.y)&&(e.position.y=this.getScaledSize(e.position.y)),(null===(o=e.baseCellSize)||void 0===o?void 0:o.width)&&(e.baseCellSize.width=this.getScaledSize(e.baseCellSize.width)),(null===(s=e.baseCellSize)||void 0===s?void 0:s.height)&&(e.baseCellSize.height=this.getScaledSize(e.baseCellSize.height)),e.cells))for(var a=0;a<e.cells.length;a++)for(var l=e.cells[a],c=0;c<l.length;c++){var u=l[c];this.scaleStyle(u.styles)}},e.prototype.scaleObjects=function(e){this.scaleTasks(null==e?void 0:e.tasks),this.scaleDependencies(null==e?void 0:e.dependencies),this.scaleResources(null==e?void 0:e.resources),this.scaleTimeMarkers(null==e?void 0:e.timeMarkers)},e.prototype.scaleTasks=function(e){var t=this;null==e||e.forEach((function(e){t.scalePoints(e.sidePoints),e.progressWidth=t.getScaledSize(e.progressWidth),t.scaleStyle(e.textStyle)}))},e.prototype.scaleDependencies=function(e){var t=this;null==e||e.forEach((function(e){var n;t.scalePoints(e.points),(null===(n=e.arrowInfo)||void 0===n?void 0:n.width)&&(e.arrowInfo.width=t.getScaledSize(e.arrowInfo.width))}))},e.prototype.scaleResources=function(e){var t=this;null==e||e.forEach((function(e){e.x=t.getScaledSize(e.x),e.y=t.getScaledSize(e.y),t.scaleStyle(e.style)}))},e.prototype.scaleTimeMarkers=function(e){var t=this;null==e||e.forEach((function(e){e.start.x=t.getScaledSize(e.start.x),e.start.y=t.getScaledSize(e.start.y),e.size.width=t.getScaledSize(e.size.width),e.size.height=t.getScaledSize(e.size.height)}))},e.prototype.scaleStyle=function(e){var t,n,r,i;if(e){var o=e.cellWidth;if(null==o?void 0:o.hasValue()){var s=this.getScaledSize(Number(o.getValue()));o.assign(s)}e.minCellHeight&&(e.minCellHeight=this.getScaledSize(e.minCellHeight)),e.minCellWidth&&(e.minCellWidth=this.getScaledSize(e.minCellWidth)),(null===(t=e.cellPadding)||void 0===t?void 0:t.left)&&(e.cellPadding.left=this.getScaledSize(e.cellPadding.left)),(null===(n=e.cellPadding)||void 0===n?void 0:n.right)&&(e.cellPadding.right=this.getScaledSize(e.cellPadding.right)),(null===(r=e.cellPadding)||void 0===r?void 0:r.top)&&(e.cellPadding.top=this.getScaledSize(e.cellPadding.top)),(null===(i=e.cellPadding)||void 0===i?void 0:i.bottom)&&(e.cellPadding.bottom=this.getScaledSize(e.cellPadding.bottom))}},e.prototype.scalePoints=function(e){var t=this;null==e||e.forEach((function(e){e.x=t.getScaledSize(e.x),e.y=t.getScaledSize(e.y)}))},e._defaultScaleFactor=.75,e}();t.ScalingHelper=i},7223:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PdfCustomFontSettings=void 0;var r=n(2491),i=function(){function e(e){this.style="normal",e&&this.assign(e)}return e.prototype.assign=function(e){(0,r.isDefined)(e.fontObject)&&(this.fontObject=e.fontObject),(0,r.isDefined)(e.name)&&(this.name=e.name),(0,r.isDefined)(e.style)&&(this.style=e.style),(0,r.isDefined)(e.weight)&&(this.weight=e.weight)},e.prototype.applyToDoc=function(e){try{if(e&&this.fontObject&&this.name){var t=this.name+"-"+this.style+".ttf";e.addFileToVFS(t,this.fontObject),e.addFont(t,this.name,this.style,this.weight),e.setFont(this.name)}}catch(e){}},e}();t.PdfCustomFontSettings=i},6431:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PdfDataRange=void 0;var r=n(2491),i=function(){function e(e,t,n,r){var i=!e||e instanceof Date?{startDate:e,endDate:t,startIndex:n,endIndex:r}:e;i&&this.assign(i)}return e.prototype.assign=function(e){(0,r.isDefined)(e.startDate)&&(this.startDate=e.startDate instanceof Date?e.startDate:new Date(e.startDate)),(0,r.isDefined)(e.endDate)&&(this.endDate=e.endDate instanceof Date?e.endDate:new Date(e.endDate)),(0,r.isDefined)(e.startIndex)&&(this.startIndex=parseInt(e.startIndex)),(0,r.isDefined)(e.endIndex)&&(this.endIndex=parseInt(e.endIndex))},e}();t.PdfDataRange=i},9895:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DataExportMode=t.ExportMode=void 0,function(e){e[e.all=0]="all",e[e.treeList=1]="treeList",e[e.chart=2]="chart"}(t.ExportMode||(t.ExportMode={})),function(e){e[e.all=0]="all",e[e.visible=1]="visible"}(t.DataExportMode||(t.DataExportMode={}))},6997:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.GanttPdfExportProps=void 0;var r=n(6353),i=n(2491),o=n(5063),s=n(6431),a=n(7223),l=n(9895),c=function(){function e(e){this.landscape=!1,this.margins=null,this.exportMode=l.ExportMode.all,this.exportDataMode=l.DataExportMode.visible,e&&this.assign(e)}return e.prototype.assign=function(e){if(e){if((0,i.isDefined)(e.pdfDocument)&&(this.pdfDoc=e.pdfDocument),(0,i.isDefined)(e.pdfDoc)&&(this.pdfDoc=e.pdfDoc),this.docCreateMethod=e.docCreateMethod,(0,i.isDefined)(e.fileName)&&(this.fileName=e.fileName),this.landscape=!!e.landscape,(0,i.isDefined)(e.margins)&&(this.margins=new o.Margin(e.margins)),(0,i.isDefined)(e.format)){var t=e.format;if("string"==typeof t)this.format=t;else{var n=parseInt(t.width),c=parseInt(t.height);this.pageSize=new r.Size(n,c)}}else if((0,i.isDefined)(e.pageSize)){var u=e.pageSize;this.pageSize=u instanceof r.Size?u.clone():new r.Size(u.width,u.height)}if((0,i.isDefined)(e.exportMode)&&(this.exportMode=this.getEnumValue(l.ExportMode,e.exportMode)),(0,i.isDefined)(e.dateRange)){var d=e.dateRange;"number"==typeof d||"string"==typeof d?this.exportDataMode=this.getEnumValue(l.DataExportMode,d):this.dateRange=new s.PdfDataRange(d)}(0,i.isDefined)(e.font)&&(this.font=new a.PdfCustomFontSettings(e.font))}},e.prototype.getEnumValue=function(e,t){if(!(0,i.isDefined)(e[t]))return null;var n=parseInt(t);return isNaN(n)?e[t]:n},e.autoFormatKey="auto",e}();t.GanttPdfExportProps=c},6032:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.CellDef=void 0;var r=n(2491),i=n(6995),o=function(){function e(e,t,n){this.content="","string"==typeof e?(this.content=e,this.colSpan=t,n&&this.appendStyles(n)):e&&this.assign(e)}return Object.defineProperty(e.prototype,"styles",{get:function(){return this._styles||(this._styles=new i.StyleDef),this._styles},enumerable:!1,configurable:!0}),e.prototype.assign=function(e){(0,r.isDefined)(e.content)&&(this.content=e.content),(0,r.isDefined)(e.colSpan)&&(this.colSpan=e.colSpan),e.styles&&this.appendStyles(e.styles)},e.prototype.appendStyles=function(e){e&&this.styles.assign(e)},e.prototype.hasValue=function(){return!0},e.prototype.getValue=function(){var e={};return e.content=this.content,this.colSpan>1&&(e.colSpan=this.colSpan),this._styles&&(e.styles=this._styles.getValue()),e},e}();t.CellDef=o},405:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Color=void 0;var r=n(2491),i=function(){function e(e){this._num=null,this._opacity=1,this._rgb=null,this.assign(e)}return Object.defineProperty(e.prototype,"opacity",{get:function(){return this._opacity},enumerable:!1,configurable:!0}),e.prototype.hasValue=function(){return(0,r.isDefined)(this._num)||!!this._rgb||0===this._opacity},e.prototype.getValue=function(){return!(0===this._opacity&&!this._rgb)&&((0,r.isDefined)(this._num)?this._num:this._rgb?this.getRBGColor():null)},e.prototype.assign=function(t){this.reset(),"string"==typeof t&&this.assignFromString(t),"number"==typeof t&&(this._num=t),t instanceof Array&&this.assignFromRgbArray(t),t instanceof e&&this.assignFromColor(t)},e.prototype.reset=function(){this._opacity=1,this._num=null,this._rgb=null},e.prototype.assignFromString=function(e){e&&("transparent"===e&&(this._opacity=0),0===e.indexOf("#")&&this.assignFromHexString(e),"rgb"===e.substr(0,3).toLowerCase()&&this.assignFromRgbString(e))},e.prototype.assignFromHexString=function(e){if(4===e.length&&(e="#"+e[1].repeat(2)+e[2].repeat(2)+e[3].repeat(2)),e.length>6){var t=parseInt(e.substr(1,2),16),n=parseInt(e.substr(3,2),16),r=parseInt(e.substr(5,2),16);this._rgb=[t,n,r]}},e.prototype.assignFromRgbString=function(t){var n="rgba"===t.substr(0,4).toLowerCase(),r=t.toLowerCase().match(n?e.rgbaRegexp:e.rgbRegexp);if(r){var i=parseInt(r[1]),o=parseInt(r[2]),s=parseInt(r[3]);this._rgb=[i,o,s],n&&(this._opacity=parseFloat(r[4]))}},e.prototype.assignFromRgbArray=function(e){e&&e.length>2&&(this._rgb=[e[0],e[1],e[2]],(0,r.isDefined)(e[3])&&(this._opacity=e[3]))},e.prototype.assignFromColor=function(e){this._opacity=e._opacity,this._num=e._num,this._rgb=e._rgb},e.prototype.getRBGColor=function(){return this._rgb?this._rgb:[0,0,0]},e.prototype.applyOpacityToBackground=function(t){if(1!==this._opacity){var n=(t instanceof e?t:new e(t)).getValue();if(n instanceof Array){var r=this.opacity,i=Math.round((1-r)*n[0]+r*this._rgb[0]),o=Math.round((1-r)*n[1]+r*this._rgb[1]),s=Math.round((1-r)*n[2]+r*this._rgb[2]);this._rgb=[i,o,s]}}},e.rgbRegexp=/rgb\s*\(\s*([0-9]+)\s*,\s*([0-9]+)\s*,\s*([0-9]+)\s*\)/,e.rgbaRegexp=/rgba?\s*\(\s*([0-9]+)\s*,\s*([0-9]+)\s*,\s*([0-9]+)\s*,?\s*([0-9]*\.?[0-9]*)\s*\)/,e}();t.Color=i},5510:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PdfGanttTableDrawer=void 0;var r=n(2491),i=n(8935),o=n(817),s=n(7624),a=function(){function e(e){this._pdfDoc=e}return e.prototype.drawTable=function(e){var t,n;if(e){var r=this.createTableOptions(e);(null===(t=e.style)||void 0===t?void 0:t.fontSize)&&this._pdfDoc.setFontSize(null===(n=e.style)||void 0===n?void 0:n.fontSize),this._pdfDoc.autoTable(r.getValue())}},e.prototype.createTableOptions=function(e){var t=this.createDefaultTableOptions();return this.addTableCommonSettings(e,t),this.addCommonTableStyles(e,t),this.prepareBodyCells(e),t.addBody(e.cells),e.hideRowLines&&this.hideRowLines(t),t},e.prototype.createDefaultTableOptions=function(){var e=new s.TableOptions;return e.pageBreak="auto",e.margin.assign(0),e.tableWidth.assign("auto"),e.styles.cellPadding.assign(0),e.styles.halign="center",e.styles.valign="middle",e.styles.lineWidth=1,e.styles.overflow="hidden",e},e.prototype.addTableCommonSettings=function(e,t){t.startY=e.position.y,t.margin.assign({left:e.position.x}),t.tableWidth.assign(e.size.width)},e.prototype.addCommonTableStyles=function(e,t){var n=t.styles;n.assign(e.style),0===n.fillColor.opacity&&n.fillColor.assign("#FFFFFF"),n.minCellHeight=e.baseCellSize.height,t.alternateRowStyles.minCellHeight=t.styles.minCellHeight,t.alternateRowStyles.fillColor.assign(t.styles.fillColor),(0,r.isDefined)(e.baseCellSize.width)&&n.cellWidth.assign(e.baseCellSize.width)},e.prototype.prepareBodyCells=function(t){var n,r,s;if(t.name===i.PdfPageTableNames.treeListMain||t.name===i.PdfPageTableNames.chartScaleTop||t.name===i.PdfPageTableNames.chartScaleBottom)for(var a=t.cells,l=0;l<a.length;l++)for(var c=a[l],u=0;u<c.length;u++){var d=c[u],p=d.styles,h=(null===(n=null==p?void 0:p.cellWidth)||void 0===n?void 0:n.getValue())||t.baseCellSize.width||0,f=null!==(r=null==p?void 0:p.cellPadding.left)&&void 0!==r?r:0,g=null!==(s=null==p?void 0:p.cellPadding.right)&&void 0!==s?s:0,y=Math.max(h-f-g-e.cellEllipsisSpace,0);d.content=o.EllipsisHelper.limitPdfTextWithEllipsis(d.content,this._pdfDoc,y)}},e.prototype.hideRowLines=function(e){e.styles.lineWidth=0,e.onDrawCellCallback=function(e){var t=e.cell,n=e.doc,r=t.styles.lineColor,i=t.x,o=t.x+t.styles.cellWidth,s=t.y,a=t.y+e.row.height,l=e.column.index===e.table.columns.length-1,c=e.row.index===e.table.body.length-1,u=0===e.row.index;n.setDrawColor(r[0],r[1],r[2]),n.setLineWidth(1),n.line(i,a,i,s),l&&n.line(o,a,o,s),u&&n.line(i,s,o,s),c&&n.line(i,a,o,a)}},e.cellEllipsisSpace=3,e}();t.PdfGanttTableDrawer=a},817:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.EllipsisHelper=void 0;var n=function(){function e(){}return e.limitPdfTextWithEllipsis=function(t,n,r){if(!(null==n?void 0:n.getTextWidth)||!r)return t;if(n.getTextWidth(t.toString())>r){for(var i=t,o=t.length-1;n.getTextWidth(i)>r&&o>0;)i=i.substring(0,o)+e.ellipsis,o--;return i}return t},e.ellipsis="...",e}();t.EllipsisHelper=n},5063:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Margin=void 0;var r=n(2491),i=n(6907),o=function(){function e(e){this.assign(e)}return e.prototype.assign=function(e){if((0,r.isDefined)(e))if("string"==typeof e)this.assignFromString(e);else if("number"==typeof e||e instanceof Array)this.assignWithValues(e);else{var t=e||e;this.assignWithMargin(t)}},e.prototype.assignFromString=function(e){var t=e.split(" ").map((function(e){return i.DomUtils.pxToInt(e)}));this.assignWithValues(t)},e.prototype.assignWithMargin=function(e){(0,r.isDefined)(e.top)&&(this.top=e.top),(0,r.isDefined)(e.right)&&(this.right=e.right),(0,r.isDefined)(e.bottom)&&(this.bottom=e.bottom),(0,r.isDefined)(e.left)&&(this.left=e.left)},e.prototype.assignWithValues=function(e){var t=this.getCorrectedValues(e);this.top=t[0],this.right=t[1],this.bottom=t[2],this.left=t[3]},e.prototype.getCorrectedValues=function(e){var t=[this.top,this.right,this.bottom,this.left];if("number"==typeof e){t=[e,e,e,e]}else{var n=e;switch(n.length){case 1:t=[n[0],n[0],n[0],n[0]];break;case 2:t=[n[0],n[1],n[0],n[1]];break;case 3:t=[n[0],n[1],n[2],n[1]];break;default:n.forEach((function(e,n){return t[n]=e}))}}return t},e.prototype.hasValue=function(){return(0,r.isDefined)(this.top)||(0,r.isDefined)(this.left)||(0,r.isDefined)(this.right)||(0,r.isDefined)(this.bottom)},e.prototype.getValue=function(){if(!this.hasValue())return null;if(this.top===this.bottom&&this.left===this.right&&this.top===this.left)return this.top;var e={};return(0,r.isDefined)(this.top)&&(e.top=this.top),(0,r.isDefined)(this.left)&&(e.left=this.left),(0,r.isDefined)(this.right)&&(e.right=this.right),(0,r.isDefined)(this.bottom)&&(e.bottom=this.bottom),e},e}();t.Margin=o},2642:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PredefinedStyles=void 0;var n=function(){function e(){}return e.getPredefinedStringOrUndefined=function(e,t){var n=e&&t&&e.toLowerCase()||void 0;return n&&(t.filter((function(e){return e.toLowerCase()===n}))[0]||t.filter((function(e){return n.indexOf(e.toLowerCase())>-1}))[0])},e.fontFamilies=["helvetica","times","courier"],e.fontStyles=["normal","bold","italic","bolditalic"],e.headerFooterVisibility=["everyPage","firstPage","never"],e.horizontalAlign=["left","center","right"],e.overflow=["linebreak","ellipsize","visible","hidden"],e.pageBreak=["auto","avoid","always"],e.rowPageBreak=["auto","avoid"],e.verticalAlign=["top","middle","bottom"],e.width=["auto","wrap"],e}();t.PredefinedStyles=n},6995:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.StyleDef=void 0;var r=n(2491),i=n(6907),o=n(405),s=n(2642),a=n(5063),l=n(7343),c=function(){function e(e){this._fillColor=new o.Color,this._textColor=new o.Color,this._lineColor=new o.Color,this._cellWidth=new l.Width,this._cellPadding=new a.Margin,e&&this.assign(e)}return Object.defineProperty(e.prototype,"font",{get:function(){return this._fontFamily},set:function(e){this._fontFamily=s.PredefinedStyles.getPredefinedStringOrUndefined(e,s.PredefinedStyles.fontFamilies)||e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"fontStyle",{get:function(){return this._fontStyle},set:function(e){this._fontStyle=s.PredefinedStyles.getPredefinedStringOrUndefined(e,s.PredefinedStyles.fontStyles)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"fontSize",{get:function(){return this._fontSize},set:function(e){this._fontSize=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"overflow",{get:function(){return this._overflow},set:function(e){this._overflow=s.PredefinedStyles.getPredefinedStringOrUndefined(e,s.PredefinedStyles.overflow)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"halign",{get:function(){return this._horizontalAlign},set:function(e){this._horizontalAlign=s.PredefinedStyles.getPredefinedStringOrUndefined(e,s.PredefinedStyles.horizontalAlign)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"valign",{get:function(){return this._verticalAlign},set:function(e){this._verticalAlign=s.PredefinedStyles.getPredefinedStringOrUndefined(e,s.PredefinedStyles.verticalAlign)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"fillColor",{get:function(){return this._fillColor},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"textColor",{get:function(){return this._textColor},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"lineColor",{get:function(){return this._lineColor},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"cellWidth",{get:function(){return this._cellWidth},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"cellPadding",{get:function(){return this._cellPadding},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"lineWidth",{get:function(){return this._lineWidth},set:function(e){this._lineWidth=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"minCellWidth",{get:function(){return this._minCellWidth},set:function(e){this._minCellWidth=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"minCellHeight",{get:function(){return this._minCellHeight},set:function(e){this._minCellHeight=e},enumerable:!1,configurable:!0}),e.prototype.assign=function(t){t&&(t instanceof e?((0,r.isDefined)(t.font)&&(this.font=t.font),(0,r.isDefined)(t.fontStyle)&&(this.fontStyle=t.fontStyle),(0,r.isDefined)(t.overflow)&&(this.overflow=t.overflow),(0,r.isDefined)(t.halign)&&(this.halign=t.halign),(0,r.isDefined)(t.valign)&&(this.valign=t.valign),(0,r.isDefined)(t.fontSize)&&(this.fontSize=t.fontSize),(0,r.isDefined)(t.lineWidth)&&(this.lineWidth=t.lineWidth),(0,r.isDefined)(t.minCellWidth)&&(this.minCellWidth=t.minCellWidth),(0,r.isDefined)(t.minCellHeight)&&(this.minCellHeight=t.minCellHeight),(0,r.isDefined)(t.fillColor)&&this.fillColor.assign(t.fillColor),(0,r.isDefined)(t.textColor)&&this.textColor.assign(t.textColor),(0,r.isDefined)(t.lineColor)&&this.lineColor.assign(t.lineColor),(0,r.isDefined)(t.cellWidth)&&this.cellWidth.assign(t.cellWidth),(0,r.isDefined)(t.cellPadding)&&this.cellPadding.assign(t.cellPadding)):this.assignFromCssStyle(t))},e.prototype.assignFromCssStyle=function(e){if(e.fontFamily&&(this.font=this.getPdfFontFamily(e)),this.fontStyle=this.getPdfFontStyle(e),(0,r.isDefined)(e.fontSize)&&(this.fontSize=this.getPfrFontSize(e.fontSize)),e.textAlign&&(this.halign=e.textAlign),e.verticalAlign&&(this.valign=e.verticalAlign),(0,r.isDefined)(e.borderWidth)&&(this.lineWidth=e.borderWidth),(0,r.isDefined)(e.cellWidth)&&this.cellWidth.assign(e.cellWidth),(0,r.isDefined)(e.width)&&(this.minCellWidth="number"==typeof e.width?e.width:i.DomUtils.pxToInt(e.width)),(0,r.isDefined)(e.height)&&(this.minCellHeight="number"==typeof e.height?e.height:i.DomUtils.pxToInt(e.height)),e.backgroundColor&&this.fillColor.assign(e.backgroundColor),e.color&&this.textColor.assign(e.color),e.borderColor&&this.lineColor.assign(e.borderColor),(0,r.isDefined)(e.width)&&this.cellWidth.assign(e.width),this.assignPaddingFromCss(e),(0,r.isDefined)(e.extraLeftPadding)){var t=this._cellPadding.left;this._cellPadding.left=t?t+e.extraLeftPadding:e.extraLeftPadding}},e.prototype.getPdfFontStyle=function(e){var t=e.fontWeight,n=parseInt(t),r="bold"===t||!isNaN(n)&&n>500,i=r?"bold":"normal";return"italic"===e.fontStyle&&(i=r?"bolditalic":"italic"),i},e.prototype.getPdfFontFamily=function(e){var t=e.fontFamily&&e.fontFamily.toLowerCase(),n="helvetica";return t.indexOf("times")>-1&&(n="times"),t.indexOf("courier")>-1&&(n="courier"),n},e.prototype.getPfrFontSize=function(e){var t=i.DomUtils.pxToInt(e);if(!isNaN(t))return Math.ceil(t/96*72)},e.prototype.assignPaddingFromCss=function(e){if(e.padding)this._cellPadding.assign(e.padding);else{var t={};e.paddingLeft&&(t.left=i.DomUtils.pxToInt(e.paddingLeft)),e.paddingTop&&(t.top=i.DomUtils.pxToInt(e.paddingTop)),e.paddingRight&&(t.right=i.DomUtils.pxToInt(e.paddingRight)),e.paddingBottom&&(t.bottom=i.DomUtils.pxToInt(e.paddingBottom)),this._cellPadding.assign(t)}},e.prototype.hasValue=function(){return!0},e.prototype.getValue=function(){var e=this,t={};return(0,r.isDefined)(this.font)&&(t.font=this.font),(0,r.isDefined)(this.fontStyle)&&(t.fontStyle=this.fontStyle),(0,r.isDefined)(this.fontSize)&&(t.fontSize=this.fontSize),(0,r.isDefined)(this.overflow)&&(t.overflow=this.overflow),(0,r.isDefined)(this.halign)&&(t.halign=this.halign),(0,r.isDefined)(this.valign)&&(t.valign=this.valign),(0,r.isDefined)(this.lineWidth)&&(t.lineWidth=this.lineWidth),(0,r.isDefined)(this.minCellWidth)&&(t.minCellWidth=this.minCellWidth),(0,r.isDefined)(this.minCellHeight)&&(t.minCellHeight=this.minCellHeight),this.getJsPdfProviderProps().forEach((function(n){var r=e[n];r&&r.hasValue()&&(t[n]=r.getValue())})),t},e.prototype.getJsPdfProviderProps=function(){return["fillColor","textColor","lineColor","cellWidth","cellPadding"]},e}();t.StyleDef=c},7624:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TableOptions=void 0;var r=n(2491),i=n(6032),o=n(405),s=n(2642),a=n(5063),l=n(6995),c=n(7343),u=function(){function e(){this._margin=new a.Margin,this._tableLineColor=new o.Color,this._tableWidth=new c.Width,this._styles=new l.StyleDef,this._alternateRowStyles=new l.StyleDef}return Object.defineProperty(e.prototype,"pageBreak",{get:function(){return this._pageBreak},set:function(e){this._pageBreak=s.PredefinedStyles.getPredefinedStringOrUndefined(e,s.PredefinedStyles.pageBreak)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"rowPageBreak",{get:function(){return this._rowPageBreak},set:function(e){this._rowPageBreak=s.PredefinedStyles.getPredefinedStringOrUndefined(e,s.PredefinedStyles.rowPageBreak)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"showHead",{get:function(){return this._showHead},set:function(e){this._showHead=s.PredefinedStyles.getPredefinedStringOrUndefined(e,s.PredefinedStyles.headerFooterVisibility)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"showFoot",{get:function(){return this._showFoot},set:function(e){this._showFoot=s.PredefinedStyles.getPredefinedStringOrUndefined(e,s.PredefinedStyles.headerFooterVisibility)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"startY",{get:function(){return this._startY},set:function(e){this._startY=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"tableLineWidth",{get:function(){return this._tableLineWidth},set:function(e){this._tableLineWidth=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"margin",{get:function(){return this._margin},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"tableLineColor",{get:function(){return this._tableLineColor},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"tableWidth",{get:function(){return this._tableWidth},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"body",{get:function(){return this._body},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"styles",{get:function(){return this._styles},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"alternateRowStyles",{get:function(){return this._alternateRowStyles},enumerable:!1,configurable:!0}),e.prototype.hasValue=function(){return!0},e.prototype.getValue=function(){var e=this,t={};return t.pageBreak=this.pageBreak,t.rowPageBreak=this.rowPageBreak,t.showFoot=this.showFoot,t.showHead=this.showHead,t.startY=this.startY,t.tableLineWidth=this.tableLineWidth,this.getJsPdfProviderProps().forEach((function(n){var r=e[n];r&&r.hasValue()&&(t[n]=r.getValue())})),t.body=this.getBodyForJsPdf(),t.columnStyles=this.getColumnStylesForJsPdf(),this.onDrawCellCallback&&(t.didDrawCell=this.onDrawCellCallback),t},e.prototype.getJsPdfProviderProps=function(){return["margin","tableLineColor","tableWidth","styles","alternateRowStyles"]},e.prototype.getBodyForJsPdf=function(){for(var e=[],t=0;t<this._body.length;t++){for(var n=this._body[t],r=[],i=0;i<n.length;i++)r.push(n[i].getValue());e.push(r)}return e},e.prototype.assign=function(e){e&&((0,r.isDefined)(e.margin)&&this.margin.assign(e.margin),(0,r.isDefined)(e.pageBreak)&&(this.pageBreak=e.pageBreak),(0,r.isDefined)(e.rowPageBreak)&&(this.rowPageBreak=e.rowPageBreak),(0,r.isDefined)(e.showFoot)&&(this.showFoot=e.showFoot),(0,r.isDefined)(e.showHead)&&(this.showHead=e.showHead),(0,r.isDefined)(e.startY)&&(this.startY=e.startY),(0,r.isDefined)(e.tableLineWidth)&&(this.tableLineWidth=e.tableLineWidth),(0,r.isDefined)(e.tableLineColor)&&this.tableLineColor.assign(e.tableLineColor),(0,r.isDefined)(e.tableWidth)&&this.tableWidth.assign(e.tableWidth))},e.prototype.addBody=function(e){e&&(this._body=new Array,this.addCells(e,this._body))},e.prototype.addCells=function(e,t){for(var n=this.styles.fillColor,r=0;r<e.length;r++){for(var o=e[r],s=new Array,a=0;a<o.length;a++){var l=new i.CellDef(o[a]);n.hasValue()&&l.styles&&l.styles.fillColor.hasValue()&&l.styles.fillColor.applyOpacityToBackground(n),s.push(l)}t.push(s)}},e.prototype.applyColumnStyle=function(e,t){var n;null!==(n=this._columnStyles)&&void 0!==n||(this._columnStyles=new Array),this._columnStyles[e]=new l.StyleDef(t)},e.prototype.getColumnStylesForJsPdf=function(){if(this._columnStyles){var e={};return this._columnStyles.forEach((function(t,n){t&&(e[n]=t.getValue())})),e}return null},e}();t.TableOptions=u},7343:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Width=void 0;var r=n(2642),i=n(6907),o=function(){function e(e){this.assign(e)}return e.prototype.assign=function(t){if(t instanceof e)this._widthInternal=t._widthInternal;else{var n="number"==typeof t?t:parseInt(t);isNaN(n)?this.assignFromString(t):this._widthInternal=n}},e.prototype.assignFromString=function(e){if(e){var t=i.DomUtils.pxToInt(e);this._widthInternal=t||r.PredefinedStyles.getPredefinedStringOrUndefined(e,r.PredefinedStyles.width)}},e.prototype.hasValue=function(){return!!this._widthInternal},e.prototype.getValue=function(){return this._widthInternal},e}();t.Width=o},8603:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TaskAreaExportHelper=void 0;var r=n(6907),i=n(858),o=n(1855),s=n(9201),a=n(405),l=n(9895),c=function(){function e(e,t){this._owner=e,this._props=t}return Object.defineProperty(e.prototype,"customRangeLeftOffset",{get:function(){var e;return null!==(e=this._customRangeLeftOffset)&&void 0!==e||(this._customRangeLeftOffset=this.layoutCalculator.getWidthByDateRange(this.startDate,this.ownerStartDate)),this._customRangeLeftOffset},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"baseCellSize",{get:function(){return this._owner.tickSize},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"objectsLeftDelta",{get:function(){return this.renderedScaleLeft},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"objectsTopDelta",{get:function(){if(!this.hasTasks)return 0;var e=this.visibleTaskIndices[0];return this.getCellTop(e)+this.getTaskCellOffsetTop(e)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"offsetLeft",{get:function(){var e;return null!==(e=this._offsetLeft)&&void 0!==e||(this._offsetLeft=Math.max(this.visibleLeft-this.renderedScaleLeft,0)),this._offsetLeft},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"offsetTop",{get:function(){var e;return null!==(e=this._offsetTop)&&void 0!==e||(this._offsetTop=this.getOffsetTop()),this._offsetTop},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"scales",{get:function(){var e=this.settings.viewType;return[s.DateUtils.ViewTypeToScaleMap[e],e]},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"scaleRanges",{get:function(){var e;return null!==(e=this._scaleRanges)&&void 0!==e||(this._scaleRanges=this.layoutCalculator.getScaleRangesInArea(this.scaleLeft,this.scaleRight)),this._scaleRanges},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"scaleBottomStartIndex",{get:function(){return this.scaleRanges[1][0]},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"scaleBottomEndIndex",{get:function(){return this.scaleRanges[1][1]},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"scaleTopStartIndex",{get:function(){return this.scaleRanges[0][0]},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"scaleTopEndIndex",{get:function(){return this.scaleRanges[0][1]},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"scaleTopWidths",{get:function(){var e;return null!==(e=this._scaleTopWidths)&&void 0!==e||(this._scaleTopWidths=this.getScaleTopWidths()),this._scaleTopWidths},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"scaleBottomWidths",{get:function(){var e;return null!==(e=this._scaleBottomWidths)&&void 0!==e||(this._scaleBottomWidths=this.getScaleBottomWidths()),this._scaleBottomWidths},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"headerRowHeight",{get:function(){if(!this._headerRowHeight){var e=this.scaleElements[0].filter((function(e){return!!e}))[0];this._headerRowHeight=null==e?void 0:e.offsetHeight}return this._headerRowHeight},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"visibleTaskIndices",{get:function(){var e;return null!==(e=this._visibleTaskIndices)&&void 0!==e||(this._visibleTaskIndices=this.getTaskIndices()),this._visibleTaskIndices},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"baseCellHeight",{get:function(){return this.hasTasks?this.baseCellSize.height:this.taskAreaHeight},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"taskAreaHeight",{get:function(){return this.hasTasks?this.visibleTaskIndices.length*this.baseCellHeight:this._owner.renderHelper.taskArea.offsetHeight},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"scaleTableStyle",{get:function(){var e;return null!==(e=this._scaleTableStyle)&&void 0!==e||(this._scaleTableStyle=this.getScaleTableStyle()),this._scaleTableStyle},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"chartMainTableStyle",{get:function(){var e;return null!==(e=this._chartMainTableStyle)&&void 0!==e||(this._chartMainTableStyle=this.getChartMainTableStyle()),this._chartMainTableStyle},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"parentRowBackColor",{get:function(){var e;return null!==(e=this._parentRowBackColor)&&void 0!==e||(this._parentRowBackColor=this.getParentBackColor()),this._parentRowBackColor},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"arrowWidth",{get:function(){var e;return null!==(e=this._arrowWidth)&&void 0!==e||(this._arrowWidth=this.getArrowWidth()),this._arrowWidth},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"dependencyColor",{get:function(){var e;return null!==(e=this._dependencyColor)&&void 0!==e||(this._dependencyColor=this.getDependencyColor()),this._dependencyColor},enumerable:!1,configurable:!0}),e.prototype.getTaskElementBackColor=function(e,t){var n=this.getTaskElementStyle(e,t);return null==n?void 0:n.backgroundColor},e.prototype.getTaskElementStyle=function(e,t){var n=this.getTaskWrapper(e);return this.getElementStyle(n.getElementsByClassName(t)[0])},e.prototype.isElementVisible=function(e){return e&&"none"!==getComputedStyle(e).display},Object.defineProperty(e.prototype,"hasTasks",{get:function(){return this.visibleTaskIndices.length>0},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"visibleLeft",{get:function(){var e;return null!==(e=this._visibleLeft)&&void 0!==e||(this._visibleLeft=this.isVisibleMode?this.container.scrollLeft:0),this._visibleLeft},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"visibleTop",{get:function(){var e;return null!==(e=this._visibleTop)&&void 0!==e||(this._visibleTop=this.isVisibleMode?this.container.scrollTop:0),this._visibleTop},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"visibleRight",{get:function(){var e;return null!==(e=this._visibleRight)&&void 0!==e||(this._visibleRight=this.getVisibleRight()),this._visibleRight},enumerable:!1,configurable:!0}),e.prototype.getVisibleRight=function(){var e=this.container.getElement().offsetWidth;return this.visibleLeft+e},Object.defineProperty(e.prototype,"visibleBottom",{get:function(){var e;return null!==(e=this._visibleBottom)&&void 0!==e||(this._visibleBottom=this.getVisibleBottom()),this._visibleBottom},enumerable:!1,configurable:!0}),e.prototype.getVisibleBottom=function(){return this.isVisibleMode?this.visibleTop+this.container.getHeight():this.visibleTaskIndices.length*this.baseCellSize.height},Object.defineProperty(e.prototype,"scaleLeft",{get:function(){var e;return null!==(e=this._scaleLeft)&&void 0!==e||(this._scaleLeft=this.isVisibleMode?this.visibleLeft:this.getPosByDate(this.startDate)),this._scaleLeft},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"scaleRight",{get:function(){var e;return null!==(e=this._scaleRight)&&void 0!==e||(this._scaleRight=this.isVisibleMode?this.visibleRight:this.getPosByDate(this.endDate)-1),this._scaleRight},enumerable:!1,configurable:!0}),e.prototype.getScaleTopWidths=function(){var e=this.getScaleWidths(this.scaleTopStartIndex,this.scaleTopEndIndex,this.scales[0]),t=this.layoutCalculator,n=t.getScaleItemInfo(this.scaleBottomStartIndex,this.scales[1]),r=t.getScaleItemInfo(this.scaleTopStartIndex,this.scales[0]),i=Math.max(n.position.x-r.position.x,0);e[this.scaleTopStartIndex]-=i;var o=t.getScaleItemInfo(this.scaleTopEndIndex,this.scales[0]),s=t.getScaleItemInfo(this.scaleBottomEndIndex,this.scales[1]);return i=Math.max(o.position.x+o.size.width-s.position.x-s.size.width,0),e[this.scaleTopEndIndex]-=i,e},e.prototype.getScaleBottomWidths=function(){return this.getScaleWidths(this.scaleBottomStartIndex,this.scaleBottomEndIndex,this.scales[1])},e.prototype.getScaleWidths=function(e,t,n){for(var r=new Array,i=e;i<=t;i++)r[i]=this.layoutCalculator.getScaleItemInfo(i,n).size.width;return r},e.prototype.getOffsetTop=function(){return this.isVisibleMode&&this.hasTasks?this.getTaskCellOffsetTop(this.visibleTaskIndices[0]):0},Object.defineProperty(e.prototype,"renderedScaleLeft",{get:function(){return this.getCellLeft(this.scaleBottomStartIndex)},enumerable:!1,configurable:!0}),e.prototype.getTaskCellOffsetTop=function(e){var t=this.getCellTop(e);return Math.max(this.visibleTop-t,0)},e.prototype.getCellTop=function(e){return this.layoutCalculator.getGridBorderPosition(e-1,!1).y},e.prototype.getCellLeft=function(e){return this.layoutCalculator.getScaleItemInfo(e,this.scales[1]).position.x},e.prototype.getTaskIndices=function(){var e,t;return this.dataMode===l.DataExportMode.all||this.exportRange?this._owner.getAllVisibleTaskIndices(null===(e=this.exportRange)||void 0===e?void 0:e.startIndex,null===(t=this.exportRange)||void 0===t?void 0:t.endIndex):this.getVisibleTaskIndices()},e.prototype.getVisibleTaskIndices=function(){var e=this,t=[];return this.taskElements.forEach((function(n,i){if(n){var o=r.DomUtils.pxToInt(n.style.top),s=o+n.offsetHeight,a=o>=e.visibleTop&&o<=e.visibleBottom,l=s>=e.visibleTop&&s<=e.visibleBottom;(a||l)&&t.push(i)}})),t},Object.defineProperty(e.prototype,"scaleElements",{get:function(){return this._owner.renderHelper.scaleElements.slice()},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"scaleBorders",{get:function(){return this._owner.renderHelper.scaleBorders.slice()},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"hlRowElements",{get:function(){return this._owner.renderHelper.hlRowElements.filter((function(e){return!!e}))},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"selectionElements",{get:function(){return this._owner.renderHelper.selectionElements.filter((function(e){return!!e}))},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"taskElements",{get:function(){return this._owner.renderHelper.taskElements},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"connectorLines",{get:function(){var e,t=this;return null!==(e=this._connectorLines)&&void 0!==e||(this._connectorLines=this._owner.renderHelper.allConnectorLines.filter((function(e){return t.isLineVisible(e)}))),this._connectorLines},enumerable:!1,configurable:!0}),e.prototype.isLineVisible=function(e){if(this.dataMode===l.DataExportMode.all)return!0;var t=e.attr["dependency-id"];return this.visibleDependencyKeys.indexOf(t)>-1},Object.defineProperty(e.prototype,"visibleDependencyKeys",{get:function(){var e;return null!==(e=this._visibleDependencyKeys)&&void 0!==e||(this._visibleDependencyKeys=this._owner.getVisibleDependencyKeysByTaskRange(this.visibleTaskIndices)),this._visibleDependencyKeys},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"resourcesElements",{get:function(){var e,t=this;return null!==(e=this._resourcesElements)&&void 0!==e||(this._resourcesElements=this.visibleTaskIndices.map((function(e){return t._owner.renderHelper.resourcesElements[e]})).filter((function(e){return t.isElementVisible(e)&&e.parentElement}))),this._resourcesElements},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"stripLinesElements",{get:function(){if(!this._stripLinesElements){var e=this._owner.renderHelper.stripLinesMap.filter((function(e){return!!e})).map((function(e){return e}));this._stripLinesElements=e.map((function(e){return e}))}return this._stripLinesElements},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"noWorkingIntervalsElements",{get:function(){if(!this._noWorkingIntervalsElements){this._noWorkingIntervalsElements=[];var e=this._owner.renderHelper.noWorkingIntervalsToElementsMap;for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&this._noWorkingIntervalsElements.push(e[t])}return this._noWorkingIntervalsElements},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"taskArea",{get:function(){return this._owner.renderHelper.taskArea},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"settings",{get:function(){return this._owner.settings},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"dataMode",{get:function(){var e;return null===(e=this._props)||void 0===e?void 0:e.exportDataMode},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"exportRange",{get:function(){var e;return null===(e=this._props)||void 0===e?void 0:e.dateRange},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isVisibleMode",{get:function(){return this.dataMode===l.DataExportMode.visible&&!this.exportRange},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"ownerStartDate",{get:function(){return this._owner.range.start},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"ownerEndDate",{get:function(){return this._owner.range.end},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"startDate",{get:function(){var e,t,n,r;if((null===(e=this.exportRange)||void 0===e?void 0:e.startDate)&&(null===(t=this.exportRange)||void 0===t?void 0:t.endDate)){var i=Math.min(null===(n=this.exportRange)||void 0===n?void 0:n.startDate.getTime(),null===(r=this.exportRange)||void 0===r?void 0:r.endDate.getTime());return new Date(i)}return this.ownerStartDate},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"endDate",{get:function(){var e,t,n,r;if((null===(e=this.exportRange)||void 0===e?void 0:e.startDate)&&(null===(t=this.exportRange)||void 0===t?void 0:t.endDate)){var i=Math.max(null===(n=this.exportRange)||void 0===n?void 0:n.startDate.getTime(),null===(r=this.exportRange)||void 0===r?void 0:r.endDate.getTime());return new Date(i)}return this.ownerEndDate},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"hasCustomRangeOutOfRender",{get:function(){return this.startDate.getTime()!==this.ownerStartDate.getTime()||this.endDate.getTime()!==this.ownerEndDate.getTime()},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"layoutCalculator",{get:function(){if(!this._layoutCalculator){var e=this._owner.renderHelper.gridLayoutCalculator;this.hasCustomRangeOutOfRender?(this._layoutCalculator=new o.GridLayoutCalculator,this._layoutCalculator.setSettings(e.visibleTaskAreaSize,e.tickSize,e.elementSizeValues,new i.DateRange(this.startDate,this.endDate),e.viewModel,e.viewType,e.scrollBarHeight,this._owner.settings.firstDayOfWeek)):this._layoutCalculator=e}return this._layoutCalculator},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"container",{get:function(){return this._owner.renderHelper.taskAreaContainer},enumerable:!1,configurable:!0}),e.prototype.getPosByDate=function(e){return this.layoutCalculator.getPosByDate(e)},e.prototype.getScaleTableStyle=function(){var e={},t=this.scaleElements[0].filter((function(e){return!!e}))[0],n=this.getElementStyle(t);return e.backgroundColor=this.findElementBackColor(t),e.borderColor=this.getChartTableBorderColor(),e.verticalAlign="middle",e.textAlign="center",e.fontSize=n.fontSize,e.fontFamily=n.fontFamily,e.fontWeight=n.fontWeight,e.fontStyle=n.fontStyle,e.color=n.color,e},e.prototype.getChartMainTableStyle=function(){var e={};return e.backgroundColor=this.findElementBackColor(this.taskArea),e.borderColor=this.getChartTableBorderColor(),e},e.prototype.findElementBackColor=function(e){if(!e)return null;for(var t=e,n=new a.Color("transparent");0===n.opacity&&t;){var r=this.getElementStyle(t);n.assign(r.backgroundColor),t=t.parentElement}return n},e.prototype.getChartTableBorderColor=function(){var e=this.getElementStyle(this.scaleBorders[0].filter((function(e){return!!e}))[0]);return null==e?void 0:e.borderColor},e.prototype.getParentBackColor=function(){var e=this.getElementStyle(this.hlRowElements[0]);return null==e?void 0:e.backgroundColor},e.prototype.getArrowWidth=function(){var e=this.getDependencyLineStyle(o.GridLayoutCalculator.arrowClassName),t=e.borderWidth||e.borderLeftWidth||e.borderRightWidth||e.borderTopWidth||e.borderBottomWidth;return e&&r.DomUtils.pxToInt(t)},e.prototype.getDependencyColor=function(){var e=this.getDependencyLineStyle(o.GridLayoutCalculator.CLASSNAMES.CONNECTOR_HORIZONTAL);return null==e?void 0:e.borderColor},e.prototype.getDependencyLineStyle=function(e){return this.getElementStyle(this.taskArea.getElementsByClassName(e)[0])},e.prototype.getElementStyle=function(e){return e&&getComputedStyle(e)},e.prototype.getTaskWrapper=function(e){return this.isTaskTemplateMode?this._owner.renderHelper.fakeTaskWrapper:(this.taskElements[e]||this._owner.renderHelper.createDefaultTaskElement(e),this.taskElements[e])},Object.defineProperty(e.prototype,"isTaskTemplateMode",{get:function(){return!!this._owner.settings.taskContentTemplate},enumerable:!1,configurable:!0}),e}();t.TaskAreaExportHelper=c},6057:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.CollectionBase=void 0;var r=n(2491),i=n(2601),o=function(){function e(){this._items=new Array,this._isGanttCollection=!0}return e.prototype.add=function(e){if((0,r.isDefined)(e)){if(this.getItemById(e.internalId))throw"The collection item with id ='"+e.internalId+"' already exists.";this._addItem(e)}},e.prototype.addRange=function(e){for(var t=0;t<e.length;t++)this.add(e[t])},e.prototype.remove=function(e){var t=this._items.indexOf(e);t>-1&&t<this._items.length&&this._removeItems(t,1)},e.prototype.clear=function(){this._removeItems(0,this._items.length)},e.prototype.invalidate=function(){delete this._invertedItems},e.prototype._addItem=function(e){this._items.push(e),delete this._invertedItems},e.prototype._removeItems=function(e,t){this._items.splice(e,t),delete this._invertedItems},Object.defineProperty(e.prototype,"items",{get:function(){return this._items.slice()},set:function(e){e&&(this._items=e.slice())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"length",{get:function(){return this._items.length},enumerable:!1,configurable:!0}),e.prototype.getItem=function(e){return e>-1&&e<this._items.length?this._items[e]:null},Object.defineProperty(e.prototype,"invertedItems",{get:function(){var e;return null!==(e=this._invertedItems)&&void 0!==e||(this._invertedItems=this._createInvertedItems()),this._invertedItems},enumerable:!1,configurable:!0}),e.prototype._createInvertedItems=function(){for(var e={},t=0;t<this._items.length;t++){var n=this._items[t];e[n.internalId]=n}return e},e.prototype.getItemById=function(e){return this.invertedItems[e]},e.prototype.getItemByPublicId=function(e){return this._items.filter((function(t){return t.id===e||t.id.toString()===e}))[0]},e.prototype.assign=function(e){(0,r.isDefined)(e)&&(this.items=e.items)},e.prototype.importFromObject=function(e){(0,r.isDefined)(e)&&(this.clear(),e._isGanttCollection?this.assign(e):e instanceof Array?this.importFromArray(e):this.createItemFromObjectAndAdd(e))},e.prototype.createItemFromObjectAndAdd=function(e){if((0,r.isDefined)(e)&&Object.keys(e).length>0){var t=this.createItem();t.assignFromObject(e),this.add(t)}},e.prototype.importFromArray=function(e){for(var t=0;t<e.length;t++)this.createItemFromObjectAndAdd(e[t])},e.prototype.importFromJSON=function(e){this.importFromObject(i.GanttJsonUtils.parseJson(e))},e}();t.CollectionBase=o},7380:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DependencyCollection=void 0;var r=n(655),i=n(6057),o=n(7352),s=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r.__extends(t,e),t.prototype.createItem=function(){return new o.Dependency},t}(i.CollectionBase);t.DependencyCollection=s},4432:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.WorkingDayRuleCollection=void 0;var r=n(655),i=n(6057),o=n(8401),s=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r.__extends(t,e),t.prototype.createItem=function(){return new o.WorkingTimeRule},t}(i.CollectionBase);t.WorkingDayRuleCollection=s},9883:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ResourceAssignmentCollection=void 0;var r=n(655),i=n(6057),o=n(7437),s=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r.__extends(t,e),t.prototype.createItem=function(){return new o.ResourceAssignment},t}(i.CollectionBase);t.ResourceAssignmentCollection=s},8828:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ResourceCollection=void 0;var r=n(655),i=n(6057),o=n(2301),s=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r.__extends(t,e),t.prototype.createItem=function(){return new o.Resource},t}(i.CollectionBase);t.ResourceCollection=s},9504:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TaskCollection=void 0;var r=n(655),i=n(8492),o=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r.__extends(t,e),t.prototype.createItem=function(){return new i.Task},t}(n(6057).CollectionBase);t.TaskCollection=o},5594:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.EventDispatcher=void 0;var n=function(){function e(){this.listeners=[]}return e.prototype.add=function(e){if(!e)throw new Error("Error");this.hasEventListener(e)||this.listeners.push(e)},e.prototype.remove=function(e){for(var t=0,n=void 0;n=this.listeners[t];t++)if(n===e){this.listeners.splice(t,1);break}},e.prototype.raise=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];for(var r=0,i=void 0;i=this.listeners[r];r++){var o=i[e];null==o||o.apply(i,t)}},e.prototype.hasEventListener=function(e){for(var t=0,n=this.listeners.length;t<n;t++)if(this.listeners[t]===e)return!0;return!1},e}();t.EventDispatcher=n},3452:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ModelChangesDispatcher=void 0;var r=n(255),i=n(8403),o=n(8738),s=n(9669),a=n(5594),l=function(){function e(){this.onModelChanged=new a.EventDispatcher,this.isLocked=!1}return e.prototype.notifyTaskCreating=function(e){this.isLocked||this.onModelChanged.raise("NotifyTaskCreating",e)},e.prototype.notifyTaskCreated=function(e,t,n){this.isLocked||this.onModelChanged.raise("NotifyTaskCreated",e,t,n)},e.prototype.notifyTaskRemoving=function(e){this.isLocked||this.onModelChanged.raise("NotifyTaskRemoving",e)},e.prototype.notifyTaskRemoved=function(e,t,n){this.isLocked||this.onModelChanged.raise("NotifyTaskRemoved",e,t,n)},e.prototype.notifyTaskUpdating=function(e){this.isLocked||this.onModelChanged.raise("NotifyTaskUpdating",e)},e.prototype.notifyTaskMoving=function(e){this.isLocked||this.onModelChanged.raise("NotifyTaskMoving",e)},e.prototype.notifyTaskEditDialogShowing=function(e){this.isLocked||this.onModelChanged.raise("NotifyTaskEditDialogShowing",e)},e.prototype.notifyResourceManagerDialogShowing=function(e){this.isLocked||this.onModelChanged.raise("NotifyResourceManagerDialogShowing",e)},e.prototype.notifyTaskUpdated=function(e,t,n){this.isLocked||this.onModelChanged.raise("NotifyTaskUpdated",e,t,n)},e.prototype.notifyParentTaskUpdated=function(e,t){this.isLocked||this.onModelChanged.raise("NotifyParentTaskUpdated",e,t)},e.prototype.notifyDependencyInserting=function(e){this.isLocked||this.onModelChanged.raise("NotifyDependencyInserting",e)},e.prototype.notifyDependencyInserted=function(e,t,n){this.isLocked||this.onModelChanged.raise("NotifyDependencyInserted",e,t,n)},e.prototype.notifyDependencyRemoving=function(e){this.isLocked||this.onModelChanged.raise("NotifyDependencyRemoving",e)},e.prototype.notifyDependencyRemoved=function(e,t,n){this.isLocked||this.onModelChanged.raise("NotifyDependencyRemoved",e,t,n)},e.prototype.notifyResourceCreating=function(e){this.isLocked||this.onModelChanged.raise("NotifyResourceCreating",e)},e.prototype.notifyResourceCreated=function(e,t,n){this.isLocked||this.onModelChanged.raise("NotifyResourceCreated",e,t,n)},e.prototype.notifyResourceRemoving=function(e){this.isLocked||this.onModelChanged.raise("NotifyResourceRemoving",e)},e.prototype.notifyResourceRemoved=function(e,t,n){this.isLocked||this.onModelChanged.raise("NotifyResourceRemoved",e,t,n)},e.prototype.notifyResourceColorChanged=function(e,t,n){this.isLocked||this.onModelChanged.raise("NotifyResourceColorChanged",e,t,n)},e.prototype.notifyResourceAssigning=function(e){this.isLocked||this.onModelChanged.raise("NotifyResourceAssigning",e)},e.prototype.notifyResourceAssigned=function(e,t,n){this.isLocked||this.onModelChanged.raise("NotifyResourceAssigned",e,t,n)},e.prototype.notifyResourceUnassigning=function(e){this.isLocked||this.onModelChanged.raise("NotifyResourceUnassigning",e)},e.prototype.notifyResourceUnassigned=function(e,t,n){this.isLocked||this.onModelChanged.raise("NotifyResourceUnassigned",e,t,n)},e.prototype.notifyParentDataRecalculated=function(e){this.onModelChanged.raise("NotifyParentDataRecalculated",e)},e.prototype.notifyScaleCellPrepared=function(e){this.onModelChanged.raise("NotifyScaleCellPrepared",e)},e.prototype.notifyGanttViewUpdated=function(){this.onModelChanged.raise("NotifyGanttViewUpdated")},e.prototype.fireResourceUnassigning=function(e){var t=new o.ResourceUnassigningArguments(e);return this.notifyResourceUnassigning(t),!t.cancel},e.prototype.raiseTaskUpdating=function(e,t,n){var r=new s.TaskUpdatingArguments(e,t);return this.notifyTaskUpdating(r),!r.cancel&&(n(r.newValues),!0)},e.prototype.raiseTaskMoving=function(e,t,n,r){var i=new s.TaskUpdatingArguments(e,{start:t,end:n});return this.notifyTaskMoving(i),!i.cancel&&(r(i.start,i.end),!0)},e.prototype.raiseTaskTaskEditDialogShowing=function(e,t){var n=new i.TaskEditDialogShowingArguments(e);return this.notifyTaskEditDialogShowing(n),!n.cancel&&(t(n),!0)},e.prototype.raiseResourceManagerDialogShowing=function(e,t){var n=new r.ResourceManagerDialogShowingArguments(e);return this.notifyResourceManagerDialogShowing(n),!n.cancel&&(t(n),!0)},e.prototype.lock=function(){this.isLocked=!0},e.prototype.unlock=function(){this.isLocked=!1},e}();t.ModelChangesDispatcher=l},6124:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.GanttDataObjectNames=t.DataObject=void 0;var r=n(2491),i=n(8679),o=function(){function e(){this.internalId=i.MathUtils.generateGuid()}return e.prototype.assignFromObject=function(e){(0,r.isDefined)(e)&&(0,r.isDefined)(e.id)&&this.updateId(e.id)},e.prototype.updateId=function(e){this.id=e,this.internalId=String(e)},e}();t.DataObject=o,t.GanttDataObjectNames={task:"task",dependency:"dependency",resource:"resource",resourceAssignment:"assignment"}},7352:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Dependency=void 0;var r=n(655),i=n(2491),o=n(6124),s=n(5950),a=function(e){function t(){var t=e.call(this)||this;return t.predecessorId="",t.successorId="",t.type=null,t}return r.__extends(t,e),Object.defineProperty(t.prototype,"isStartDependency",{get:function(){return this.type===s.DependencyType.SS||this.type===s.DependencyType.SF},enumerable:!1,configurable:!0}),t.prototype.assignFromObject=function(t){(0,i.isDefined)(t)&&(e.prototype.assignFromObject.call(this,t),this.predecessorId=String(t.predecessorId),this.successorId=String(t.successorId),this.type=this.parseType(t.type))},t.prototype.parseType=function(e){if(!(0,i.isDefined)(e))return s.DependencyType.FS;switch(e.toString().toUpperCase()){case"SS":case"1":return s.DependencyType.SS;case"FF":case"2":return s.DependencyType.FF;case"SF":case"3":return s.DependencyType.SF;default:return s.DependencyType.FS}},t}(o.DataObject);t.Dependency=a},5950:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DependencyType=t.TaskType=void 0,function(e){e[e.Regular=0]="Regular",e[e.Summary=1]="Summary",e[e.Milestone=2]="Milestone"}(t.TaskType||(t.TaskType={})),function(e){e[e.FS=0]="FS",e[e.SS=1]="SS",e[e.FF=2]="FF",e[e.SF=3]="SF"}(t.DependencyType||(t.DependencyType={}))},2301:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Resource=void 0;var r=n(655),i=n(2491),o=function(e){function t(){var t=e.call(this)||this;return t.text="",t.color="",t}return r.__extends(t,e),t.prototype.assignFromObject=function(t){(0,i.isDefined)(t)&&(e.prototype.assignFromObject.call(this,t),this.text=t.text,(0,i.isDefined)(t.color)&&(this.color=t.color))},t}(n(6124).DataObject);t.Resource=o},7437:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ResourceAssignment=void 0;var r=n(655),i=n(2491),o=function(e){function t(){var t=e.call(this)||this;return t.taskId="",t.resourceId="",t}return r.__extends(t,e),t.prototype.assignFromObject=function(t){(0,i.isDefined)(t)&&(e.prototype.assignFromObject.call(this,t),this.taskId=String(t.taskId),this.resourceId=String(t.resourceId))},t}(n(6124).DataObject);t.ResourceAssignment=o},8492:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Task=void 0;var r=n(655),i=n(2491),o=n(6124),s=-8e15,a=function(e){function t(){var t=e.call(this)||this;return t.start=null,t.end=null,t.duration=null,t.description="",t.parentId=null,t.title="",t.owner=null,t.progress=0,t.taskType=null,t.customFields={},t.expanded=!0,t.color="",t}return r.__extends(t,e),Object.defineProperty(t.prototype,"normalizedProgress",{get:function(){return Math.max(Math.min(this.progress,100),0)},enumerable:!1,configurable:!0}),t.prototype.assignFromObject=function(t){(0,i.isDefined)(t)&&(e.prototype.assignFromObject.call(this,t),this.owner=t.owner,this.parentId=(0,i.isDefined)(t.parentId)?String(t.parentId):null,this.rawParentId=t.parentId,this.description=t.description,this.title=t.title,this.start="string"==typeof t.start?new Date(t.start):t.start||this.createInvalidDate(),this.end="string"==typeof t.end?new Date(t.end):t.end||this.createInvalidDate(),this.duration=t.duration,this.progress=t.progress,this.taskType=t.taskType,(0,i.isDefined)(t.expanded)&&(this.expanded=!!t.expanded),(0,i.isDefined)(t.color)&&(this.color=t.color),this.assignCustomFields(t.customFields))},t.prototype.assignCustomFields=function(e){if(e)for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&(this.customFields[t]=e[t])},t.prototype.isMilestone=function(){return this.start.getTime()===this.end.getTime()},t.prototype.getDuration=function(){return this.end.getTime()-this.start.getTime()},t.prototype.isValidStart=function(){return this.isValidTaskaDte(this.start)},t.prototype.isValidEnd=function(){return this.isValidTaskaDte(this.end)},t.prototype.isValid=function(){return this.isValidStart()&&this.isValidEnd()},t.prototype.createInvalidDate=function(){return new Date(s)},t.prototype.isValidTaskaDte=function(e){return!!e&&e.getTime()!==s},t}(o.DataObject);t.Task=a},8774:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.BaseArguments=void 0;var n=function(e){this.cancel=!1,this.values={},this.key=e};t.BaseArguments=n},3279:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DependencyInsertingArguments=void 0;var r=n(655),i=function(e){function t(t,n,r){var i=e.call(this,null)||this;return i.values={predecessorId:t,successorId:n,type:r},i}return r.__extends(t,e),Object.defineProperty(t.prototype,"predecessorId",{get:function(){return this.values.predecessorId},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"successorId",{get:function(){return this.values.successorId},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"type",{get:function(){return this.values.type},enumerable:!1,configurable:!0}),t}(n(8774).BaseArguments);t.DependencyInsertingArguments=i},4797:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DependencyRemovingArguments=void 0;var r=n(655),i=function(e){function t(t){var n=e.call(this,t.id)||this;return n.values=t,n}return r.__extends(t,e),t}(n(8774).BaseArguments);t.DependencyRemovingArguments=i},255:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ResourceManagerDialogShowingArguments=void 0;var r=n(655),i=function(e){function t(t){var n=e.call(this,void 0)||this;return n.values.resources=t.resources,n}return r.__extends(t,e),t}(n(8774).BaseArguments);t.ResourceManagerDialogShowingArguments=i},8403:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TaskEditDialogShowingArguments=void 0;var r=n(655),i=function(e){function t(t){var n=e.call(this,t.id)||this;return n.values={start:t.start,end:t.end,title:t.title,progress:t.progress},n.hiddenFields=t.hiddenFields,n.readOnlyFields=t.readOnlyFields,n}return r.__extends(t,e),t}(n(8774).BaseArguments);t.TaskEditDialogShowingArguments=i},1389:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ResourceAssigningArguments=void 0;var r=n(655),i=function(e){function t(t,n){var r=e.call(this,null)||this;return r.values={resourceId:t,taskId:n},r}return r.__extends(t,e),Object.defineProperty(t.prototype,"resourceId",{get:function(){return this.values.resourceId},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"taskId",{get:function(){return this.values.taskId},enumerable:!1,configurable:!0}),t}(n(8774).BaseArguments);t.ResourceAssigningArguments=i},8738:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ResourceUnassigningArguments=void 0;var r=n(655),i=function(e){function t(t){var n=e.call(this,t.internalId)||this;return n.values=t,n}return r.__extends(t,e),t}(n(8774).BaseArguments);t.ResourceUnassigningArguments=i},990:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ResourceInsertingArguments=void 0;var r=n(655),i=function(e){function t(t,n){void 0===n&&(n="");var r=e.call(this,null)||this;return r.values={text:t,color:n},r}return r.__extends(t,e),Object.defineProperty(t.prototype,"text",{get:function(){return this.values.text},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"color",{get:function(){return this.values.color},enumerable:!1,configurable:!0}),t}(n(8774).BaseArguments);t.ResourceInsertingArguments=i},9748:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ResourceRemovingArguments=void 0;var r=n(655),i=function(e){function t(t){var n=e.call(this,t.id)||this;return n.values=t,n}return r.__extends(t,e),t}(n(8774).BaseArguments);t.ResourceRemovingArguments=i},4605:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TaskInsertingArguments=void 0;var r=n(655),i=function(e){function t(t,n){var r=e.call(this,t)||this;return r.values=null!=n?n:{},r}return r.__extends(t,e),Object.defineProperty(t.prototype,"start",{get:function(){return this.values.start},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"end",{get:function(){return this.values.end},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"title",{get:function(){return this.values.title},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"progress",{get:function(){return this.values.progress},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"parentId",{get:function(){return this.values.parentId},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"color",{get:function(){return this.values.color},enumerable:!1,configurable:!0}),t}(n(8774).BaseArguments);t.TaskInsertingArguments=i},4642:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TaskRemovingArguments=void 0;var r=n(655),i=function(e){function t(t){var n=e.call(this,t.id)||this;return n.values=t,n}return r.__extends(t,e),t}(n(8774).BaseArguments);t.TaskRemovingArguments=i},9669:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TaskUpdatingArguments=void 0;var r=n(655),i=function(e){function t(t,n){var r=e.call(this,t.id)||this;return r.values=t,r.createNewValues(n),r}return r.__extends(t,e),t.prototype.createNewValues=function(e){var t=this;this.newValues={};var n=function(n){Object.prototype.hasOwnProperty.call(e,n)&&(r.newValues[n]=e[n],Object.defineProperty(r,n,{get:function(){return t.newValues[n]}}))},r=this;for(var i in e)n(i)},t}(n(8774).BaseArguments);t.TaskUpdatingArguments=i},8725:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.History=void 0;var r=n(5221),i=n(9751),o=function(){function e(e){this.historyItems=[],this.currentIndex=-1,this.currentProcessingItemInfo=null,this.transaction=null,this.transactionLevel=-1,this._listener=e}return e.prototype.undo=function(){this.canUndo()&&(this.activateItem(this.historyItems[this.currentIndex],!0),this.currentIndex--)},e.prototype.redo=function(){this.canRedo()&&(this.currentIndex++,this.activateItem(this.historyItems[this.currentIndex]))},e.prototype.beginTransaction=function(){var e;this.transactionLevel++,0==this.transactionLevel&&(this.transaction=new i.CompositionHistoryItem,null===(e=this._listener)||void 0===e||e.onTransactionStart())},e.prototype.endTransaction=function(){var e;if(!(--this.transactionLevel>=0)){var t=this.transaction.historyItems.length;t>1?this.addInternal(this.transaction):1==t&&this.addInternal(this.transaction.historyItems.pop()),this.transaction=null,null===(e=this._listener)||void 0===e||e.onTransactionEnd()}},e.prototype.addAndRedo=function(e){this.add(e),this.activateItem(e)},e.prototype.add=function(e){this.transactionLevel>=0?this.transaction.add(e):this.addInternal(e)},e.prototype.canUndo=function(){return this.currentIndex>=0},e.prototype.canRedo=function(){return this.currentIndex<this.historyItems.length-1},e.prototype.addInternal=function(e){this.currentIndex<this.historyItems.length-1&&this.historyItems.splice(this.currentIndex+1),this.historyItems.push(e),this.currentIndex++,this.deleteOldItems()},e.prototype.deleteOldItems=function(){var t=this.historyItems.length-e.MAX_HISTORY_ITEM_COUNT;t>0&&this.currentIndex>t&&(this.historyItems.splice(0,t),this.currentIndex-=t)},e.prototype.clear=function(){this.currentIndex=-1,this.historyItems=[]},e.prototype.activateItem=function(e,t){void 0===t&&(t=!1),this.currentProcessingItemInfo=new r.HistoryItemInfo(e,t),t?e.undo():e.redo(),this.currentProcessingItemInfo=null},e.prototype.getCurrentProcessingItemInfo=function(){return this.currentProcessingItemInfo},e.prototype.rollBackAndRemove=function(e){var t=e.item;this.checkAndRemoveItem(t)&&(e.isUndo?t.redo():t instanceof i.CompositionHistoryItem?t.undoItemsQuery():t.undo())},e.prototype.checkAndRemoveItem=function(e){var t=this.historyItems.indexOf(e);return t>-1?(this.historyItems.splice(t,1),this.currentIndex--):this.transaction&&(t=this.transaction.historyItems.indexOf(e))>-1&&this.transaction.historyItems.splice(t,1),t>-1},e.prototype.updateObsoleteInsertedKey=function(e,t,n){this.transaction&&this.updateItemsObsoleteInsertedKey(e,t,n,[this.transaction]),this.updateItemsObsoleteInsertedKey(e,t,n,this.historyItems)},e.prototype.updateItemsObsoleteInsertedKey=function(e,t,n,r){if(r)for(var o=0;o<r.length;o++){var s=r[o];s.keyUpdaters.filter((function(t){return t.getKey()===e&&t.objectType===n})).forEach((function(e){return e.updateKey(t)})),s instanceof i.CompositionHistoryItem&&this.updateItemsObsoleteInsertedKey(e,t,n,s.historyItems)}},e.MAX_HISTORY_ITEM_COUNT=100,e}();t.History=o},9751:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.CompositionHistoryItem=void 0;var r=n(655),i=function(e){function t(){var t=e.call(this,null)||this;return t.historyItems=[],t}return r.__extends(t,e),t.prototype.redo=function(){for(var e,t=0;e=this.historyItems[t];t++)e.redo()},t.prototype.undo=function(){for(var e,t=this.historyItems.length-1;e=this.historyItems[t];t--)e.undo()},t.prototype.add=function(e){if(null==e)throw new Error("Can't add null HistoryItem");this.historyItems.push(e)},t.prototype.undoItemsQuery=function(){this.undo()},t.prototype.setModelManipulator=function(t){if(e.prototype.setModelManipulator.call(this,t),this.historyItems)for(var n=0;n<this.historyItems.length;n++)this.historyItems[n].setModelManipulator(t)},t}(n(7917).HistoryItem);t.CompositionHistoryItem=i},1211:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.InsertDependencyHistoryItem=void 0;var r=n(655),i=n(8679),o=n(6124),s=function(e){function t(t,n,r,i){var o=e.call(this,t)||this;return o.predecessorId=n,o.successorId=r,o.type=i,o}return r.__extends(t,e),t.prototype.redo=function(){var e;null!==(e=this.insertedKey)&&void 0!==e||(this.insertedKey=i.MathUtils.generateGuid()),this.modelManipulator.dependency.insertDependency(this.predecessorId,this.successorId,this.type,this.insertedKey)},t.prototype.undo=function(){this.modelManipulator.dependency.removeDependency(this.insertedKey)},Object.defineProperty(t.prototype,"keyUpdaters",{get:function(){var e=this;return[{objectType:o.GanttDataObjectNames.dependency,getKey:function(){return e.insertedKey},updateKey:function(t){return e.insertedKey=t}},{objectType:o.GanttDataObjectNames.task,getKey:function(){return e.predecessorId},updateKey:function(t){return e.predecessorId=t}},{objectType:o.GanttDataObjectNames.task,getKey:function(){return e.successorId},updateKey:function(t){return e.successorId=t}}]},enumerable:!1,configurable:!0}),t}(n(7917).HistoryItem);t.InsertDependencyHistoryItem=s},5865:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.RemoveDependencyHistoryItem=void 0;var r=n(655),i=n(6124),o=function(e){function t(t,n){var r=e.call(this,t)||this;return r.dependencyId=n,r}return r.__extends(t,e),t.prototype.redo=function(){this.dependency=this.modelManipulator.dependency.removeDependency(this.dependencyId)},t.prototype.undo=function(){this.modelManipulator.dependency.insertDependency(this.dependency.predecessorId,this.dependency.successorId,this.dependency.type,this.dependencyId)},Object.defineProperty(t.prototype,"keyUpdaters",{get:function(){var e=this;return[{objectType:i.GanttDataObjectNames.dependency,getKey:function(){return e.dependencyId},updateKey:function(t){return e.dependencyId=t}},{objectType:i.GanttDataObjectNames.task,getKey:function(){var t;return null===(t=e.dependency)||void 0===t?void 0:t.predecessorId},updateKey:function(t){return e.dependency.predecessorId=t}},{objectType:i.GanttDataObjectNames.task,getKey:function(){var t;return null===(t=e.dependency)||void 0===t?void 0:t.successorId},updateKey:function(t){return e.dependency.successorId=t}}]},enumerable:!1,configurable:!0}),t}(n(7917).HistoryItem);t.RemoveDependencyHistoryItem=o},7917:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.HistoryItem=void 0;var n=function(){function e(e){this.setModelManipulator(e)}return e.prototype.setModelManipulator=function(e){this.modelManipulator=e},Object.defineProperty(e.prototype,"keyUpdaters",{get:function(){return[]},enumerable:!1,configurable:!0}),e}();t.HistoryItem=n},5221:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.HistoryItemInfo=void 0;var n=function(e,t){void 0===t&&(t=!1),this.item=e,this.isUndo=t};t.HistoryItemInfo=n},3064:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.HistoryItemState=void 0;var n=function(e,t){this.id=e,this.value=t};t.HistoryItemState=n},3683:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.AssignResourceHistoryItem=void 0;var r=n(655),i=n(7917),o=n(6124),s=n(8679),a=function(e){function t(t,n,r){var i=e.call(this,t)||this;return i.resourceId=n,i.taskId=r,i}return r.__extends(t,e),t.prototype.redo=function(){var e;null!==(e=this.insertedKey)&&void 0!==e||(this.insertedKey=s.MathUtils.generateGuid()),this.modelManipulator.resource.assign(this.resourceId,this.taskId,this.insertedKey)},t.prototype.undo=function(){this.modelManipulator.resource.deassig(this.insertedKey)},Object.defineProperty(t.prototype,"keyUpdaters",{get:function(){var e=this;return[{objectType:o.GanttDataObjectNames.resourceAssignment,getKey:function(){return e.insertedKey},updateKey:function(t){return e.insertedKey=t}},{objectType:o.GanttDataObjectNames.task,getKey:function(){return e.taskId},updateKey:function(t){return e.taskId=t}},{objectType:o.GanttDataObjectNames.resource,getKey:function(){return e.resourceId},updateKey:function(t){return e.resourceId=t}}]},enumerable:!1,configurable:!0}),t}(i.HistoryItem);t.AssignResourceHistoryItem=a},1493:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DeassignResourceHistoryItem=void 0;var r=n(655),i=n(7917),o=n(6124),s=function(e){function t(t,n){var r=e.call(this,t)||this;return r.assignmentId=n,r}return r.__extends(t,e),t.prototype.redo=function(){this.assignment=this.modelManipulator.resource.deassig(this.assignmentId)},t.prototype.undo=function(){this.modelManipulator.resource.assign(this.assignment.resourceId,this.assignment.taskId,this.assignmentId)},Object.defineProperty(t.prototype,"keyUpdaters",{get:function(){var e=this;return[{objectType:o.GanttDataObjectNames.resourceAssignment,getKey:function(){return e.assignmentId},updateKey:function(t){return e.assignmentId=t}},{objectType:o.GanttDataObjectNames.task,getKey:function(){var t;return null===(t=e.assignment)||void 0===t?void 0:t.taskId},updateKey:function(t){return e.assignment.taskId=t}},{objectType:o.GanttDataObjectNames.resource,getKey:function(){var t;return null===(t=e.assignment)||void 0===t?void 0:t.resourceId},updateKey:function(t){return e.assignment.resourceId=t}}]},enumerable:!1,configurable:!0}),t}(i.HistoryItem);t.DeassignResourceHistoryItem=s},2961:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.CreateResourceHistoryItem=void 0;var r=n(655),i=n(7917),o=n(6124),s=n(8679),a=function(e){function t(t,n,r,i){void 0===r&&(r="");var o=e.call(this,t)||this;return o.text=n,o.color=r,o.createCallback=i,o}return r.__extends(t,e),t.prototype.redo=function(){var e;null!==(e=this.insertedKey)&&void 0!==e||(this.insertedKey=s.MathUtils.generateGuid()),this.modelManipulator.resource.create(this.text,this.color,this.insertedKey,this.createCallback)},t.prototype.undo=function(){this.modelManipulator.resource.remove(this.insertedKey)},Object.defineProperty(t.prototype,"keyUpdaters",{get:function(){var e=this;return[{objectType:o.GanttDataObjectNames.resource,getKey:function(){return e.insertedKey},updateKey:function(t){return e.insertedKey=t}}]},enumerable:!1,configurable:!0}),t}(i.HistoryItem);t.CreateResourceHistoryItem=a},4641:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ResourceColorHistoryItem=void 0;var r=n(655),i=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r.__extends(t,e),t.prototype.getPropertiesManipulator=function(){return this.modelManipulator.resource.properties.color},t}(n(7049).ResourcePropertiesHistoryItemBase);t.ResourceColorHistoryItem=i},7049:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ResourcePropertiesHistoryItemBase=void 0;var r=n(655),i=n(6124),o=function(e){function t(t,n,r){var i=e.call(this,t)||this;return i.resourceId=n,i.newValue=r,i}return r.__extends(t,e),t.prototype.redo=function(){this.oldState=this.getPropertiesManipulator().setValue(this.resourceId,this.newValue)},t.prototype.undo=function(){this.getPropertiesManipulator().restoreValue(this.oldState)},t.prototype.getPropertiesManipulator=function(){throw new Error("Not Implemented")},Object.defineProperty(t.prototype,"keyUpdaters",{get:function(){var e=this;return[{objectType:i.GanttDataObjectNames.resource,getKey:function(){return e.resourceId},updateKey:function(t){return e.resourceId=t}}]},enumerable:!1,configurable:!0}),t}(n(7917).HistoryItem);t.ResourcePropertiesHistoryItemBase=o},7466:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.RemoveResourceHistoryItem=void 0;var r=n(655),i=n(9751),o=n(6124),s=function(e){function t(t,n){var r=e.call(this)||this;return r.modelManipulator=t,r.resourceId=n,r}return r.__extends(t,e),t.prototype.redo=function(){e.prototype.redo.call(this),this.resource=this.modelManipulator.resource.remove(this.resourceId)},t.prototype.undo=function(){var t=this;this.modelManipulator.resource.create(this.resource.text,this.resource.color,this.resourceId,(function(){t.resource.color&&t.modelManipulator.resource.properties.color.setValue(t.resource.internalId,t.resource.color),window.setTimeout((function(){return e.prototype.undo.call(t)}),0)}))},t.prototype.undoItemsQuery=function(){this.modelManipulator.resource.create(this.resource.text,this.resource.color,this.resourceId,(function(){})),this.resource.color&&this.modelManipulator.resource.properties.color.setValue(this.resource.internalId,this.resource.color),e.prototype.undo.call(this)},Object.defineProperty(t.prototype,"keyUpdaters",{get:function(){var e=this;return[{objectType:o.GanttDataObjectNames.resource,getKey:function(){return e.resourceId},updateKey:function(t){return e.resourceId=t}}]},enumerable:!1,configurable:!0}),t}(i.CompositionHistoryItem);t.RemoveResourceHistoryItem=s},1284:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.CreateTaskHistoryItem=void 0;var r=n(655),i=n(8679),o=n(6124),s=function(e){function t(t,n){var r=e.call(this,t)||this;return r.data=n,r}return r.__extends(t,e),t.prototype.redo=function(){var e;null!==(e=this.insertedKey)&&void 0!==e||(this.insertedKey=i.MathUtils.generateGuid()),this.modelManipulator.task.create(this.data,this.insertedKey)},t.prototype.undo=function(){this.modelManipulator.task.remove(this.insertedKey)},Object.defineProperty(t.prototype,"keyUpdaters",{get:function(){var e,t=this,n=[{objectType:o.GanttDataObjectNames.task,getKey:function(){return t.insertedKey},updateKey:function(e){return t.insertedKey=e}}];return(null===(e=this.data)||void 0===e?void 0:e.parentId)&&n.push({objectType:o.GanttDataObjectNames.task,getKey:function(){var e;return null===(e=t.data)||void 0===e?void 0:e.parentId},updateKey:function(e){return t.data.parentId=e}}),n},enumerable:!1,configurable:!0}),t}(n(7917).HistoryItem);t.CreateTaskHistoryItem=s},9599:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.RemoveTaskHistoryItem=void 0;var r=n(655),i=n(6124),o=n(9751),s=function(e){function t(t,n){var r=e.call(this)||this;return r.modelManipulator=t,r.taskId=n,r}return r.__extends(t,e),t.prototype.redo=function(){e.prototype.redo.call(this),this.task=this.modelManipulator.task.remove(this.taskId)},t.prototype.undo=function(){var t=this;this.modelManipulator.task.create(this.task,this.taskId,(function(){window.setTimeout((function(){return e.prototype.undo.call(t)}),0)}))},t.prototype.undoItemsQuery=function(){var e;this.modelManipulator.task.create(this.task,this.taskId);for(var t=this.historyItems.length-1;e=this.historyItems[t];t--)e instanceof o.CompositionHistoryItem?e.undoItemsQuery():e.undo()},Object.defineProperty(t.prototype,"keyUpdaters",{get:function(){var e=this;return[{objectType:i.GanttDataObjectNames.task,getKey:function(){return e.taskId},updateKey:function(t){return e.taskId=t}},{objectType:i.GanttDataObjectNames.task,getKey:function(){var t;return null===(t=e.task)||void 0===t?void 0:t.parentId},updateKey:function(t){return e.task.parentId=t}}]},enumerable:!1,configurable:!0}),t}(o.CompositionHistoryItem);t.RemoveTaskHistoryItem=s},9496:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.UpdateTaskHistoryItem=void 0;var r=n(655),i=n(6124),o=n(7917),s=n(3064),a=function(e){function t(t,n,r){var i=e.call(this,t)||this;return i.taskId=n,i.newValues=r,i}return r.__extends(t,e),t.prototype.redo=function(){var e=this.modelManipulator.task.update(this.taskId,this.newValues);this.oldState=new s.HistoryItemState(this.taskId,e)},t.prototype.undo=function(){this.modelManipulator.task.update(this.taskId,this.oldState.value)},Object.defineProperty(t.prototype,"keyUpdaters",{get:function(){var e=this;return[{objectType:i.GanttDataObjectNames.task,getKey:function(){return e.taskId},updateKey:function(t){return e.taskId=t}}]},enumerable:!1,configurable:!0}),t}(o.HistoryItem);t.UpdateTaskHistoryItem=a},6382:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.BaseManipulator=void 0;var n=function(){function e(e,t){this.viewModel=e,this.dispatcher=t}return e.prototype.getErrorCallback=function(){return this.viewModel.getDataUpdateErrorCallback()},Object.defineProperty(e.prototype,"renderHelper",{get:function(){return this.viewModel.owner.renderHelper},enumerable:!1,configurable:!0}),e}();t.BaseManipulator=n},1178:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TaskDependencyManipulator=void 0;var r=n(655),i=n(6124),o=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r.__extends(t,e),t.prototype.insertDependency=function(e,t,n,r){var o=this.viewModel;o.onBeginDataObjectCreate();var s=o.dependencies.createItem();s.predecessorId=e,s.successorId=t,s.type=n,r&&(s.internalId=r),s.id=s.internalId,o.dependencies.add(s);return o.updateVisibleItemDependencies(),this.renderHelper.recreateConnectorLineElement(s.internalId,!0),this.dispatcher.notifyDependencyInserted(this.getObjectForDataSource(s),(function(e){var t=s.internalId;s.updateId(e),o.processServerInsertedKey(t,s.internalId,i.GanttDataObjectNames.dependency)}),this.getErrorCallback()),o.onEndDataObjectCreate(),s},t.prototype.removeDependency=function(e){var t=this.viewModel.dependencies.getItemById(e);return this.viewModel.dependencies.remove(t),this.dispatcher.notifyDependencyRemoved(t.id,this.getErrorCallback(),this.viewModel.getDependencyObjectForDataSource(t)),this.viewModel.updateVisibleItemDependencies(),this.renderHelper.recreateConnectorLineElement(t.internalId),t},t.prototype.getObjectForDataSource=function(e){var t=this.viewModel.tasks.getItemById(e.predecessorId),n=this.viewModel.tasks.getItemById(e.successorId);return{id:e.id,predecessorId:t.id,successorId:n.id,type:e.type}},t}(n(6382).BaseManipulator);t.TaskDependencyManipulator=o},9650:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ModelManipulator=void 0;var r=n(1178),i=n(7518),o=n(728),s=function(e,t){this.task=new o.TaskManipulator(e,t),this.dependency=new r.TaskDependencyManipulator(e,t),this.resource=new i.ResourcesManipulator(e,t),this.dispatcher=t};t.ModelManipulator=s},4596:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ResourceColorManipulator=void 0;var r=n(655),i=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r.__extends(t,e),t.prototype.getPropertyValue=function(e){return e.color},t.prototype.setPropertyValue=function(e,t){e.color=t,this.dispatcher.notifyResourceColorChanged(e.id,t,this.getErrorCallback())},t}(n(7470).ResourcePropertyManipulator);t.ResourceColorManipulator=i},79:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ResourcePropertiesManipulator=void 0;var r=n(655),i=n(6382),o=n(4596),s=function(e){function t(t,n){var r=e.call(this,t,n)||this;return r.color=new o.ResourceColorManipulator(t,n),r}return r.__extends(t,e),t}(i.BaseManipulator);t.ResourcePropertiesManipulator=s},7470:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ResourcePropertyManipulator=void 0;var r=n(655),i=n(3064),o=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r.__extends(t,e),t.prototype.setValue=function(e,t){var n=this,r=this.viewModel.resources.getItemById(e),o=new i.HistoryItemState(e,this.getPropertyValue(r));return this.setPropertyValue(r,t),this.viewModel.assignments.items.filter((function(e){return e.resourceId===r.internalId})).forEach((function(e){var t=n.viewModel.findItem(e.taskId).visibleIndex;n.renderHelper.recreateTaskElement(t)})),o},t.prototype.restoreValue=function(e){var t=this;if(e){var n=e.value,r=this.viewModel.resources.getItemById(e.id);this.setPropertyValue(r,n),this.viewModel.assignments.items.filter((function(e){return e.resourceId===r.internalId})).forEach((function(e){var n=t.viewModel.findItem(e.taskId).visibleIndex;t.renderHelper.recreateTaskElement(n)}))}},t}(n(6382).BaseManipulator);t.ResourcePropertyManipulator=o},7518:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ResourcesManipulator=void 0;var r=n(655),i=n(6124),o=n(6382),s=n(79),a=function(e){function t(t,n){var r=e.call(this,t,n)||this;return r.properties=new s.ResourcePropertiesManipulator(t,n),r}return r.__extends(t,e),t.prototype.create=function(e,t,n,r){var o=this.viewModel;o.onBeginDataObjectCreate();var s=o.resources.createItem();return s.text=e,t&&(s.color=t),n&&(s.internalId=n),s.id=s.internalId,this.viewModel.resources.add(s),this.dispatcher.notifyResourceCreated(this.getResourceObjectForDataSource(s),(function(e){var t=s.internalId;s.updateId(e),o.processServerInsertedKey(t,s.internalId,i.GanttDataObjectNames.resource),r&&r(e)}),this.getErrorCallback()),o.onEndDataObjectCreate(),s},t.prototype.remove=function(e){var t=this.viewModel.resources.getItemById(e);if(!t)throw new Error("Invalid resource id");if(this.viewModel.assignments.items.filter((function(t){return t.resourceId===e})).length)throw new Error("Can't delete assigned resource");return this.viewModel.resources.remove(t),this.dispatcher.notifyResourceRemoved(t.id,this.getErrorCallback(),this.viewModel.getResourceObjectForDataSource(t)),t},t.prototype.assign=function(e,t,n){var r=this.viewModel;r.onBeginDataObjectCreate();var o=r.assignments.createItem();return o.resourceId=e,o.taskId=t,n&&(o.internalId=n),o.id=o.internalId,this.viewModel.assignments.add(o),this.dispatcher.notifyResourceAssigned(this.getResourceAssignmentObjectForDataSource(o),(function(e){var t=o.internalId;o.updateId(e),r.processServerInsertedKey(t,o.internalId,i.GanttDataObjectNames.resourceAssignment)}),this.getErrorCallback()),this.viewModel.updateModel(),r.onEndDataObjectCreate(),this.viewModel.owner.resetAndUpdate(),o},t.prototype.deassig=function(e){var t=this.viewModel.assignments.getItemById(e);return this.viewModel.assignments.remove(t),this.dispatcher.notifyResourceUnassigned(t.id,this.getErrorCallback(),this.viewModel.getResourceAssignmentObjectForDataSource(t)),this.viewModel.updateModel(),this.viewModel.owner.resetAndUpdate(),t},t.prototype.getResourceObjectForDataSource=function(e){return{id:e.id,text:e.text}},t.prototype.getResourceAssignmentObjectForDataSource=function(e){return{id:e.id,taskId:this.viewModel.tasks.getItemById(e.taskId).id,resourceId:this.viewModel.resources.getItemById(e.resourceId).id}},t}(o.BaseManipulator);t.ResourcesManipulator=a},728:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TaskManipulator=void 0;var r=n(655),i=n(2491),o=n(6124),s=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r.__extends(t,e),t.prototype.create=function(e,t,n){var r=this,i=this.viewModel;i.onBeginDataObjectCreate();var s=i.tasks.createItem();s.start=e.start,s.end=e.end,s.title=e.title,s.progress=e.progress,e.color&&(s.color=e.color);var a=i.tasks.getItemById(e.parentId);return a&&(a.expanded=!0),s.parentId=e.parentId,t&&(s.internalId=t),s.id=s.internalId,i.tasks.add(s),i.updateModel(),this.dispatcher.notifyTaskCreated(this.getObjectForDataSource(s),(function(e){var t=s.internalId;if(s.updateId(e),i.processServerInsertedKey(t,s.internalId,o.GanttDataObjectNames.task),n&&n(),r.viewModel.requireFirstLoadParentAutoCalc){var a=i.getCurrentTaskData().map((function(e){return""===e.parentId&&(e.parentId=null),e}));r.dispatcher.notifyParentDataRecalculated(a)}}),this.getErrorCallback()),i.onEndDataObjectCreate(),i.owner.resetAndUpdate(),s},t.prototype.remove=function(e){var t=this.viewModel.tasks.getItemById(e);if(!t)throw new Error("Invalid task id");if(this.viewModel.dependencies.items.filter((function(t){return t.predecessorId==e||t.successorId==e})).length)throw new Error("Can't delete task with dependency");if(this.viewModel.assignments.items.filter((function(t){return t.taskId==e})).length)throw new Error("Can't delete task with assigned resource");return this.viewModel.tasks.remove(t),this.dispatcher.notifyTaskRemoved(t.id,this.getErrorCallback(),this.viewModel.getTaskObjectForDataSource(t)),this.viewModel.updateModel(),this.viewModel.owner.resetAndUpdate(),t},t.prototype.update=function(e,t){var n=this.viewModel.tasks.getItemById(e),r={};Object.keys(t).forEach((function(e){(0,i.isDefined)(n[e])&&(r[e]=n[e],n[e]=t[e])}));var o=this.viewModel.findItem(e);return o&&this.renderHelper.recreateTaskElement(o.visibleIndex),this.dispatcher.notifyTaskUpdated(n.id,t,this.getErrorCallback()),r},t.prototype.getObjectForDataSource=function(e){return this.viewModel.getTaskObjectForDataSource(e)},t}(n(6382).BaseManipulator);t.TaskManipulator=s},2601:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.GanttJsonUtils=void 0;var r=n(9937),i=function(){function e(){}return e.parseJson=function(e){return r.JsonUtils.isValid(e)?JSON.parse(e):null},e}();t.GanttJsonUtils=i},8478:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ValidationController=void 0;var r=n(655),i=n(2491),o=n(9201),s=n(5950),a=n(9496),l=n(858),c=n(7880),u=n(4927),d=function(){function e(e){this.lockPredecessorToSuccessor=!0,this.settings=e}return Object.defineProperty(e.prototype,"viewModel",{get:function(){return this.settings.getViewModel()},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"history",{get:function(){return this.settings.getHistory()},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"modelManipulator",{get:function(){return this.settings.getModelManipulator()},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"range",{get:function(){return this.settings.getRange()},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"validationSettings",{get:function(){return this.settings.getValidationSettings()},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"_parentAutoCalc",{get:function(){return this.viewModel.parentAutoCalc},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"enablePredecessorGap",{get:function(){return this.viewModel.enablePredecessorGap},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isValidateDependenciesRequired",{get:function(){return this.settings.getIsValidateDependenciesRequired()},enumerable:!1,configurable:!0}),e.prototype.updateOwnerInAutoParentMode=function(){this.settings.updateOwnerInAutoParentMode()},e.prototype.checkStartDependencies=function(e,t){var n=this,r=[],i=this.viewModel.tasks.getItemById(e);return this.viewModel.dependencies.items.filter((function(t){return t.successorId===e})).forEach((function(e){var o=n.viewModel.tasks.getItemById(e.predecessorId);(e.type===s.DependencyType.FS&&o.end>t||e.type===s.DependencyType.SS&&o.start>t)&&r.push(new u.ValidationError(e.internalId,!0)),(e.type===s.DependencyType.FS&&o.end.valueOf()===i.start.valueOf()&&t>o.end||e.type===s.DependencyType.SS&&o.start.valueOf()===i.start.valueOf()&&t>o.start)&&r.push(new u.ValidationError(e.internalId))})),r},e.prototype.checkEndDependencies=function(e,t){var n=this,r=[],i=this.viewModel.tasks.getItemById(e);return this.viewModel.dependencies.items.filter((function(t){return t.successorId===e})).forEach((function(e){var o=n.viewModel.tasks.getItemById(e.predecessorId);(e.type===s.DependencyType.SF&&o.start>t||e.type===s.DependencyType.FF&&o.end>t)&&r.push(new u.ValidationError(e.internalId,!0)),(e.type===s.DependencyType.SF&&o.start.valueOf()===i.end.valueOf()&&t>o.start||e.type===s.DependencyType.FF&&o.end.valueOf()===i.end.valueOf()&&t>o.end)&&r.push(new u.ValidationError(e.internalId))})),r},e.prototype.moveEndDependTasks=function(e,t,n){var r=this;void 0===n&&(n=null);var i=this.viewModel.dependencies.items.filter((function(t){return t.predecessorId===e&&!t.isStartDependency})),o=this.viewModel.tasks.getItemById(e);i.forEach((function(e){var i=r.viewModel.tasks.getItemById(e.successorId);if(!(!i||n&&r.viewModel.checkParent(i.internalId,n)||i.parentId===o.id)){var c=new l.DateRange(new Date(i.start.getTime()),new Date(i.end.getTime())),u=new l.DateRange(new Date(i.start.getTime()),new Date(i.end.getTime())),d=o.end.getTime()-t.getTime(),p=r.enablePredecessorGap?o.end:t;if(e.type===s.DependencyType.FS&&(i.start<p||r.lockPredecessorToSuccessor&&i.start.getTime()===t.getTime())?(u.start.setTime(o.end.getTime()),u.end.setTime(u.start.getTime()+(i.end.getTime()-i.start.getTime())),r.correctMoving(i.internalId,u)):e.type===s.DependencyType.FF&&(i.end<p||r.lockPredecessorToSuccessor&&i.end.getTime()===t.getTime())?(u.start.setTime(o.end.getTime()-(i.end.getTime()-i.start.getTime())),u.end.setTime(o.end.getTime()),r.correctMoving(i.internalId,u)):r.enablePredecessorGap||(u.start.setTime(i.start.getTime()+d),u.end.setTime(i.end.getTime()+d)),!c.equal(u)){var h={start:u.start,end:u.end};r.history.addAndRedo(new a.UpdateTaskHistoryItem(r.modelManipulator,e.successorId,h)),r.moveRelatedTasks(e,c,i,u)}}}))},e.prototype.moveStartDependTasks=function(e,t,n){var r=this;void 0===n&&(n=null);var i=this.viewModel.dependencies.items.filter((function(t){return t.predecessorId===e&&t.isStartDependency})),o=this.viewModel.tasks.getItemById(e);i.forEach((function(e){var i=r.viewModel.tasks.getItemById(e.successorId);if(!(!i||n&&r.viewModel.checkParent(i.internalId,n)||i.parentId===o.id)){var c=new l.DateRange(new Date(i.start.getTime()),new Date(i.end.getTime())),u=new l.DateRange(new Date(i.start.getTime()),new Date(i.end.getTime())),d=o.start.getTime()-t.getTime(),p=r.enablePredecessorGap?o.start:t;if(e.type===s.DependencyType.SF&&(i.end<p||r.lockPredecessorToSuccessor&&i.end.getTime()===t.getTime())?(u.start.setTime(o.start.getTime()-(i.end.getTime()-i.start.getTime())),u.end.setTime(o.start.getTime()),r.correctMoving(i.internalId,u)):e.type===s.DependencyType.SS&&(i.start<p||r.lockPredecessorToSuccessor&&i.start.getTime()===t.getTime())?(u.start.setTime(o.start.getTime()),u.end.setTime(o.start.getTime()+(i.end.getTime()-i.start.getTime())),r.correctMoving(i.internalId,u)):r.enablePredecessorGap||(u.start.setTime(i.start.getTime()+d),u.end.setTime(i.end.getTime()+d)),!c.equal(u)){var h={start:u.start,end:u.end};r.history.addAndRedo(new a.UpdateTaskHistoryItem(r.modelManipulator,e.successorId,h)),r.moveRelatedTasks(e,c,i,u)}}}))},e.prototype.moveRelatedTasks=function(e,t,n,r){var i=r.start.getTime()-t.start.getTime();this.correctParentsOnChildMoving(n.internalId,i),this.moveStartDependTasks(e.successorId,t.start),this.moveEndDependTasks(e.successorId,t.end)},e.prototype.getCorrectDateRange=function(e,t,n){var i=this,o=new l.DateRange(new Date(t),new Date(n));return r.__spreadArray(r.__spreadArray([],this.checkStartDependencies(e,o.start),!0),this.checkEndDependencies(e,o.end),!0).filter((function(e){return e.critical})).forEach((function(e){var t=i.viewModel.dependencies.getItemById(e.dependencyId),n=i.viewModel.tasks.getItemById(t.predecessorId);t.type===s.DependencyType.FS&&o.start<n.end&&o.start.setTime(n.end.getTime()),t.type===s.DependencyType.SS&&o.start<n.start&&o.start.setTime(n.start.getTime()),t.type===s.DependencyType.FF&&o.end<n.end&&o.end.setTime(n.end.getTime()),t.type===s.DependencyType.SF&&o.end<n.start&&o.end.setTime(n.start.getTime())})),o},e.prototype.correctMoving=function(e,t){var n=this,i=t.end.getTime()-t.start.getTime();return r.__spreadArray(r.__spreadArray([],this.checkStartDependencies(e,t.start),!0),this.checkEndDependencies(e,t.end),!0).filter((function(e){return e.critical})).forEach((function(e){var r=n.viewModel.dependencies.getItemById(e.dependencyId),o=n.viewModel.tasks.getItemById(r.predecessorId);r.type===s.DependencyType.FS&&t.start<o.end&&(t.start.setTime(o.end.getTime()),t.end.setTime(t.start.getTime()+i)),r.type===s.DependencyType.SS&&t.start<o.start&&(t.start.setTime(o.start.getTime()),t.end.setTime(t.start.getTime()+i)),r.type===s.DependencyType.FF&&t.end<o.end&&(t.end.setTime(o.end.getTime()),t.start.setTime(t.end.getTime()-i)),r.type===s.DependencyType.SF&&t.end<o.start&&(t.end.setTime(o.start.getTime()),t.start.setTime(t.end.getTime()-i))})),t},e.prototype.recalculateParents=function(e,t){for(var n=e&&e.parent;n&&n.task;){for(var r=n.children,i=this.range.end,o=this.range.start,s=0,a=0,l={id:n.task.internalId},u=0;u<r.length;u++){var d=r[u].task;if(d.isValid()){i=c.DateTimeUtils.getMinDate(i,d.start),o=c.DateTimeUtils.getMaxDate(o,d.end);var p=d.getDuration();s+=d.progress*p,a+=p}}c.DateTimeUtils.areDatesEqual(n.task.start,i)||(l.start=i),c.DateTimeUtils.areDatesEqual(n.task.end,o)||(l.end=o),l.oldStart=n.task.start,l.oldEnd=n.task.end,(s=a>0?Math.round(s/a):0)!==n.task.progress&&(l.progress=s),t(l),n=n.parent}},e.prototype.updateParentsRangeByChild=function(e){var t=this;this.recalculateParents(this.viewModel.findItem(e),(function(e){if((0,i.isDefined)(e.id)){var n=t.history,r=t.modelManipulator;(0,i.isDefined)(e.start)&&(n.addAndRedo(new a.UpdateTaskHistoryItem(r,e.id,{start:e.start})),t.moveStartDependTasks(e.id,e.oldStart)),(0,i.isDefined)(e.end)&&(n.addAndRedo(new a.UpdateTaskHistoryItem(r,e.id,{end:e.end})),t.moveEndDependTasks(e.id,e.oldEnd)),(0,i.isDefined)(e.progress)&&n.addAndRedo(new a.UpdateTaskHistoryItem(r,e.id,{progress:e.progress}))}}))},e.prototype.updateChildRangeByParent=function(e,t,n){var r=this.viewModel.findItem(e);if(r&&0!==r.children.length)for(var i=r.children,s=0;s<i.length;s++){var l=i[s].task,c=new Date(l.start.getTime()+t),u=o.DateUtils.getRangeMSPeriod(l.start,l.end),d=o.DateUtils.getDSTCorrectedTaskEnd(c,u);n.push({id:l.internalId,start:l.start,end:l.end}),this.history.addAndRedo(new a.UpdateTaskHistoryItem(this.modelManipulator,l.internalId,{start:c,end:d})),this.updateChildRangeByParent(l.internalId,t,n)}},e.prototype.updateParentsIfRequired=function(e){this._parentAutoCalc&&(this.updateParentsRangeByChild(e),this.updateOwnerInAutoParentMode())},e.prototype.correctParentsOnChildMoving=function(e,t){var n=this;if(this._parentAutoCalc&&0!==t){this.updateParentsRangeByChild(e);var r=[];this.updateChildRangeByParent(e,t,r),this.isValidateDependenciesRequired&&r.forEach((function(t){n.moveStartDependTasks(t.id,t.start,e),n.moveEndDependTasks(t.id,t.end,e)})),this.updateOwnerInAutoParentMode()}},e.prototype.canCreateDependency=function(e,t){return this.viewModel.canCreateDependency(e,t)},e}();t.ValidationController=d},4927:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ValidationError=void 0;var n=function(e,t){void 0===t&&(t=!1),this.dependencyId=e,this.critical=t};t.ValidationError=n},6350:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ViewVisualModelDependencyInfo=void 0;var n=function(e,t,n){this.id=e,this.predecessor=t,this.type=n};t.ViewVisualModelDependencyInfo=n},3562:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ViewVisualModelItem=void 0;var r=n(2491),i=n(6353),o=function(){function e(e,t){this.dependencies=new Array,this.parent=null,this.visible=!0,this.selected=!1,this.visibleIndex=-1,this.task=e,this.resources=t,this.children=new Array,this.isCustom=!1,this.size=new i.Size(0,0)}return Object.defineProperty(e.prototype,"resourceText",{get:function(){var e="";return this.resources.items.forEach((function(t){return e+=t.text+" "})),e},enumerable:!1,configurable:!0}),e.prototype.addChild=function(e){(0,r.isDefined)(e)&&this.children.indexOf(e)<0&&this.children.push(e)},e.prototype.removeChild=function(e){var t=this.children.indexOf(e);t>-1&&this.children.splice(t,1)},e.prototype.getExpanded=function(){return!!this.task&&this.task.expanded},e.prototype.getVisible=function(){if(!this.visible)return!1;for(var e=this.parent;e;){if(!e.visible)return!1;e=e.parent}return!0},e.prototype.changeVisibility=function(e){this.visible=e},e.prototype.changeSelection=function(e){this.selected=e},e.prototype.setDependencies=function(e){e&&(this.dependencies=e.slice())},e}();t.ViewVisualModelItem=o},1408:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ViewVisualModel=void 0;var r=n(8828),i=n(9504),o=n(7380),s=n(9883),a=n(3562),l=n(6350),c=n(21),u=n(2491),d=n(6124),p=n(7352),h=n(2301),f=n(7437),g=function(){function e(e,t,n,a,l,u,d){this._fLockCount=0,this.lockChangesProcessing=!1,this.owner=e,this.tasks=new i.TaskCollection,this.tasks.importFromObject(t),this.dependencies=new o.DependencyCollection,this.dependencies.importFromObject(n),this.resources=new r.ResourceCollection,this.resources.importFromObject(a),this.assignments=new s.ResourceAssignmentCollection,this.assignments.importFromObject(l),this._itemList=new Array,this._viewItemList=new Array,this._workTimeCalculator=new c.WorkingTimeCalculator(u,d),this.updateModel(!0)}return Object.defineProperty(e.prototype,"renderHelper",{get:function(){return this.owner.renderHelper},enumerable:!1,configurable:!0}),e.prototype.updateModel=function(e){this._itemList.splice(0,this._itemList.length);for(var t=this.tasks.items,n=0;n<t.length;n++){var r=t[n];r&&this._itemList.push(new a.ViewVisualModelItem(r,this.getAssignedResources(r)))}this.createHierarchy(e),this.populateItemsForView(),this.owner&&this.owner.currentSelectedTaskID&&this.changeTaskSelected(this.owner.currentSelectedTaskID,!0)},e.prototype.createHierarchy=function(e){var t=this;this.root=new a.ViewVisualModelItem(null,null);for(var n=this._itemList,r=n.reduce((function(e,t){var n,r=null===(n=t.task)||void 0===n?void 0:n.internalId;return(0,u.isDefined)(r)&&(e[r]=t),e}),{}),i=this.requireFirstLoadParentAutoCalc&&e,o=0;o<n.length;o++){var s=n[o],l=r[s.task.parentId]||this.root;s.parent=l,l.addChild(s),i&&this.owner.validationController.recalculateParents(s,(function(e){if((0,u.isDefined)(e.id)){var n=t.tasks.getItemById(e.id);(0,u.isDefined)(e.start)&&(n.start=e.start),(0,u.isDefined)(e.end)&&(n.end=e.end),(0,u.isDefined)(e.progress)&&(n.progress=e.progress)}}))}i&&this.owner.dispatcher.notifyParentDataRecalculated(this.getCurrentTaskData())},e.prototype.getCurrentTaskData=function(){var e=this;return this.tasks.items.map((function(t){return e.getTaskObjectForDataSource(t)}))},e.prototype.getTaskObjectForDataSource=function(e){var t=e.parentId&&this.tasks.getItemById(e.parentId),n=this.getRootTaskId(),r=n&&e.parentId===n?this.getRootRawValue():null==t?void 0:t.id;return{id:e.id,start:e.isValidStart()?e.start:null,end:e.isValidEnd()?e.end:null,duration:e.duration,description:e.description,parentId:r,progress:e.progress,color:e.color,taskType:e.taskType,title:e.title,customFields:e.customFields,expanded:e.expanded}},e.prototype.getDependencyObjectForDataSource=function(e){var t=e instanceof p.Dependency?e:this.getItemByPublicId("dependency",e);if(t){var n=this.convertInternalToPublicKey("task",t.predecessorId),r=this.convertInternalToPublicKey("task",t.successorId);return{id:t.id,predecessorId:(0,u.isDefined)(n)?n:t.predecessorId,successorId:(0,u.isDefined)(r)?r:t.successorId,type:t.type}}return null},e.prototype.getResourceObjectForDataSource=function(e){var t=e instanceof h.Resource?e:this.getItemByPublicId("resource",e);return t?{id:t.id,text:t.text,color:t.color}:null},e.prototype.getResourceAssignmentObjectForDataSource=function(e){var t=e instanceof f.ResourceAssignment?e:this.getItemByPublicId("assignment",e);if(t){var n=this.convertInternalToPublicKey("task",t.taskId),r=this.convertInternalToPublicKey("resource",t.resourceId);return{id:t.id,taskId:(0,u.isDefined)(n)?n:t.taskId,resourceId:(0,u.isDefined)(r)?r:t.resourceId}}return null},e.prototype.getRootRawValue=function(){var e,t,n;return null!==(n=null===(t=null===(e=this.root.children[0])||void 0===e?void 0:e.task)||void 0===t?void 0:t.rawParentId)&&void 0!==n?n:null},e.prototype.populateItemsForView=function(){this._viewItemList.splice(0,this._viewItemList.length),this.populateVisibleItems(this.root),this.updateVisibleItemDependencies()},e.prototype.populateVisibleItems=function(e){var t=this,n=e===this.root;e&&(e.task||n)&&(n||(this._viewItemList.push(e),e.visibleIndex=this._viewItemList.length-1),(e.getExpanded()||e===this.root)&&e.children.forEach((function(e){return t.populateVisibleItems(e)})))},e.prototype.updateVisibleItemDependencies=function(){for(var e=this._viewItemList,t=0;t<e.length;t++){var n=e[t],r=this.getTasVisibleDependencies(n.task);n.setDependencies(r)}},e.prototype.getAssignedResources=function(e){var t=this,n=new r.ResourceCollection;return this.assignments.items.filter((function(t){return t.taskId==e.internalId})).forEach((function(e){n.add(t.resources.getItemById(e.resourceId))})),n},e.prototype.getTasVisibleDependencies=function(e){for(var t=new Array,n=e.internalId,r=this.dependencies.items.filter((function(e){return e.successorId==n})),i=0;i<r.length;i++){var o=r[i],s=this.findItem(o.predecessorId);s&&s.getVisible()&&t.push(new l.ViewVisualModelDependencyInfo(o.internalId,s,o.type))}return t},e.prototype.changeTaskExpanded=function(e,t){var n=this.tasks.getItemById(String(e));n&&(n.expanded=t,this.changed())},e.prototype.changeTaskVisibility=function(e,t){var n=this.findItem(e);n&&(n.visible=t,this.changed())},e.prototype.changeTaskSelected=function(e,t){var n=this._itemList.filter((function(t){return t.task&&t.task.internalId===e}))[0];if(n){n.selected=t;var r=this.findItem(e),i=r&&r.visibleIndex;i>-1&&this.renderHelper.recreateTaskElement(i)}},e.prototype.beginUpdate=function(){this._fLockCount++},e.prototype.endUpdate=function(){this._fLockCount--,0==this._fLockCount&&this.changed()},e.prototype.compareTaskOrder=function(e){var t=new i.TaskCollection;t.importFromObject(e);var n=t.items,r=this.tasks.items;if(n.length!==r.length)return!1;for(var o=0;o<n.length;o++){var s=n[o],a=r[o];if(s.id!==a.id)return!1}return!0},e.prototype.refreshTaskDataIfRequires=function(e){var t=!this.lockChangesProcessing&&!this.compareTaskOrder(e);if(t){var n=this.saveTaskInternalIds();this.tasks.importFromObject(e),this.restoreTaskInternalIds(n),this.updateModel()}return t},e.prototype.saveTaskInternalIds=function(){var e={};return this.tasks.items.map((function(t){return e[t.id]=t.internalId})),e},e.prototype.restoreTaskInternalIds=function(e){for(var t in e)if(Object.prototype.hasOwnProperty.call(e,t)){var n=this.tasks.getItemByPublicId(t);n&&(n.internalId=e[t])}},e.prototype.canCreateDependency=function(e,t){if(!e||!t||e===t)return!1;var n=!1;if(this.enableDependencyValidation){var r=this.getDependentTasksHash();n=this.parentAutoCalc?this.checkTasksInterdependence(e,t,r)||this.checkParent(e,t)||this.checkParent(t,e):this.checkDependencyChain([e],[t],r,[])}return!n},e.prototype.checkParent=function(e,t){return this.getTaskTreeLine(e).indexOf(t)>-1},e.prototype.getTaskTreeLine=function(e){var t=[e],n=this.findItem(e);if(n)for(n=n.parent;null==n?void 0:n.task;)t.push(null==n?void 0:n.task.internalId),n=n.parent;else for(var r=this.tasks.getItemById(e),i=this.tasks.getItemById(null==r?void 0:r.parentId);i;)t.push(i.id),i=this.tasks.getItemById(i.parentId);return t},e.prototype.getDependentTasksHash=function(){var e={};return this.dependencies.items.forEach((function(t){var n,r,i=t.predecessorId,o=t.successorId;null!==(n=e[i])&&void 0!==n||(e[i]=[]),e[i].indexOf(o)<0&&e[i].push(o),null!==(r=e[o])&&void 0!==r||(e[o]=[]),e[o].indexOf(i)<0&&e[o].push(i)})),e},e.prototype.checkTasksInterdependence=function(e,t,n){for(var r,i,o=this.getTaskTreeLine(e).reverse(),s=this.getTaskTreeLine(t).reverse(),a=0;a<o.length-1;a++){var l=o[a],c=s.indexOf(l);c>-1&&(r=o[a+1],i=s[c+1])}return r||i||(r=o[0],i=s[0]),this.checkDependencyChain(this.getBranchIds(r),this.getBranchIds(i),n,[])},e.prototype.checkDependencyChain=function(e,t,n,r){if(e.some((function(e){return t.indexOf(e)>-1})))return!0;r.push.apply(r,e);for(var i=0;i<e.length;i++){var o=n[e[i]];if(o&&this.checkDependencyChain(o.filter((function(e){return-1===r.indexOf(e)})),t,n,r))return!0}return!1},e.prototype.getBranchIds=function(e){var t=this,n=[e],r=this.findItem(e),i=null==r?void 0:r.children;return i&&i.forEach((function(e){var r,i=null===(r=e.task)||void 0===r?void 0:r.internalId;i&&(n=n.concat(t.getBranchIds(i)))})),n},e.prototype.getTasksExpandedState=function(){var e=this.tasks.items,t={};return e.forEach((function(e){return t[e.id]=e.expanded})),t},e.prototype.applyTasksExpandedState=function(e){if(e){for(var t in this.beginUpdate(),e)Object.prototype.hasOwnProperty.call(e,t)&&this.changeTaskExpanded(t,e[t]);this.endUpdate()}},e.prototype.changed=function(){0===this._fLockCount&&(this.populateItemsForView(),this.owner&&this.owner.onVisualModelChanged&&this.owner.onVisualModelChanged())},e.prototype.findItem=function(e){return this._viewItemList.filter((function(t){return t.task&&t.task.internalId===e}))[0]},Object.defineProperty(e.prototype,"items",{get:function(){return this._viewItemList},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"itemCount",{get:function(){return this.items.length},enumerable:!1,configurable:!0}),e.prototype.getTaskVisibility=function(e){var t=this.findItem(e);return!!t&&t.getVisible()},e.prototype.getTaskSelected=function(e){var t=this.findItem(e);return!!t&&t.selected},Object.defineProperty(e.prototype,"noWorkingIntervals",{get:function(){return this._workTimeCalculator.noWorkingIntervals},enumerable:!1,configurable:!0}),e.prototype.updateRange=function(e){this._workTimeCalculator.updateRange(e)},e.prototype.taskHasChildrenByIndex=function(e){return this._viewItemList[e].children.length>0},e.prototype.taskHasChildren=function(e){var t=this.findItem(e);return t&&t.children.length>0},Object.defineProperty(e.prototype,"enableDependencyValidation",{get:function(){var e,t=this.owner&&this.owner.settings;return null===(e=null==t?void 0:t.validation)||void 0===e?void 0:e.validateDependencies},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"parentAutoCalc",{get:function(){var e=this.owner&&this.owner.settings;return e&&e.validation&&e.validation.autoUpdateParentTasks},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"enablePredecessorGap",{get:function(){var e=this.owner&&this.owner.settings;return e&&e.validation&&e.validation.enablePredecessorGap},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"requireFirstLoadParentAutoCalc",{get:function(){return this.parentAutoCalc&&this.owner.requireFirstLoadParentAutoCalc()},enumerable:!1,configurable:!0}),e.prototype.isTaskToCalculateByChildren=function(e){return this.parentAutoCalc&&this.taskHasChildren(e)},e.prototype.hasTasks=function(){return this.tasks.length>0},e.prototype.getDataUpdateErrorCallback=function(){return this.owner.getDataUpdateErrorCallback&&this.owner.getDataUpdateErrorCallback()},e.prototype.convertPublicToInternalKey=function(e,t){var n=this.getItemByPublicId(e,t);return n&&n.internalId},e.prototype.convertInternalToPublicKey=function(e,t){var n=this.getItemByInternalId(e,t);return n&&n.id},e.prototype.getItemByPublicId=function(e,t){var n=t.toString();switch(e){case"task":return this.tasks.getItemByPublicId(n);case"dependency":return this.dependencies.getItemByPublicId(n);case"resource":return this.resources.getItemByPublicId(n);case"assignment":return this.assignments.getItemByPublicId(n)}return null},e.prototype.getItemByInternalId=function(e,t){switch(e){case"task":return this.tasks.getItemById(t);case"dependency":return this.dependencies.getItemById(t);case"resource":return this.resources.getItemById(t);case"assignment":return this.assignments.getItemById(t)}return null},e.prototype.findAssignment=function(e,t){var n=this.convertPublicToInternalKey("resource",e),r=this.convertPublicToInternalKey("task",t);return this.assignments.items.filter((function(e){return e.resourceId===n&&e.taskId===r}))[0]},e.prototype.findAllTaskAssignments=function(e){return this.assignments.items.filter((function(t){return t.taskId===e}))},e.prototype.getAllVisibleTaskIndices=function(e,t){var n,r=[];null!=e||(e=0),null!=t||(t=this._viewItemList.length-1);for(var i=e;i<=t;i++){var o=this._viewItemList[i];(null==o?void 0:o.getVisible())&&(null===(n=null==o?void 0:o.task)||void 0===n?void 0:n.isValid())&&r.push(i)}return r},e.prototype.getVisibleTasks=function(){var e=this;return this.tasks.items.filter((function(t){return t&&e.getTaskVisibility(t.internalId)&&t.isValid()}))},e.prototype.getVisibleDependencies=function(){var e=this.getVisibleTasks().map((function(e){return e.internalId}));return this.dependencies.items.filter((function(t){return t&&e.indexOf(t.successorId)>-1&&e.indexOf(t.predecessorId)>-1}))},e.prototype.getVisibleResourceAssignments=function(){var e=this.getVisibleTasks().map((function(e){return e.internalId}));return this.assignments.items.filter((function(t){return t&&e.indexOf(t.taskId)>-1}))},e.prototype.getVisibleResources=function(){for(var e=[],t=this.getVisibleResourceAssignments(),n=0;n<t.length;n++){var r=this.getItemByInternalId("resource",t[n].resourceId);r&&-1===e.indexOf(r)&&e.push(r)}return e},e.prototype.getRootTaskId=function(){var e;return null!==(e=this.rootTaskId)&&void 0!==e||(this.rootTaskId=this.calculateRootTaskId()),this.rootTaskId},e.prototype.calculateRootTaskId=function(){var e=this.items[0];if(!e)return null;for(;e.parent&&e.task;)e=e.parent;return e.children[0].task.parentId},e.prototype.getTaskMinStart=function(){var e=this.owner.dataRange.start;return this.tasks.items.forEach((function(t){t.isValid()&&t.start.getTime()<e.getTime()&&(e=t.start)})),e},e.prototype.getTaskMaxEnd=function(){var e=this.owner.dataRange.end;return this.tasks.items.forEach((function(t){t.isValid()&&t.end.getTime()>e.getTime()&&(e=t.end)})),e},e.prototype.processServerInsertedKey=function(e,t,n){var r;n===d.GanttDataObjectNames.task&&this.tasks.invalidate(),n===d.GanttDataObjectNames.dependency&&(this.dependencies.invalidate(),this.updateVisibleItemDependencies()),n===d.GanttDataObjectNames.resource&&this.resources.invalidate(),n===d.GanttDataObjectNames.resourceAssignment&&this.assignments.invalidate(),null===(r=this.owner)||void 0===r||r.updateHistoryObsoleteInsertedKey(e,t,n)},e.prototype.onBeginDataObjectCreate=function(){var e,t;null===(t=(e=this.owner).lockUpdateWithReload)||void 0===t||t.call(e)},e.prototype.onEndDataObjectCreate=function(){var e,t;null===(t=(e=this.owner).unlockUpdateWithReload)||void 0===t||t.call(e)},e}();t.ViewVisualModel=g},858:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DateRange=void 0;var n=function(){function e(e,t){this.start=e,this.end=t}return e.prototype.equal=function(e){var t=!0;return t=(t=t&&this.start.getTime()===e.start.getTime())&&this.end.getTime()===e.end.getTime()},e}();t.DateRange=n},7880:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DateTimeUtils=void 0;var r=n(2753),i=n(9331),o=n(2491),s=n(858),a=n(7812),l=function(){function e(){}return e.compareDates=function(e,t){return e&&t?t.getTime()-e.getTime():-1},e.areDatesEqual=function(e,t){return 0==this.compareDates(e,t)},e.getMaxDate=function(e,t){return e||t?e?t&&this.compareDates(e,t)>0?t:e:t:null},e.getMinDate=function(e,t){return e||t?e?t?this.compareDates(e,t)>0?e:t:e:t:null},e.getDaysBetween=function(e,t){var n=Math.abs(t.getTime()-e.getTime());return Math.ceil(n/this.msInDay)},e.getWeeksBetween=function(e,t){var n=this.getDaysBetween(e,t),r=Math.floor(n/7);return e.getDay()>t.getDay()&&r++,r},e.getMonthsDifference=function(e,t){var n=this.compareDates(e,t),r=n>=0?e:t,i=n>=0?t:e;return 12*(i.getFullYear()-r.getFullYear())+(i.getMonth()-r.getMonth())},e.getYearsDifference=function(e,t){return Math.abs(t.getFullYear()-e.getFullYear())},e.getDayNumber=function(e){return Math.ceil(e.getTime()/this.msInDay)},e.getDateByDayNumber=function(e){var t=new Date(e*this.msInDay);return t.setHours(0),t.setMinutes(0),t.setSeconds(0),t},e.addDays=function(e,t){return new Date(e.getTime()+t*this.msInDay)},e.checkDayOfMonth=function(e,t){return e==t.getDate()},e.checkDayOfWeek=function(e,t){return e==t.getDay()},e.checkMonth=function(e,t){return e==t.getMonth()},e.checkYear=function(e,t){return e==t.getFullYear()},e.checkDayOfWeekOccurrenceInMonth=function(e,t,n){var r=this.getSpecificDayOfWeekInMonthDates(t,e.getFullYear(),e.getMonth());return n==a.DayOfWeekMonthlyOccurrence.Last?this.areDatesEqual(e,r[r.length-1]):this.areDatesEqual(e,r[n])},e.getFirstDayOfWeekInMonth=function(e,t){return new Date(e,t,1).getDay()},e.getSpecificDayOfWeekInMonthDates=function(e,t,n){for(var r=this.getFirstDayOfWeekInMonth(t,n),i=e>=r?e-r:e+7-r,o=new Array,s=new Date(t,n,i+1);s.getMonth()==n;)o.push(s),s=this.addDays(s,7);return o},e.getSpecificDayOfWeekInMonthDate=function(e,t,n,r){var i=this.getSpecificDayOfWeekInMonthDates(e,t,n);return r==a.DayOfWeekMonthlyOccurrence.Last?i[i.length-1]:i[r]},e.checkValidDayInMonth=function(e,t,n){return!(n<1||n>31||new Date(e,t,n).getMonth()!=t)},e.getNextMonth=function(e,t){return void 0===t&&(t=1),(e+t)%12},e.convertToDate=function(e){if(e instanceof Date)return new Date(e);var t=Date.parse(e);return isNaN(t)?null:new Date(t)},e.convertTimeRangeToDateRange=function(e,t){var n=this.getDateByDayNumber(t),r=n.getFullYear(),i=n.getMonth(),o=n.getDate(),a=e.start,l=new Date(r,i,o,a.hour,a.min,a.sec,a.msec),c=e.end,u=new Date(r,i,o,c.hour,c.min,c.sec,c.msec);return new s.DateRange(l,u)},e.convertToTimeRanges=function(e){var t=this;return e instanceof Array?e.map((function(e){return t.convertToTimeRange(e)})):this.parseTimeRanges(e)},e.convertToTimeRange=function(e){return e?e instanceof i.TimeRange?e:(0,o.isDefined)(e.start)&&(0,o.isDefined)(e.end)?new i.TimeRange(this.convertToTime(e.start),this.convertToTime(e.end)):this.parseTimeRange(e):null},e.convertToTime=function(e){return e?e instanceof r.Time?e:e instanceof Date?this.getTimeGromJsDate(e):this.parseTime(e):null},e.parseTimeRanges=function(e){var t=this;return e?e.split(/;|,/).map((function(e){return t.parseTimeRange(e)})).filter((function(e){return!!e})):null},e.parseTimeRange=function(e){if(!e)return null;var t=e.split("-"),n=t[0],r=t[1];return(0,o.isDefined)(n)&&(0,o.isDefined)(r)?new i.TimeRange(this.parseTime(n),this.parseTime(r)):null},e.parseTime=function(e){if(!e)return null;var t=e.split(":"),n=parseInt(t[0])||0,i=parseInt(t[1])||0,o=parseInt(t[2])||0,s=parseInt(t[3])||0;return new r.Time(n,i,o,s)},e.getTimeGromJsDate=function(e){if(!e)return null;var t=e.getHours(),n=e.getMinutes(),i=e.getSeconds(),o=e.getMilliseconds();return new r.Time(t,n,i,o)},e.caclTimeDifference=function(e,t){return t.getTimeInMilleconds()-e.getTimeInMilleconds()},e.areTimesEqual=function(e,t){return 0==this.caclTimeDifference(e,t)},e.getMaxTime=function(e,t){return e||t?e?t&&this.caclTimeDifference(e,t)>0?t:e:t:null},e.getMinTime=function(e,t){return e||t?e?t?this.caclTimeDifference(e,t)>0?e:t:e:t:null},e.getLastTimeOfDay=function(){return new r.Time(23,59,59,999)},e.msInDay=864e5,e}();t.DateTimeUtils=l},8719:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DayOfWeek=void 0,function(e){e[e.Sunday=0]="Sunday",e[e.Monday=1]="Monday",e[e.Tuesday=2]="Tuesday",e[e.Wednesday=3]="Wednesday",e[e.Thursday=4]="Thursday",e[e.Friday=5]="Friday",e[e.Saturday=6]="Saturday"}(t.DayOfWeek||(t.DayOfWeek={}))},7812:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DayOfWeekMonthlyOccurrence=void 0,function(e){e[e.First=0]="First",e[e.Second=1]="Second",e[e.Third=2]="Third",e[e.Forth=3]="Forth",e[e.Last=4]="Last"}(t.DayOfWeekMonthlyOccurrence||(t.DayOfWeekMonthlyOccurrence={}))},1805:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DayWorkingTimeInfo=void 0;var r=n(9331),i=n(7880),o=n(2753),s=function(){function e(e,t,n){void 0===e&&(e=0),void 0===t&&(t=!0),void 0===n&&(n=null),this._workingIntervals=new Array,this.dayNumber=e,this.isWorkDay=t,this.addWorkingIntervals(n)}return e.prototype.addWorkingIntervals=function(e){e&&(this._workingIntervals=this._workingIntervals.concat(e.filter((function(e){return!!e}))),this.rearrangeWorkingIntervals())},e.prototype.rearrangeWorkingIntervals=function(){for(var e=0;e<this._workingIntervals.length;e++)this.concatWithIntersectedRanges(this._workingIntervals[e]);this.sortIntervals()},e.prototype.concatWithIntersectedRanges=function(e){var t=this;this.getIntersectedIntervals(e).forEach((function(n){e.concatWith(n),t.removeInterval(n)}))},e.prototype.getIntersectedIntervals=function(e){return this._workingIntervals.filter((function(t){return t.hasIntersect(e)&&t!==e}))},e.prototype.sortIntervals=function(){this._workingIntervals.sort((function(e,t){return i.DateTimeUtils.caclTimeDifference(t.start,e.start)}))},e.prototype.removeInterval=function(e){var t=this._workingIntervals.indexOf(e);t>-1&&t<this._workingIntervals.length&&this._workingIntervals.splice(t,1)},e.prototype.clearIntervals=function(){this._workingIntervals.splice(0,this._workingIntervals.length)},Object.defineProperty(e.prototype,"workingIntervals",{get:function(){return this._workingIntervals.slice()},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"noWorkingIntervals",{get:function(){var e=new Array;if(this.isWorkDay&&0===this._workingIntervals.length)return e;var t=this._workingIntervals.map((function(e){return e.end}));t.splice(0,0,new o.Time);var n=this._workingIntervals.map((function(e){return e.start}));n.push(i.DateTimeUtils.getLastTimeOfDay());for(var s=0;s<t.length;s++){var a=t[s],l=n[s];i.DateTimeUtils.areTimesEqual(a,l)||e.push(new r.TimeRange(a,l))}return e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isWorkDay",{get:function(){return this._isWorkDay},set:function(e){this._isWorkDay=e,e||this.clearIntervals()},enumerable:!1,configurable:!0}),e}();t.DayWorkingTimeInfo=s},3110:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Month=void 0,function(e){e[e.January=0]="January",e[e.February=1]="February",e[e.March=2]="March",e[e.April=3]="April",e[e.May=4]="May",e[e.June=5]="June",e[e.July=6]="July",e[e.August=7]="August",e[e.September=8]="September",e[e.October=9]="October",e[e.November=10]="November",e[e.December=11]="December"}(t.Month||(t.Month={}))},7872:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.MonthInfo=void 0;var r=n(7880),i=function(){function e(e,t){this.month=e,this.year=t}return e.prototype.addMonths=function(e){var t=r.DateTimeUtils.getNextMonth(this.month,e),n=Math.floor(e/12);t<this.month&&++n,this.month=t,this.year+=n},e}();t.MonthInfo=i},4902:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Daily=void 0;var r=n(655),i=n(1789),o=n(7880),s=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r.__extends(t,e),t.prototype.checkDate=function(e){return!0},t.prototype.checkInterval=function(e){return o.DateTimeUtils.getDaysBetween(this.start,e)%this.interval==0},t.prototype.calculatePointByInterval=function(e){var t=this.interval;return this.isRecurrencePoint(e)||(t-=o.DateTimeUtils.getDaysBetween(this.start,e)%this.interval),o.DateTimeUtils.addDays(e,t)},t.prototype.calculateNearestPoint=function(e){return o.DateTimeUtils.addDays(e,1)},t}(i.RecurrenceBase);t.Daily=s},4390:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Monthly=void 0;var r=n(655),i=n(1789),o=n(7880),s=n(7872),a=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r.__extends(t,e),t.prototype.checkDate=function(e){return this._calculateByDayOfWeek?o.DateTimeUtils.checkDayOfWeekOccurrenceInMonth(e,this.dayOfWeekInternal,this.dayOfWeekOccurrenceInternal):o.DateTimeUtils.checkDayOfMonth(this.dayInternal,e)},t.prototype.checkInterval=function(e){return o.DateTimeUtils.getMonthsDifference(this.start,e)%this.interval==0},t.prototype.calculatePointByInterval=function(e){var t=this.start,n=o.DateTimeUtils.getMonthsDifference(t,e),r=Math.floor(n/this.interval)*this.interval,i=new s.MonthInfo(t.getMonth(),t.getFullYear());i.addMonths(r);var a=this.getSpecDayInMonth(i.year,i.month);return o.DateTimeUtils.compareDates(a,e)>=0&&(i.addMonths(this.interval),a=this.getSpecDayInMonth(i.year,i.month)),a},t.prototype.calculateNearestPoint=function(e){var t=e.getMonth(),n=e.getFullYear(),r=this.getSpecDayInMonth(n,t);if(o.DateTimeUtils.compareDates(r,e)>=0){var i=new s.MonthInfo(t,n);i.addMonths(1),r=this.getSpecDayInMonth(i.year,i.month)}return r},Object.defineProperty(t.prototype,"day",{get:function(){return this.dayInternal},set:function(e){this.dayInternal=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"dayOfWeek",{get:function(){return this.dayOfWeekInternal},set:function(e){this.dayOfWeekInternal=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"dayOfWeekOccurrence",{get:function(){return this.dayOfWeekOccurrenceInternal},set:function(e){this.dayOfWeekOccurrenceInternal=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"calculateByDayOfWeek",{get:function(){return this._calculateByDayOfWeek},set:function(e){this._calculateByDayOfWeek=e},enumerable:!1,configurable:!0}),t}(i.RecurrenceBase);t.Monthly=a},1789:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.RecurrenceBase=void 0;var r=n(655),i=n(8719),o=n(7812),s=n(3110),a=n(2491),l=n(7880),c=n(9612),u=function(e){function t(t,n,r,i){void 0===t&&(t=null),void 0===n&&(n=null),void 0===r&&(r=1),void 0===i&&(i=0);var o=e.call(this)||this;return o._start=null,o._end=null,o._interval=1,o._occurrenceCount=0,o._dayOfWeek=0,o._day=1,o._dayOfWeekOccurrence=0,o._month=0,o._calculateByDayOfWeek=!1,o.start=t,o.end=n,o.interval=r,o.occurrenceCount=i,o}return r.__extends(t,e),t.prototype.assignFromObject=function(t){(0,a.isDefined)(t)&&(e.prototype.assignFromObject.call(this,t),this.start=l.DateTimeUtils.convertToDate(t.start),this.end=l.DateTimeUtils.convertToDate(t.end),(0,a.isDefined)(t.interval)&&(this.interval=t.interval),(0,a.isDefined)(t.occurrenceCount)&&(this.occurrenceCount=t.occurrenceCount),(0,a.isDefined)(t.dayOfWeek)&&(this.dayOfWeekInternal=c.RecurrenceFactory.getEnumValue(i.DayOfWeek,t.dayOfWeek)),(0,a.isDefined)(t.day)&&(this.dayInternal=t.day),(0,a.isDefined)(t.dayOfWeekOccurrence)&&(this.dayOfWeekOccurrenceInternal=c.RecurrenceFactory.getEnumValue(o.DayOfWeekMonthlyOccurrence,t.dayOfWeekOccurrence)),(0,a.isDefined)(t.month)&&(this.monthInternal=c.RecurrenceFactory.getEnumValue(s.Month,t.month)),(0,a.isDefined)(t.calculateByDayOfWeek)&&(this._calculateByDayOfWeek=!!t.calculateByDayOfWeek))},t.prototype.calculatePoints=function(e,t){if(!e||!t)return new Array;var n=l.DateTimeUtils.getMaxDate(e,this._start),r=l.DateTimeUtils.getMinDate(t,this._end);return this._occurrenceCount>0?this.calculatePointsByOccurrenceCount(n,r):this.calculatePointsByDateRange(n,r)},t.prototype.calculatePointsByOccurrenceCount=function(e,t){for(var n=new Array,r=this.getFirstPoint(e);r&&n.length<this._occurrenceCount&&l.DateTimeUtils.compareDates(r,t)>=0;)this.isRecurrencePoint(r)&&n.push(r),r=this.getNextPoint(r);return n},t.prototype.calculatePointsByDateRange=function(e,t){for(var n=new Array,r=this.getFirstPoint(e);r&&l.DateTimeUtils.compareDates(r,t)>=0;)this.isRecurrencePoint(r)&&n.push(r),r=this.getNextPoint(r);return n},t.prototype.getFirstPoint=function(e){return this.isRecurrencePoint(e)?e:this.getNextPoint(e)},t.prototype.isRecurrencePoint=function(e){return this.isDateInRange(e)&&this.checkDate(e)&&(!this.useIntervalInCalc()||this.checkInterval(e))},t.prototype.isDateInRange=function(e){return!!e&&(!(this._start&&l.DateTimeUtils.compareDates(this.start,e)<0)&&!(0==this._occurrenceCount&&this.end&&l.DateTimeUtils.compareDates(e,this.end)<0))},t.prototype.useIntervalInCalc=function(){return this.interval>1&&!!this._start},t.prototype.getNextPoint=function(e){return this.isDateInRange(e)?this.useIntervalInCalc()?this.calculatePointByInterval(e):this.calculateNearestPoint(e):null},t.prototype.getSpecDayInMonth=function(e,t){return this._calculateByDayOfWeek?l.DateTimeUtils.getSpecificDayOfWeekInMonthDate(this.dayOfWeekInternal,e,t,this.dayOfWeekOccurrenceInternal):new Date(e,t,this.dayInternal)},Object.defineProperty(t.prototype,"dayInternal",{get:function(){return this._day},set:function(e){e>0&&e<=31&&(this._day=e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"dayOfWeekInternal",{get:function(){return this._dayOfWeek},set:function(e){e>=i.DayOfWeek.Sunday&&e<=i.DayOfWeek.Saturday&&(this._dayOfWeek=e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"dayOfWeekOccurrenceInternal",{get:function(){return this._dayOfWeekOccurrence},set:function(e){e>=o.DayOfWeekMonthlyOccurrence.First&&e<=o.DayOfWeekMonthlyOccurrence.Last&&(this._dayOfWeekOccurrence=e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"monthInternal",{get:function(){return this._month},set:function(e){e>=s.Month.January&&e<=s.Month.December&&(this._month=e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"start",{get:function(){return this._start},set:function(e){e&&(this._start=e,this._end&&e>this._end&&(this._end=e))},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"end",{get:function(){return this._end},set:function(e){e&&(this._end=e,this._start&&e<this._start&&(this._start=e))},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"occurrenceCount",{get:function(){return this._occurrenceCount},set:function(e){e<0&&(e=0),this._occurrenceCount=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"interval",{get:function(){return this._interval},set:function(e){e>0&&(this._interval=e)},enumerable:!1,configurable:!0}),t}(n(6124).DataObject);t.RecurrenceBase=u},9612:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.RecurrenceFactory=void 0;var r=n(2491),i=n(4902),o=n(5475),s=n(4390),a=n(7515),l=function(){function e(){}return e.createRecurrenceByType=function(e){if(!e)return null;switch(e.toLowerCase()){case"daily":return new i.Daily;case"weekly":return new o.Weekly;case"monthly":return new s.Monthly;case"yearly":return new a.Yearly}return null},e.createRecurrenceFromObject=function(e){if(!e)return null;var t=this.createRecurrenceByType(e.type);return t&&t.assignFromObject(e),t},e.getEnumValue=function(e,t){if(!(0,r.isDefined)(e[t]))return null;var n=parseInt(t);return isNaN(n)?e[t]:n},e}();t.RecurrenceFactory=l},5475:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Weekly=void 0;var r=n(655),i=n(1789),o=n(7880),s=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r.__extends(t,e),t.prototype.checkDate=function(e){return o.DateTimeUtils.checkDayOfWeek(this.dayOfWeekInternal,e)},t.prototype.checkInterval=function(e){return o.DateTimeUtils.getWeeksBetween(this.start,e)%this.interval==0},t.prototype.calculatePointByInterval=function(e){var t=o.DateTimeUtils.getWeeksBetween(this.start,e),n=Math.floor(t/this.interval);(t%this.interval>0||e.getDay()>=this.dayOfWeekInternal)&&n++;var r=n*this.interval;return this.calcNextPointWithWeekCount(this.start,r)},t.prototype.calculateNearestPoint=function(e){var t=this.dayOfWeekInternal-e.getDay();return t>0?o.DateTimeUtils.addDays(new Date(e),t):this.calcNextPointWithWeekCount(e,1)},t.prototype.calcNextPointWithWeekCount=function(e,t){void 0===t&&(t=1);var n=7*t+this.dayOfWeekInternal-e.getDay();return o.DateTimeUtils.addDays(new Date(e),n)},Object.defineProperty(t.prototype,"dayOfWeek",{get:function(){return this.dayOfWeekInternal},set:function(e){this.dayOfWeekInternal=e},enumerable:!1,configurable:!0}),t}(i.RecurrenceBase);t.Weekly=s},7515:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Yearly=void 0;var r=n(655),i=n(1789),o=n(7880),s=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r.__extends(t,e),t.prototype.checkDate=function(e){return!!o.DateTimeUtils.checkMonth(this.month,e)&&(this._calculateByDayOfWeek?o.DateTimeUtils.checkDayOfWeekOccurrenceInMonth(e,this.dayOfWeekInternal,this.dayOfWeekOccurrenceInternal):o.DateTimeUtils.checkDayOfMonth(this.dayInternal,e))},t.prototype.checkInterval=function(e){return o.DateTimeUtils.getYearsDifference(this.start,e)%this.interval==0},t.prototype.calculatePointByInterval=function(e){var t=o.DateTimeUtils.getYearsDifference(this.start,e),n=Math.floor(t/this.interval)*this.interval,r=this.start.getFullYear()+n,i=this.getSpecDayInMonth(r,this.monthInternal);return o.DateTimeUtils.compareDates(i,e)>=0&&(r+=this.interval,i=this.getSpecDayInMonth(r,this.monthInternal)),i},t.prototype.calculateNearestPoint=function(e){var t=e.getFullYear(),n=this.getSpecDayInMonth(t,this.monthInternal);return o.DateTimeUtils.compareDates(n,e)>=0&&(n=this.getSpecDayInMonth(++t,this.monthInternal)),n},Object.defineProperty(t.prototype,"month",{get:function(){return this.monthInternal},set:function(e){this.monthInternal=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"day",{get:function(){return this.dayInternal},set:function(e){this.dayInternal=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"dayOfWeek",{get:function(){return this.dayOfWeekInternal},set:function(e){this.dayOfWeekInternal=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"dayOfWeekOccurrence",{get:function(){return this.dayOfWeekOccurrenceInternal},set:function(e){this.dayOfWeekOccurrenceInternal=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"calculateByDayOfWeek",{get:function(){return this._calculateByDayOfWeek},set:function(e){this._calculateByDayOfWeek=e},enumerable:!1,configurable:!0}),t}(i.RecurrenceBase);t.Yearly=s},2753:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Time=void 0;var n=function(){function e(e,t,n,r){void 0===e&&(e=0),void 0===t&&(t=0),void 0===n&&(n=0),void 0===r&&(r=0),this._hour=0,this._min=0,this._sec=0,this._msec=0,this._fullmsec=0,this.hour=e,this.min=t,this.sec=n,this.msec=r}return Object.defineProperty(e.prototype,"hour",{get:function(){return this._hour},set:function(e){e>=0&&e<24&&(this._hour=e,this.updateFullMilleconds())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"min",{get:function(){return this._min},set:function(e){e>=0&&e<60&&(this._min=e,this.updateFullMilleconds())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"sec",{get:function(){return this._sec},set:function(e){e>=0&&e<60&&(this._sec=e,this.updateFullMilleconds())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"msec",{get:function(){return this._msec},set:function(e){e>=0&&e<1e3&&(this._msec=e,this.updateFullMilleconds())},enumerable:!1,configurable:!0}),e.prototype.updateFullMilleconds=function(){var e=60*(60*this._hour+this._min)+this._sec;this._fullmsec=1e3*e+this._msec},e.prototype.getTimeInMilleconds=function(){return this._fullmsec},e}();t.Time=n},9331:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TimeRange=void 0;var r=n(7880),i=function(){function e(e,t){r.DateTimeUtils.caclTimeDifference(e,t)>=0?(this._start=e,this._end=t):(this._start=t,this._end=e)}return Object.defineProperty(e.prototype,"start",{get:function(){return this._start},set:function(e){e&&r.DateTimeUtils.caclTimeDifference(e,this._end)>=0&&(this._start=e)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"end",{get:function(){return this._end},set:function(e){e&&r.DateTimeUtils.caclTimeDifference(this._start,e)>=0&&(this._end=e)},enumerable:!1,configurable:!0}),e.prototype.isTimeInRange=function(e){return r.DateTimeUtils.caclTimeDifference(this._start,e)>=0&&r.DateTimeUtils.caclTimeDifference(e,this._end)>=0},e.prototype.hasIntersect=function(e){return this.isTimeInRange(e.start)||this.isTimeInRange(e.end)||e.isTimeInRange(this.start)||e.isTimeInRange(this.end)},e.prototype.concatWith=function(e){return!!this.hasIntersect(e)&&(this.start=r.DateTimeUtils.getMinTime(this.start,e.start),this.end=r.DateTimeUtils.getMaxTime(this.end,e.end),!0)},e}();t.TimeRange=i},21:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.WorkingTimeCalculator=void 0;var r=n(4432),i=n(1805),o=n(7880),s=function(){function e(e,t){this._workingRules=new r.WorkingDayRuleCollection,this._workDayList=new Array,this._calculationRange=e,this._workingRules.importFromObject(t)}return e.prototype.calculateWorkDayList=function(){if(this._calculationRange){this.clearList();for(var e=this._workingRules.items,t=0;t<e.length;t++)this.processRule(e[t]);this.sortList()}},e.prototype.processRule=function(e){for(var t=e.recurrence.calculatePoints(this._calculationRange.start,this._calculationRange.end),n=function(n){var s=t[n],a=o.DateTimeUtils.getDayNumber(s),l=r._workDayList.filter((function(e){return e.dayNumber==a}))[0];l?(l.isWorkDay=l.isWorkDay&&e.isWorkDay,l.addWorkingIntervals(e.workTimeRanges)):r._workDayList.push(new i.DayWorkingTimeInfo(a,e.isWorkDay,e.workTimeRanges))},r=this,s=0;s<t.length;s++)n(s)},e.prototype.sortList=function(){this._workDayList.sort((function(e,t){return e.dayNumber-t.dayNumber}))},e.prototype.clearList=function(){this._workDayList.splice(0,this._workDayList.length)},e.prototype.calculateNoWorkTimeIntervals=function(){var e=this,t=new Array;return 0==this._workDayList.length&&this.calculateWorkDayList(),this._workDayList.forEach((function(n){return t=t.concat(e.getNoWorkTimeRangesFromDay(n))})),this.concatJointedRanges(t)},e.prototype.concatJointedRanges=function(e){for(var t=new Array,n=0;n<e.length;n++){var r=e[n];t.length>0&&o.DateTimeUtils.compareDates(t[t.length-1].end,r.start)<2?t[t.length-1].end=r.end:t.push(r)}return t},e.prototype.getNoWorkTimeRangesFromDay=function(e){return e.noWorkingIntervals.map((function(t){return o.DateTimeUtils.convertTimeRangeToDateRange(t,e.dayNumber)}))},Object.defineProperty(e.prototype,"noWorkingIntervals",{get:function(){return this._noWorkingIntervals||(this._noWorkingIntervals=this.calculateNoWorkTimeIntervals()),this._noWorkingIntervals.slice()},enumerable:!1,configurable:!0}),e.prototype.updateRange=function(e){this._calculationRange=e,this.invalidate()},e.prototype.invalidate=function(){this._noWorkingIntervals=null,this.clearList()},e}();t.WorkingTimeCalculator=s},8401:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.WorkingTimeRule=void 0;var r=n(655),i=n(6124),o=n(2491),s=n(7880),a=n(9612),l=n(4902),c=function(e){function t(t,n,r){void 0===t&&(t=null),void 0===n&&(n=!0),void 0===r&&(r=null);var i=e.call(this)||this;return i.isWorkDay=!0,i.workTimeRanges=new Array,i.recurrence=t,i.isWorkDay=n,r&&i.workTimeRanges.concat(r),i}return r.__extends(t,e),t.prototype.assignFromObject=function(t){if((0,o.isDefined)(t)){e.prototype.assignFromObject.call(this,t),this.recurrence=a.RecurrenceFactory.createRecurrenceByType(t.recurrenceType)||new l.Daily,(0,o.isDefined)(t.recurrence)&&this.recurrence.assignFromObject(t.recurrence),(0,o.isDefined)(t.isWorkDay)&&(this.isWorkDay=!!t.isWorkDay);var n=s.DateTimeUtils.convertToTimeRanges(t.workTimeRanges);n&&(this.workTimeRanges=n)}},t}(i.DataObject);t.WorkingTimeRule=c},6626:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.GanttViewApi=void 0;var n=function(){function e(e){this.maxZoom=3,this._ganttView=e}return Object.defineProperty(e.prototype,"currentZoom",{get:function(){return this._ganttView.currentZoom},set:function(e){this._ganttView.currentZoom=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"renderHelper",{get:function(){return this._ganttView.renderHelper},enumerable:!1,configurable:!0}),e.prototype.getTaskAreaContainerWidth=function(){return this.renderHelper.getTaskAreaContainerWidth()},e.prototype.updateTickSizeWidth=function(){this._ganttView.updateTickSizeWidth()},Object.defineProperty(e.prototype,"settings",{get:function(){return this._ganttView.settings},enumerable:!1,configurable:!0}),e.prototype.resetAndUpdate=function(){this._ganttView.resetAndUpdate()},e.prototype.scrollToDateCore=function(e,t){this._ganttView.scrollToDateCore(e,t)},Object.defineProperty(e.prototype,"ganttOwner",{get:function(){return this._ganttView.ganttOwner},enumerable:!1,configurable:!0}),e.prototype.scrollLeftByViewType=function(){this._ganttView.scrollLeftByViewType()},Object.defineProperty(e.prototype,"dataRange",{get:function(){return this._ganttView.dataRange},enumerable:!1,configurable:!0}),e.prototype.calculateAutoViewType=function(e,t){return this._ganttView.calculateAutoViewType(e,t)},e.prototype.zoomIn=function(e){void 0===e&&(e=this.getTaskAreaContainerWidth()/2);var t=this.renderHelper.getTargetDateByPos(e),n=this.settings.viewTypeRange.min;this.currentZoom<this.maxZoom?(this.currentZoom++,this.updateTickSizeWidth(),this.resetAndUpdate()):this.settings.viewType>n&&(this.currentZoom=1,this.setViewType(this.settings.viewType-1,!1)),this.scrollToDateCore(t,-e)},e.prototype.zoomOut=function(e){void 0===e&&(e=this.renderHelper.getTaskAreaContainerWidth()/2);var t=this.renderHelper.getTargetDateByPos(e),n=this.settings.viewTypeRange.max;this.currentZoom>1?(this.currentZoom--,this.updateTickSizeWidth(),this.resetAndUpdate()):this.settings.viewType<n&&(this.currentZoom=this.maxZoom,this.setViewType(this.settings.viewType+1,!1)),this.scrollToDateCore(t,-e)},e.prototype.setViewType=function(e,t){void 0===t&&(t=!0),null==e&&(e=this.calculateAutoViewType(this.dataRange.start,this.dataRange.end)),this.settings.viewType!==e&&(this.settings.viewType=e,this.updateTickSizeWidth(),this.resetAndUpdate(),t&&this.scrollLeftByViewType(),this.ganttOwner.updateGanttViewType&&this.ganttOwner.updateGanttViewType(e))},e.prototype.setViewTypeRange=function(e,t){void 0!==e&&(this.settings.viewTypeRange.min=Math.min(e,t)),void 0!==t&&(this.settings.viewTypeRange.max=Math.max(e,t));var n=this.settings.viewTypeRange.min,r=this.settings.viewTypeRange.max,i=this.settings.viewType;n>i?this.setViewType(n):r<i&&this.setViewType(r)},e}();t.GanttViewApi=n},5098:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TaskEditController=void 0;var r=n(2449),i=n(858),o=n(6907),s=n(9279),a=n(1886),l=n(9080),c=n(9201),u=function(){function e(e){this.showInfoDelay=1e3,this.taskIndex=-1,this.successorIndex=-1,this.isEditingInProgress=!1,this.disableTaskEditBox=!1,this.isTaskEditBoxShown=!1,this.settings=e,this.createElements()}return Object.defineProperty(e.prototype,"taskId",{get:function(){return this.viewModel.items[this.taskIndex].task.internalId},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"successorId",{get:function(){return this.viewModel.items[this.successorIndex].task.internalId},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"task",{get:function(){return this.viewItem.task},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"viewItem",{get:function(){return this.viewModel.items[this.taskIndex]},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"renderHelper",{get:function(){return this.settings.getRenderHelper()},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"ganttSettings",{get:function(){return this.settings.getGanttSettings()},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"viewModel",{get:function(){return this.settings.getViewModel()},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"commandManager",{get:function(){return this.settings.getCommandManager()},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"updateTaskCommand",{get:function(){return this.commandManager.updateTaskCommand},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"validationController",{get:function(){return this.settings.getValidationController()},enumerable:!1,configurable:!0}),e.prototype.raiseTaskMoving=function(e,t,n,r){return this.settings.getModelManipulator().dispatcher.raiseTaskMoving(e,t,n,r)},Object.defineProperty(e.prototype,"tooltip",{get:function(){var e;return null!==(e=this._tooltip)&&void 0!==e||(this._tooltip=new a.TaskEditTooltip(this.baseElement,this.tooltipSettings,this.renderHelper.elementTextHelperCultureInfo)),this._tooltip.tooltipSettings=this.tooltipSettings,this._tooltip},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"tooltipSettings",{get:function(){var e=this;return l.TooltipSettings.parse({getHeaderHeight:this.settings.getRenderHelper().header.clientHeight,getTaskProgressTooltipContentTemplate:this.ganttSettings.taskProgressTooltipContentTemplate,getTaskTimeTooltipContentTemplate:this.ganttSettings.taskTimeTooltipContentTemplate,getTaskTooltipContentTemplate:this.ganttSettings.taskTooltipContentTemplate,destroyTemplate:function(t){e.settings.destroyTemplate(t)},formatDate:function(t){return e.settings.formatDate(t)},getTaskAreaContainer:function(){return e.settings.getRenderHelper().taskAreaContainer}})},enumerable:!1,configurable:!0}),e.prototype.show=function(t){if(!this.isEditingInProgress&&!this.disableTaskEditBox){this.taskIndex=t,this.hide(),this.changeWrapInfo(),this.baseElement.className=e.CLASSNAMES.TASK_EDIT_BOX,this.displayDependency(),this.task.isMilestone()&&!this.viewItem.isCustom?this.baseElement.className=this.baseElement.className+" milestone":(this.isTaskUpdateAllowed()||(this.baseElement.className=this.baseElement.className+" "+e.CLASSNAMES.TASK_EDIT_HIDE_UPDATING),this.viewItem.isCustom&&this.baseElement.classList.add(e.CLASSNAMES.TASK_EDIT_BOX_CUSTOM));var n=this.settings.getGanttSettings().editing.taskHoverDelay||0;this.taskDateRange=new i.DateRange(this.task.start,this.task.end),this.displayTaskEditBox(n),this.displayProgressEdit(),this.displayStartEndEditElements()}},e.prototype.displayStartEndEditElements=function(){!this.task.isMilestone()&&this.isTaskUpdateAllowed()&&this.canUpdateTask()?(this.startEdit.style.display="block",this.endEdit.style.display="block"):(this.startEdit.style.display="none",this.endEdit.style.display="none")},e.prototype.displayProgressEdit=function(){!this.viewItem.isCustom&&this.canUpdateTask()&&this.isTaskUpdateAllowed()&&this.wrapInfo.size.width>this.wrapInfo.size.height?(this.progressEdit.style.display="block",this.progressEdit.style.left=this.task.normalizedProgress/100*this.wrapInfo.size.width-this.progressEdit.offsetWidth/2+"px"):this.progressEdit.style.display="none"},e.prototype.displayDependency=function(){this.ganttSettings.editing.enabled&&this.ganttSettings.editing.allowDependencyInsert&&this.ganttSettings.showDependencies||(this.baseElement.className=this.baseElement.className+" hide-dependency")},e.prototype.changeWrapInfo=function(){this.updateWrapInfo(),this.wrapInfo.assignPosition(this.baseElement),this.wrapInfo.assignSize(this.baseElement)},e.prototype.displayTaskEditBox=function(e){var t=this;void 0===e&&(e=0);var n=function(){t.renderHelper.taskArea.appendChild(t.baseElement),t.isTaskEditBoxShown=!0};e?this.timerId=setTimeout(n,e):n()},e.prototype.endEditing=function(){this.isEditingInProgress=!1,this.hide()},e.prototype.hide=function(){this.isTaskEditBoxShown=!1;var e=this.baseElement.parentNode;e&&e.removeChild(this.baseElement),this.tooltip.hide(),clearTimeout(this.timerId)},e.prototype.cancel=function(){clearTimeout(this.timerId)},e.prototype.showTaskInfo=function(e,t){void 0===t&&(t=500),this.timerId&&(t=this.showInfoDelay),this.tooltip.showInfo(this.task,e,t)},e.prototype.updateWrapInfo=function(){this.wrapInfo=this.getTaskWrapperElementInfo(this.taskIndex),this.wrapInfo.position.x--},e.prototype.isAllowedToConnectTasks=function(e){var t,n=this.viewModel.items[e];return this.validationController.canCreateDependency(this.taskId,null===(t=n.task)||void 0===t?void 0:t.internalId)},e.prototype.showDependencySuccessor=function(e){if(this.isAllowedToConnectTasks(e)){this.successorIndex=e;var t=this.getTaskWrapperElementInfo(e);t.assignPosition(this.dependencySuccessorBaseElement),t.assignSize(this.dependencySuccessorBaseElement),t.assignSize(this.dependencySuccessorFrame),this.renderHelper.taskArea.appendChild(this.dependencySuccessorBaseElement)}},e.prototype.hideDependencySuccessor=function(){var e=this.dependencySuccessorBaseElement.parentNode;e&&e.removeChild(this.dependencySuccessorBaseElement),this.successorIndex=-1},e.prototype.processProgress=function(e){if(this.isTaskUpdateAllowed()){this.isEditingInProgress=!0;var t=e.x-this.wrapInfo.position.x,n=0;e.x>this.wrapInfo.position.x&&(n=e.x<this.wrapInfo.position.x+this.wrapInfo.size.width?Math.round(t/this.baseElement.clientWidth*100):100),this.progressEdit.style.left=n/100*this.wrapInfo.size.width-this.progressEdit.offsetWidth/2+"px",this.tooltip.showProgress(n,o.DomUtils.getAbsolutePositionX(this.progressEdit)+this.progressEdit.offsetWidth/2)}},e.prototype.confirmProgress=function(){if(this.isTaskUpdateAllowed()){this.isEditingInProgress=!1;var e=Math.round((this.progressEdit.offsetLeft+this.progressEdit.offsetWidth/2)/this.wrapInfo.size.width*100);this.updateTaskCommand.execute(this.taskId,{progress:e})}},e.prototype.processEnd=function(e){if(this.isTaskUpdateAllowed()){this.baseElement.className=this.baseElement.className+" move",this.isEditingInProgress=!0;var t=e.x>this.wrapInfo.position.x?e.x:this.wrapInfo.position.x,n=t-this.wrapInfo.position.x;this.baseElement.style.width=(n<1?0:n)+"px";var r=this.task.start,i=this.renderHelper.gridLayoutCalculator.getDateByPos(t);i.setSeconds(0),i<r||n<1?this.taskDateRange.end.setTime(r.getTime()):this.taskDateRange.end=this.getCorrectedDate(this.task.end,i),this.tooltip.showTime(r,this.taskDateRange.end,o.DomUtils.getAbsolutePositionX(this.baseElement)+this.baseElement.clientWidth)}},e.prototype.confirmEnd=function(){this.isTaskUpdateAllowed()&&(this.baseElement.className=e.CLASSNAMES.TASK_EDIT_BOX,this.isEditingInProgress=!1,this.updateTaskCommand.execute(this.taskId,{end:this.taskDateRange.end}),this.hide(),this.updateWrapInfo())},e.prototype.processStart=function(e){if(this.isTaskUpdateAllowed()){this.baseElement.className=this.baseElement.className+" move",this.isEditingInProgress=!0;var t=e.x<this.wrapInfo.position.x+this.wrapInfo.size.width?e.x:this.wrapInfo.position.x+this.wrapInfo.size.width,n=this.wrapInfo.size.width-(t-this.wrapInfo.position.x);this.baseElement.style.left=t+"px",this.baseElement.style.width=(n<1?0:n)+"px";var r=this.task.end,i=this.renderHelper.gridLayoutCalculator.getDateByPos(t);i.setSeconds(0),i>r||n<1?this.taskDateRange.start.setTime(r.getTime()):this.taskDateRange.start=this.getCorrectedDate(this.task.start,i),this.tooltip.showTime(this.taskDateRange.start,r,o.DomUtils.getAbsolutePositionX(this.baseElement))}},e.prototype.confirmStart=function(){this.isTaskUpdateAllowed()&&(this.baseElement.className=e.CLASSNAMES.TASK_EDIT_BOX,this.isEditingInProgress=!1,this.updateTaskCommand.execute(this.taskId,{start:this.taskDateRange.start}),this.hide(),this.updateWrapInfo())},e.prototype.processMove=function(e){if(this.isTaskUpdateAllowed()&&this.isTaskEditBoxShown){this.baseElement.className=this.baseElement.className+" move";var t=this.baseElement.offsetLeft+e;this.baseElement.style.left=t+"px";var n=this.renderHelper.gridLayoutCalculator.getDateByPos(t);this.taskDateRange.start=this.getCorrectedDate(this.task.start,n);var r=c.DateUtils.getRangeMSPeriod(this.task.start,this.task.end);return this.taskDateRange.end=c.DateUtils.getDSTCorrectedTaskEnd(this.taskDateRange.start,r),this.isEditingInProgress=this.raiseTaskMoving(this.task,this.taskDateRange.start,this.taskDateRange.end,this.onTaskMovingCallback.bind(this)),this.isEditingInProgress&&this.tooltip.showTime(this.taskDateRange.start,this.taskDateRange.end,o.DomUtils.getAbsolutePositionX(this.baseElement)),this.isEditingInProgress}return!0},e.prototype.onTaskMovingCallback=function(e,t){if(this.taskDateRange.start!==e||this.taskDateRange.end!==t){var n=this.renderHelper.gridLayoutCalculator,r=n.getPosByDate(e),i=n.getPosByDate(t)-r;this.baseElement.style.left=r+"px",this.baseElement.style.width=(i<1?0:i)+"px",this.taskDateRange.start=e,this.taskDateRange.end=t}},e.prototype.confirmMove=function(){this.isTaskUpdateAllowed()&&(this.ganttSettings.editing.allowDependencyInsert||(this.baseElement.className=this.baseElement.className+" hide-dependency"),this.isEditingInProgress&&(this.baseElement.className=e.CLASSNAMES.TASK_EDIT_BOX,this.updateTaskCommand.execute(this.taskId,{start:this.taskDateRange.start,end:this.taskDateRange.end}),this.updateWrapInfo(),this.hide(),this.isEditingInProgress=!1))},e.prototype.getCorrectedDate=function(e,t){if(this.ganttSettings.viewType>r.ViewType.SixHours){var n=t.getFullYear(),i=t.getMonth(),o=t.getDate(),s=this.ganttSettings.viewType===r.ViewType.Days?t.getHours():e.getHours(),a=e.getMinutes(),l=e.getSeconds(),c=e.getMilliseconds();return new Date(n,i,o,s,a,l,c)}return t},e.prototype.startDependency=function(t){this.dependencyLine=document.createElement("DIV"),this.dependencyLine.className=e.CLASSNAMES.TASK_EDIT_DEPENDENCY_LINE,this.renderHelper.taskArea.appendChild(this.dependencyLine),this.startPosition=t},e.prototype.processDependency=function(e){this.isEditingInProgress=!0,this.drawline(this.startPosition,e)},e.prototype.endDependency=function(e){this.isEditingInProgress=!1,null!=e&&this.commandManager.createDependencyCommand.execute(this.task.internalId,this.successorId,e);var t=this.dependencyLine.parentNode;t&&t.removeChild(this.dependencyLine),this.dependencyLine=null,this.hideDependencySuccessor(),this.hide()},e.prototype.selectDependency=function(e){this.ganttSettings.editing.allowDependencyDelete&&(this.dependencyId=e)},e.prototype.isDependencySelected=function(e){return this.dependencyId&&this.dependencyId===e},e.prototype.deleteSelectedDependency=function(){this.dependencyId&&this.commandManager.removeDependencyCommand.execute(this.dependencyId)},e.prototype.getTaskWrapperElementInfo=function(e){var t=this.renderHelper.gridLayoutCalculator,n=t.getTaskWrapperElementInfo(e);return n.size.width=t.getTaskWidth(e),n.size.height=t.getTaskHeight(e),n},e.prototype.createElements=function(){this.baseElement=document.createElement("DIV"),this.baseFrame=document.createElement("DIV"),this.baseFrame.className=e.CLASSNAMES.TASK_EDIT_FRAME,this.baseElement.appendChild(this.baseFrame),this.progressEdit=document.createElement("DIV"),this.progressEdit.className=e.CLASSNAMES.TASK_EDIT_PROGRESS,this.baseFrame.appendChild(this.progressEdit),this.progressEdit.appendChild(document.createElement("DIV")),this.dependencyFinish=document.createElement("DIV"),this.dependencyFinish.classList.add(e.CLASSNAMES.TASK_EDIT_DEPENDENCY_RIGTH),s.Browser.TouchUI&&this.dependencyFinish.classList.add(e.CLASSNAMES.TASK_EDIT_TOUCH),this.baseFrame.appendChild(this.dependencyFinish),this.dependencyStart=document.createElement("DIV"),this.dependencyStart.classList.add(e.CLASSNAMES.TASK_EDIT_DEPENDENCY_LEFT),s.Browser.TouchUI&&this.dependencyStart.classList.add(e.CLASSNAMES.TASK_EDIT_TOUCH),this.baseFrame.appendChild(this.dependencyStart),this.startEdit=document.createElement("DIV"),this.startEdit.className=e.CLASSNAMES.TASK_EDIT_START,this.baseFrame.appendChild(this.startEdit),this.endEdit=document.createElement("DIV"),this.endEdit.className=e.CLASSNAMES.TASK_EDIT_END,this.baseFrame.appendChild(this.endEdit),this.dependencySuccessorBaseElement=document.createElement("DIV"),this.dependencySuccessorBaseElement.className=e.CLASSNAMES.TASK_EDIT_BOX_SUCCESSOR,this.dependencySuccessorFrame=document.createElement("DIV"),this.dependencySuccessorFrame.className=e.CLASSNAMES.TASK_EDIT_FRAME_SUCCESSOR,this.dependencySuccessorBaseElement.appendChild(this.dependencySuccessorFrame),this.dependencySuccessorStart=document.createElement("DIV"),this.dependencySuccessorStart.classList.add(e.CLASSNAMES.TASK_EDIT_SUCCESSOR_DEPENDENCY_RIGTH),s.Browser.TouchUI&&this.dependencySuccessorStart.classList.add(e.CLASSNAMES.TASK_EDIT_TOUCH),this.dependencySuccessorFrame.appendChild(this.dependencySuccessorStart),this.dependencySuccessorFinish=document.createElement("DIV"),this.dependencySuccessorFinish.classList.add(e.CLASSNAMES.TASK_EDIT_SUCCESSOR_DEPENDENCY_LEFT),s.Browser.TouchUI&&this.dependencySuccessorFinish.classList.add(e.CLASSNAMES.TASK_EDIT_TOUCH),this.dependencySuccessorFrame.appendChild(this.dependencySuccessorFinish),this._tooltip=new a.TaskEditTooltip(this.baseElement,this.tooltipSettings,this.renderHelper.elementTextHelperCultureInfo),this.attachEvents()},e.prototype.attachEvents=function(){this.onMouseLeaveHandler=function(){this.isEditingInProgress||this.hide()}.bind(this),this.baseElement.addEventListener("mouseleave",this.onMouseLeaveHandler)},e.prototype.drawline=function(e,t){if(e.x>t.x){var n=t;t=e,e=n}var r=Math.atan((e.y-t.y)/(t.x-e.x));r=-(r=180*r/Math.PI);var i=Math.sqrt((e.x-t.x)*(e.x-t.x)+(e.y-t.y)*(e.y-t.y));this.dependencyLine.style.left=e.x+"px",this.dependencyLine.style.top=e.y+"px",this.dependencyLine.style.width=i+"px",this.dependencyLine.style.transform="rotate("+r+"deg)"},e.prototype.canUpdateTask=function(){return!this.viewModel.isTaskToCalculateByChildren(this.task.internalId)},e.prototype.isTaskUpdateAllowed=function(){var e=this.ganttSettings.editing;return e.enabled&&e.allowTaskUpdate},e.prototype.detachEvents=function(){var e;null===(e=this.baseElement)||void 0===e||e.removeEventListener("mouseleave",this.onMouseLeaveHandler)},e.CLASSNAMES={TASK_EDIT_BOX:"dx-gantt-task-edit-wrapper",TASK_EDIT_BOX_CUSTOM:"dx-gantt-task-edit-wrapper-custom",TASK_EDIT_FRAME:"dx-gantt-task-edit-frame",TASK_EDIT_PROGRESS:"dx-gantt-task-edit-progress",TASK_EDIT_DEPENDENCY_RIGTH:"dx-gantt-task-edit-dependency-r",TASK_EDIT_DEPENDENCY_LEFT:"dx-gantt-task-edit-dependency-l",TASK_EDIT_START:"dx-gantt-task-edit-start",TASK_EDIT_END:"dx-gantt-task-edit-end",TASK_EDIT_DEPENDENCY_LINE:"dx-gantt-task-edit-dependency-line",TASK_EDIT_BOX_SUCCESSOR:"dx-gantt-task-edit-wrapper-successor",TASK_EDIT_FRAME_SUCCESSOR:"dx-gantt-task-edit-frame-successor",TASK_EDIT_SUCCESSOR_DEPENDENCY_RIGTH:"dx-gantt-task-edit-successor-dependency-r",TASK_EDIT_SUCCESSOR_DEPENDENCY_LEFT:"dx-gantt-task-edit-successor-dependency-l",TASK_EDIT_TOUCH:"dx-gantt-edit-touch",TASK_EDIT_HIDE_UPDATING:"hide-updating"},e}();t.TaskEditController=u},1886:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TaskEditTooltip=void 0;var r=n(6907),i=function(){function e(e,t,n){this.parentElement=e,this.cultureInfo=n,this.tooltipSettings=t}return Object.defineProperty(e.prototype,"baseElement",{get:function(){return this._baseElement||this.createTooltipContainer(),this._baseElement},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"headerHeight",{get:function(){return this.tooltipSettings.getHeaderHeight},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"taskAreaContainer",{get:function(){return this.tooltipSettings.getTaskAreaContainer()},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"taskTooltipContentTemplate",{get:function(){return this.tooltipSettings.getTaskTooltipContentTemplate},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"taskProgressTooltipContentTemplate",{get:function(){return this.tooltipSettings.getTaskProgressTooltipContentTemplate},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"taskTimeTooltipContentTemplate",{get:function(){return this.tooltipSettings.getTaskTimeTooltipContentTemplate},enumerable:!1,configurable:!0}),e.prototype.destroyTemplate=function(e){this.tooltipSettings.destroyTemplate(e)},e.prototype.formatDate=function(e){return this.tooltipSettings.formatDate(e)},e.prototype.createTooltipContainer=function(){this._baseElement=document.createElement("DIV"),this._baseElement.className=e.CLASSNAMES.TASK_EDIT_PROGRESS_STATUS,this.parentElement.appendChild(this._baseElement)},e.prototype.setDefaultTooltip=function(t){this.defaultTooltip=document.createElement("DIV"),this.defaultTooltip.className=e.CLASSNAMES.TASK_EDIT_TOOLTIP_DEFAULT;var n=document.createElement("DIV");n.className=e.CLASSNAMES.TASK_EDIT_TASK_TITLE;var r=document.createElement("SPAN");if(n.appendChild(r),this.defaultTooltip.appendChild(n),r.innerText=t.title,this.defaultTooltip.appendChild(this.getTimeContent(t.start,t.end)),!isNaN(t.progress)){var i=document.createElement("DIV");i.className=e.CLASSNAMES.TASK_EDIT_PROGRESS_STATUS_TIME;var o=document.createElement("SPAN"),s=document.createElement("SPAN");i.appendChild(o),i.appendChild(s),this.defaultTooltip.appendChild(i),o.innerText=(this.cultureInfo.progress?this.cultureInfo.progress:"Progress")+": ",s.innerText=t.progress+"%"}this.baseElement.appendChild(this.defaultTooltip)},e.prototype.setDefaultProgressTooltip=function(t){this.defaultTooltip=document.createElement("DIV"),this.defaultTooltip.className=e.CLASSNAMES.TASK_EDIT_TOOLTIP_DEFAULT,this.defaultTooltip.innerText=t+"%",this.baseElement.appendChild(this.defaultTooltip)},e.prototype.setDefaultTimeTooltip=function(t,n){this.defaultTooltip=document.createElement("DIV"),this.defaultTooltip.className=e.CLASSNAMES.TASK_EDIT_TOOLTIP_DEFAULT,this.defaultTooltip.appendChild(this.getTimeContent(t,n)),this.baseElement.appendChild(this.defaultTooltip)},e.prototype.showInfo=function(e,t,n){var r=this;void 0===n&&(n=0);var i=this.taskTooltipContentTemplate;this.destroyTemplate(this.baseElement),i?i(this.baseElement,e,(function(){r.showTooltip(t,!1,n)})):(this.setDefaultTooltip(e),this.showTooltip(t,!1,n))},e.prototype.showProgress=function(e,t){var n=this,r=this.taskProgressTooltipContentTemplate;this.destroyTemplate(this.baseElement),r?r(this.baseElement,{progress:e},(function(){n.showTooltip(t)})):(this.setDefaultProgressTooltip(e),this.show(t))},e.prototype.showTime=function(e,t,n){var r=this,i=this.taskTimeTooltipContentTemplate;this.destroyTemplate(this.baseElement),i?i(this.baseElement,{start:e,end:t},(function(){r.showTooltip(n)})):(this.setDefaultTimeTooltip(e,t),this.show(n))},e.prototype.showTooltip=function(e,t,n){var r,i=this;if(void 0===t&&(t=!0),void 0===n&&(n=0),null===(r=this.baseElement)||void 0===r?void 0:r.innerHTML){var o=function(){i.show(e,t)};n?this.timerId=setTimeout(o,n):o()}},e.prototype.show=function(t,n){var r,i,o,s=this;void 0===n&&(n=!0),null===(r=this.defaultTooltip)||void 0===r||r.classList.remove(e.CLASSNAMES.TASK_EDIT_TOOLTIP_ARROW_AFTER),null===(i=this.defaultTooltip)||void 0===i||i.classList.remove(e.CLASSNAMES.TASK_EDIT_TOOLTIP_ARROW_BEFORE),this.baseElement.style.display="block";var a=this.getLeftPosition(t),l=this.needToShowUnderParent(),c=this.getTopPosition(l),u=l?e.CLASSNAMES.TASK_EDIT_TOOLTIP_ARROW_AFTER:e.CLASSNAMES.TASK_EDIT_TOOLTIP_ARROW_BEFORE;null===(o=this.defaultTooltip)||void 0===o||o.classList.add(u),this.baseElement.style.left=a+"px",this.baseElement.style.top=c+"px",n&&(this.timerId&&clearTimeout(this.timerId),this.timerId=setTimeout((function(){s.hide()}),1500))},e.prototype.hide=function(){this.baseElement.style.display="none",this.destroyTemplate(this.baseElement),clearTimeout(this.timerId)},e.prototype.getTimeContent=function(t,n){var r=document.createElement("TABLE");r.className=e.CLASSNAMES.TASK_EDIT_PROGRESS_STATUS_TIME;var i=document.createElement("TBODY");r.appendChild(i);var o=document.createElement("TR"),s=document.createElement("TD"),a=document.createElement("TD"),l=document.createElement("TR"),c=document.createElement("TD"),u=document.createElement("TD");return o.appendChild(s),o.appendChild(a),l.appendChild(c),l.appendChild(u),i.appendChild(o),i.appendChild(l),s.innerText=(this.cultureInfo.start?this.cultureInfo.start:"Start")+": ",a.innerText=this.formatDate(t),c.innerText=(this.cultureInfo.end?this.cultureInfo.end:"End")+": ",u.innerText=this.formatDate(n),r},e.prototype.getLeftPosition=function(t){var n=t-r.DomUtils.getAbsolutePositionX(this.parentElement)-2*e.defaultArrowHeight;if(this.taskAreaContainer){var i=r.DomUtils.getAbsolutePositionX(this.taskAreaContainer.getElement())+this.taskAreaContainer.getWidth(),o=t+this.baseElement.clientWidth-i;o>0&&(n-=o)}return n},e.prototype.getTopPosition=function(t){return t?this.parentElement.clientHeight+e.defaultArrowHeight:-this.baseElement.clientHeight-e.defaultArrowHeight},e.prototype.needToShowUnderParent=function(){var t,n=r.DomUtils.getAbsolutePositionY(this.parentElement)-this.headerHeight-r.DomUtils.getDocumentScrollTop()-e.defaultHeightOffset,i=(null===(t=this.taskAreaContainer)||void 0===t?void 0:t.scrollTop)||0,o=this.parentElement.offsetTop-i;return this.baseElement.clientHeight>n||this.baseElement.clientHeight>o},e.CLASSNAMES={TASK_EDIT_PROGRESS_STATUS:"dx-gantt-task-edit-tooltip",TASK_EDIT_TOOLTIP_DEFAULT:"dx-gantt-task-edit-tooltip-default",TASK_EDIT_TASK_TITLE:"dx-gantt-task-title",TASK_EDIT_PROGRESS_STATUS_TIME:"dx-gantt-status-time",TASK_EDIT_TOOLTIP_ARROW_BEFORE:"dx-gantt-task-edit-tooltip-before",TASK_EDIT_TOOLTIP_ARROW_AFTER:"dx-gantt-task-edit-tooltip-after"},e.defaultArrowHeight=5,e.defaultHeightOffset=15,e}();t.TaskEditTooltip=i},3336:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.GanttMovingHelper=void 0;var r=n(9279),i=n(3714),o=function(){function e(e){this.taskAreaContainer=e,this.movingInfo=null}return e.prototype.startMoving=function(e){this.movingInfo=this.calcMovingInfo(e),this.updateGanttAreaCursor(!0)},e.prototype.cancelMoving=function(){this.movingInfo=null},e.prototype.onMouseMove=function(e){this.move(e)},e.prototype.onMouseUp=function(e){this.cancelMoving(),this.updateGanttAreaCursor(!1)},e.prototype.move=function(e){this.updateScrollPosition(e)},e.prototype.updateScrollPosition=function(e){var t=Math.round(i.EvtUtils.getEventX(e)),n=Math.round(i.EvtUtils.getEventY(e)),r=t-this.movingInfo.eventX,o=n-this.movingInfo.eventY,s=r<0?-1:1,a=o<0?-1:1,l=s<0?this.movingInfo.maxRightDelta:this.movingInfo.maxLeftDelta,c=a<0?this.movingInfo.maxBottomDelta:this.movingInfo.maxTopDelta;Math.abs(r)>l&&(r=l*s),Math.abs(o)>c&&(o=c*a);var u=this.movingInfo.scrollLeft-r,d=this.movingInfo.scrollTop-o,p=this.taskAreaContainer;p.scrollLeft!==u&&(p.scrollLeft=u),p.scrollTop!==d&&(p.scrollTop=d)},e.prototype.calcMovingInfo=function(e){var t=this.taskAreaContainer;return{eventX:i.EvtUtils.getEventX(e),eventY:i.EvtUtils.getEventY(e),scrollLeft:t.scrollLeft,scrollTop:t.scrollTop,maxLeftDelta:t.scrollLeft,maxRightDelta:t.scrollWidth-t.scrollLeft-t.getElement().offsetWidth,maxTopDelta:t.scrollTop,maxBottomDelta:t.scrollHeight-t.scrollTop-t.getElement().offsetHeight}},e.prototype.updateGanttAreaCursor=function(e){var t=r.Browser.IE?"move":"grabbing";this.taskAreaContainer.getElement().style.cursor=e?t:"default"},e}();t.GanttMovingHelper=o},6958:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TaskAreaStateController=void 0;var r=n(8900),i=n(6907),o=n(3714),s=n(5098),a=n(2449),l=n(5376),c=n(4264),u=n(9155),d=n(125),p=n(1404),h=n(6591),f=n(2395),g=n(7295),y=function(){function e(e,t,n){var i=this;this.position=new r.Point(-1,-1),this._pointers={},this._listener=e,this._listener.setHandler(p.TaskAreaStateEventNames.STATE_EXIT,(function(){i.switchToDefaultState()})),this._listener.setHandler(p.TaskAreaStateEventNames.GET_POINTERS_INFO,(function(e){return i.getPointersInfo(e.triggerEvent)})),this._taskArea=t,this._cellSize=n,this.switchToDefaultState()}return e.prototype.onKeyDown=function(e){this._state.onKeyDown(e)},e.prototype.onScroll=function(e){this._state.onScroll(e)},e.prototype.onContextMenu=function(e){this._state.onContextMenu(e)},e.prototype.onMouseWheel=function(e){e.ctrlKey&&(e.preventDefault(),e.stopPropagation(),this.switchState(h.TaskAreaZoomState)),this._state.onMouseWheel(e)},Object.defineProperty(e.prototype,"currentState",{get:function(){return this._state},enumerable:!1,configurable:!0}),e.prototype.onTaskPointerEnter=function(e){this._state.onTaskPointerEnter(e)},e.prototype.onTaskAreaPointerLeave=function(e){this._state.onTaskAreaLeave(e)},e.prototype.onDocumentPointerCancel=function(e){this._clearPointerInfo(e)},e.prototype.onDocumentPointerDown=function(e){this._updatePinterInfo(e),this.toggleStateOnPointerDown(e),this._state.onDocumentPointerDown(e)},e.prototype.onDocumentPointerMove=function(e){this._updatePinterInfo(e),this.toggleStateOnPointerMove(e),this._state.onDocumentPointerMove(e)},e.prototype.onDocumentPointerUp=function(e){this._clearPointerInfo(e),this._state.onDocumentPointerUp(e)},e.prototype.onTaskTouchStart=function(e){this._state.onTaskTouchStart(e)},e.prototype.onTouchStart=function(e){this.toggleStateOnPointerDown(e),this._state.onTouchStart(e)},e.prototype.onTouchEnd=function(e){this._state.onTouchEnd(e)},e.prototype.onTouchMove=function(e){this.toggleStateOnPointerMove(e),this._state.onTouchMove(e)},e.prototype.onClick=function(e){this._state.onClick(e)},e.prototype.onDblClick=function(e){this._state.onDblClick(e)},e.prototype.onTaskAreaMouseLeave=function(e){this._state.onTaskAreaLeave(e)},e.prototype.onTaskElementHover=function(e){this._state.onTaskHover(e)},e.prototype.onTaskElementLeave=function(e){this._state.onTaskLeave(e)},e.prototype.onMouseUp=function(e){this._state.onMouseUp(e)},e.prototype.onMouseMove=function(e){this._state.onMouseMove(e)},e.prototype.onMouseDown=function(e){switch(this.getTaskAreaEventSource(e)){case a.TaskAreaEventSource.TaskArea:this.processMouseDownOnTaskArea(e);break;case a.TaskAreaEventSource.TaskEdit_Frame:this.switchState(g.TaskMoveState);break;case a.TaskAreaEventSource.TaskEdit_Progress:case a.TaskAreaEventSource.TaskEdit_Start:case a.TaskAreaEventSource.TaskEdit_End:this.switchState(f.TaskEditState);break;case a.TaskAreaEventSource.TaskEdit_DependencyStart:case a.TaskAreaEventSource.TaskEdit_DependencyFinish:this.switchState(c.TaskAreaDependencyState)}this._state.onMouseDown(e)},Object.defineProperty(e.prototype,"taskArea",{get:function(){return this._taskArea},enumerable:!1,configurable:!0}),e.prototype.switchToDefaultState=function(){this._state=new l.TaskAreaDefaultState(this._listener,this.taskArea,this._cellSize)},e.prototype.switchState=function(e){this._state instanceof e||(this._state&&this._state.finish(),this._state=new e(this._listener,this.taskArea,this._cellSize),this._state.start())},e.prototype.processMouseDownOnTaskArea=function(e){o.EvtUtils.isLeftButtonPressed(e)&&!u.TaskAreaDomHelper.isConnectorLine(e)&&this.switchState(d.TaskAreaScrollState)},e.prototype.toggleStateOnPointerDown=function(e){var t=this.toggleStateWhenMultiOrOutsideTouch(e);if(this.position=new r.Point(o.EvtUtils.getEventX(e),o.EvtUtils.getEventY(e)),!t&&this._canStartDrag(e))switch(this.getTaskAreaEventSource(e)){case a.TaskAreaEventSource.TaskEdit_DependencyStart:case a.TaskAreaEventSource.TaskEdit_DependencyFinish:this.switchState(c.TaskAreaDependencyState);break;case a.TaskAreaEventSource.TaskEdit_Progress:case a.TaskAreaEventSource.TaskEdit_Start:case a.TaskAreaEventSource.TaskEdit_End:this.switchState(f.TaskEditState)}},e.prototype.toggleStateOnPointerMove=function(e){var t=this.toggleStateWhenMultiOrOutsideTouch(e),n=Math.abs(this.position.x-o.EvtUtils.getEventX(e))>1||Math.abs(this.position.y-o.EvtUtils.getEventY(e))>1;if(!t&&n&&this._canStartDrag(e)&&this._state instanceof l.TaskAreaDefaultState)switch(this.getTaskAreaEventSource(e)){case a.TaskAreaEventSource.TaskArea:this.checkEventInTaskEditFrameArea(e)?this.switchState(g.TaskMoveState):this.switchState(d.TaskAreaScrollState);break;case a.TaskAreaEventSource.TaskEdit_Frame:this.isTaskUpdateDisabled()?this.switchState(d.TaskAreaScrollState):this.switchState(g.TaskMoveState);break;case a.TaskAreaEventSource.TaskEdit_Progress:case a.TaskAreaEventSource.TaskEdit_Start:case a.TaskAreaEventSource.TaskEdit_End:this.switchState(f.TaskEditState)}},e.prototype.toggleStateWhenMultiOrOutsideTouch=function(e){var t=this._getActivePointersCount(e),n=!this.isInTaskArea(e),r=t>=2||n;return t>2||n?this.switchState(l.TaskAreaDefaultState):2===t&&this.switchState(h.TaskAreaZoomState),r},e.prototype.checkEventInTaskEditFrameArea=function(e){var t,n,r=this.getTaskEditFrameElement();if(this.isTaskUpdateDisabled()||!r)return!1;var i=(null==e?void 0:e.clientX)||(null===(t=null==e?void 0:e.touches[0])||void 0===t?void 0:t.clientX),o=(null==e?void 0:e.clientY)||(null===(n=null==e?void 0:e.touches[0])||void 0===n?void 0:n.clientY),s=r.getBoundingClientRect();return i>=s.left&&i<=s.left+s.width&&o>=s.top&&o<=s.top+s.height},e.prototype.isTaskUpdateDisabled=function(){return"false"===this._taskArea.getAttribute("task-edit-enabled")},e.prototype.isInTaskArea=function(e){return i.DomUtils.isItParent(this._taskArea,o.EvtUtils.getEventSource(e))},e.prototype.getTaskEditFrameElement=function(){return this._taskArea.getElementsByClassName(s.TaskEditController.CLASSNAMES.TASK_EDIT_FRAME)[0]},e.prototype._updatePinterInfo=function(e){var t=e.pointerId;this._pointers[t]={pageX:e.pageX,pageY:e.pageY,pointerType:e.pointerType}},e.prototype._clearPointerInfo=function(e){var t=e.pointerId;delete this._pointers[t]},e.prototype.isTouchEvent=function(e){return u.TaskAreaDomHelper.isTouchEvent(e)},e.prototype.isPointerEvent=function(e){return u.TaskAreaDomHelper.isPointerEvent(e)},e.prototype._getActivePointersCount=function(e){var t=this;return this.isTouchEvent(e)?e.touches.length:Object.keys(this._pointers).filter((function(n){return t._pointers[n].pointerType===e.pointerType})).length},e.prototype.getPointersInfo=function(e){var t=this,n=null==e?void 0:e.pointerType,r=Object.keys(this._pointers).map((function(e){return t._pointers[e]}));return n&&(r=r.filter((function(e){return e.pointerType===n}))),r},e.prototype._canStartDrag=function(e){return!(this._getActivePointersCount(e)>1)&&((!u.TaskAreaDomHelper.isMousePointer(e)||1===e.buttons)&&!u.TaskAreaDomHelper.isConnectorLine(e))},e.prototype.getTaskAreaEventSource=function(e){return u.TaskAreaDomHelper.getEventSource(o.EvtUtils.getEventSource(e))},e}();t.TaskAreaStateController=y},5376:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TaskAreaDefaultState=void 0;var r=n(655),i=n(3714),o=n(9155),s=n(5867),a=n(1404),l=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r.__extends(t,e),t.prototype.finish=function(){this.clearTimers(),this.raiseEvent(a.TaskAreaStateEventNames.CONTEXTMENU_HIDE)},t.prototype.onMouseDownInternal=function(e){this.onPointerDownBase(e)},t.prototype.onDocumentPointerDownInternal=function(e){this.onPointerDownBase(e)},t.prototype.onDocumentPointerUpInternal=function(e){this.onPointerUpBase(e)},t.prototype.onDocumentPointerMoveInternal=function(e){this.clearTimers()},t.prototype.onTouchStartInternal=function(e){this.onPointerDownBase(e)},t.prototype.onTouchEndInternal=function(e){this.onPointerUpBase(e)},t.prototype.onTouchMoveInternal=function(e){this.clearTimers()},t.prototype.onPointerDownBase=function(e){var n=this;e.preventDefault(),this._lastTouchRowIndex=this.getClickedRowIndex(e),(this.isPointerEvent(e)?o.TaskAreaDomHelper.isMousePointer(e):this.isMouseEvent(e))?this.changeSelectionOnTouchDown(e):(setTimeout((function(){o.TaskAreaDomHelper.isMousePointer(e)||n.raiseEvent(a.TaskAreaStateEventNames.CONTEXTMENU_HIDE,e),n.changeSelectionOnTouchDown(e)}),0),clearTimeout(this._contextMenuTimer),this._contextMenuTimer=setTimeout((function(){return n.showContextMenuOnTouchDown(e)}),t.defaultContextMenuTimeout))},t.prototype.onPointerUpBase=function(e){var n=this;if(clearTimeout(this._contextMenuTimer),e.preventDefault(),this.canToEmulateClick(e)){var r=this.getClickedRowIndex(e),i=new Date;if(this._lastEmulatedClickTime)i.getTime()-this._lastEmulatedClickTime.getTime()<t.defaultDblClickTimeout&&(this.raiseEvent(a.TaskAreaStateEventNames.TASK_AREA_DBLCLICK,e,r),delete this._lastEmulatedClickTime);else{var s=!this.raiseEvent(a.TaskAreaStateEventNames.TASK_AREA_CLICK,e,r);clearTimeout(this._dblClickClearTimer),o.TaskAreaDomHelper.isMousePointer(e)&&this.raiseEvent(a.TaskAreaStateEventNames.CONTEXTMENU_HIDE,e),s||(this._lastEmulatedClickTime=i,this._dblClickClearTimer=setTimeout((function(){delete n._lastEmulatedClickTime}),t.defaultDblClickClearTimeout))}}},t.prototype.canToEmulateClick=function(e){var t=!o.TaskAreaDomHelper.isConnectorLine(e)&&this.getClickedRowIndex(e)===this._lastTouchRowIndex;return t&&o.TaskAreaDomHelper.isMousePointer(e)&&t&&(t=2!==e.button),t},t.prototype.changeSelectionOnTouchDown=function(e){var t=o.TaskAreaDomHelper.isConnectorLine(e);t||this.raiseTaskSelection(e,this.getClickedRowIndex(e)),this.raiseDependencySelection(e,t?i.EvtUtils.getEventSource(e).getAttribute("dependency-id"):null)},t.prototype.showContextMenuOnTouchDown=function(e){var t=o.TaskAreaDomHelper.isConnectorLine(e);this.raiseEvent(a.TaskAreaStateEventNames.CONTEXTMENU_SHOW,e,this.getClickedRowIndex(e),{type:t?"dependency":"task"})},t.prototype.clearTimers=function(){clearTimeout(this._contextMenuTimer),clearTimeout(this._dblClickClearTimer),delete this._lastEmulatedClickTime},t.defaultContextMenuTimeout=3e3,t.defaultDblClickTimeout=500,t.defaultDblClickClearTimeout=t.defaultDblClickTimeout+100,t}(s.TaskAreaStateBase);t.TaskAreaDefaultState=l},4264:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TaskAreaDependencyState=t.dependencyMap=void 0;var r=n(655),i=n(8900),o=n(6907),s=n(3714),a=n(5950),l=n(2449),c=n(9155),u=n(5867),d=n(1404);t.dependencyMap=[],t.dependencyMap[l.TaskAreaEventSource.TaskEdit_DependencyStart]=[],t.dependencyMap[l.TaskAreaEventSource.TaskEdit_DependencyFinish]=[],t.dependencyMap[l.TaskAreaEventSource.TaskEdit_DependencyStart][l.TaskAreaEventSource.Successor_DependencyStart]=a.DependencyType.SS,t.dependencyMap[l.TaskAreaEventSource.TaskEdit_DependencyStart][l.TaskAreaEventSource.Successor_DependencyFinish]=a.DependencyType.SF,t.dependencyMap[l.TaskAreaEventSource.TaskEdit_DependencyFinish][l.TaskAreaEventSource.Successor_DependencyStart]=a.DependencyType.FS,t.dependencyMap[l.TaskAreaEventSource.TaskEdit_DependencyFinish][l.TaskAreaEventSource.Successor_DependencyFinish]=a.DependencyType.FF;var p=function(e){function n(){return null!==e&&e.apply(this,arguments)||this}return r.__extends(n,e),n.prototype.onMouseUp=function(e){this.onDependencyEndByMouse(e)},n.prototype.onMouseDownInternal=function(e){this.onDependencyStart(e)},n.prototype.onMouseMoveInternal=function(e){this.onDependencyMoveStep(e)},n.prototype.onTouchStartInternal=function(e){this.onDependencyStart(e)},n.prototype.onTouchEndInternal=function(e){this.onDependencyEndByTouch(e)},n.prototype.onTouchMoveInternal=function(e){this.onDependencyMoveStep(e)},n.prototype.onDocumentPointerDownInternal=function(e){this.onDependencyStart(e)},n.prototype.onDocumentPointerUpInternal=function(e){this.onDependencyEndByTouch(e)},n.prototype.onDocumentPointerMoveInternal=function(e){this.onDependencyMoveStep(e)},n.prototype.onDependencyStart=function(e){var t=s.EvtUtils.getEventSource(e);this.source=c.TaskAreaDomHelper.getEventSource(t);var n=this.getRelativePos(new i.Point(o.DomUtils.getAbsolutePositionX(t)+t.clientWidth/2,o.DomUtils.getAbsolutePositionY(t)+t.clientHeight/2));this.raiseEvent(d.TaskAreaStateEventNames.DEPENDENCY_START,e,null,{pos:n})},n.prototype.onDependencyMoveStep=function(e){e.preventDefault();var t=this.getRelativePos(new i.Point(s.EvtUtils.getEventX(e),s.EvtUtils.getEventY(e)));this.raiseEvent(d.TaskAreaStateEventNames.DEPENDENCY_PROCESS,e,this.getClickedRowIndex(e),{pos:t})},n.prototype.onDependencyEndByMouse=function(e){var n=c.TaskAreaDomHelper.getEventSource(s.EvtUtils.getEventSource(e)),r=n===l.TaskAreaEventSource.Successor_DependencyStart||n===l.TaskAreaEventSource.Successor_DependencyFinish?t.dependencyMap[this.source][n]:null;this.processEndDependency(e,r)},n.prototype.onDependencyEndByTouch=function(e){var n=this.raiseEvent(d.TaskAreaStateEventNames.GET_DEPENDENCY_POINTS,e),r=this.getRelativePos(n.successorStart),o=this.getRelativePos(n.successorFinish),a=this.getRelativePos(new i.Point(s.EvtUtils.getEventX(e),s.EvtUtils.getEventY(e))),c=this.isTouchNearby(a,r)?l.TaskAreaEventSource.Successor_DependencyStart:this.isTouchNearby(a,o)?l.TaskAreaEventSource.Successor_DependencyFinish:null,u=c===l.TaskAreaEventSource.Successor_DependencyStart||c===l.TaskAreaEventSource.Successor_DependencyFinish?t.dependencyMap[this.source][c]:null;this.processEndDependency(e,u)},n.prototype.onTaskAreaLeaveInternal=function(e){this.processEndDependency(e,null)},n.prototype.processEndDependency=function(e,t){this.raiseEvent(d.TaskAreaStateEventNames.DEPENDENCY_END,e,null,{type:t}),this.raiseEvent(d.TaskAreaStateEventNames.STATE_EXIT,e)},n.prototype.isTouchNearby=function(e,t){return Math.abs(t.x-e.x)<=20&&Math.abs(t.y-e.y)<=20},n}(u.TaskAreaStateBase);t.TaskAreaDependencyState=p},9155:(e,t,n)=>{var r;Object.defineProperty(t,"__esModule",{value:!0}),t.TaskAreaDomHelper=void 0;var i=n(6907),o=n(3714),s=n(5098),a=n(2449),l=n(1855),c=function(){function e(){}return e.getEventSource=function(t){var n,r=(t.nodeType===(null===(n=null===window||void 0===window?void 0:window.Node)||void 0===n?void 0:n.ELEMENT_NODE)?t:t.parentNode).classList[0];return e.classToSource[r]||a.TaskAreaEventSource.TaskArea},e.isConnectorLine=function(e){var t=o.EvtUtils.getEventSource(e);return i.DomUtils.hasClassName(t,l.GridLayoutCalculator.CLASSNAMES.CONNECTOR_HORIZONTAL)||i.DomUtils.hasClassName(t,l.GridLayoutCalculator.CLASSNAMES.CONNECTOR_VERTICAL)},e.isTaskElement=function(t){var n=o.EvtUtils.getEventSource(t),r=e.classToSource[n.classList[0]];return r===a.TaskAreaEventSource.TaskEdit_Frame||r===a.TaskAreaEventSource.TaskEdit_Progress||r===a.TaskAreaEventSource.TaskEdit_Start||r===a.TaskAreaEventSource.TaskEdit_End||r===a.TaskAreaEventSource.TaskEdit_DependencyStart||r===a.TaskAreaEventSource.TaskEdit_DependencyFinish},e.isMouseEvent=function(e){return e instanceof MouseEvent},e.isTouchEvent=function(e){return window.TouchEvent&&e instanceof TouchEvent},e.isPointerEvent=function(e){return window.PointerEvent&&e instanceof PointerEvent},e.isMousePointer=function(e){return this.isPointerEvent(e)&&"mouse"===e.pointerType},e.classToSource=((r={})[s.TaskEditController.CLASSNAMES.TASK_EDIT_PROGRESS]=a.TaskAreaEventSource.TaskEdit_Progress,r[s.TaskEditController.CLASSNAMES.TASK_EDIT_START]=a.TaskAreaEventSource.TaskEdit_Start,r[s.TaskEditController.CLASSNAMES.TASK_EDIT_END]=a.TaskAreaEventSource.TaskEdit_End,r[s.TaskEditController.CLASSNAMES.TASK_EDIT_FRAME]=a.TaskAreaEventSource.TaskEdit_Frame,r[s.TaskEditController.CLASSNAMES.TASK_EDIT_DEPENDENCY_RIGTH]=a.TaskAreaEventSource.TaskEdit_DependencyStart,r[s.TaskEditController.CLASSNAMES.TASK_EDIT_DEPENDENCY_LEFT]=a.TaskAreaEventSource.TaskEdit_DependencyFinish,r[s.TaskEditController.CLASSNAMES.TASK_EDIT_SUCCESSOR_DEPENDENCY_RIGTH]=a.TaskAreaEventSource.Successor_DependencyStart,r[s.TaskEditController.CLASSNAMES.TASK_EDIT_SUCCESSOR_DEPENDENCY_LEFT]=a.TaskAreaEventSource.Successor_DependencyFinish,r),e}();t.TaskAreaDomHelper=c},125:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TaskAreaScrollState=void 0;var r=n(655),i=n(5867),o=n(1404),s=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._isStarted=!1,t}return r.__extends(t,e),t.prototype.finish=function(){this.raiseEvent(o.TaskAreaStateEventNames.TASK_AREA_END_MOVE,null)},t.prototype.onMouseUp=function(e){this.onEnd(e)},t.prototype.onMouseDownInternal=function(e){this.onBeforeStart(e)},t.prototype.onMouseMoveInternal=function(e){this.onMove(e)},t.prototype.onDocumentPointerUpInternal=function(e){this.onEnd(e)},t.prototype.onDocumentPointerMoveInternal=function(e){this.onMoveByPointer(e)},t.prototype.onTouchEndInternal=function(e){this.onEnd(e)},t.prototype.onTouchMoveInternal=function(e){this.onMoveByPointer(e)},t.prototype.onBeforeStart=function(e){e.preventDefault(),this.raiseDependencySelection(e,null),this.raiseEvent(o.TaskAreaStateEventNames.TASK_EDIT_END,e),this.raiseEvent(o.TaskAreaStateEventNames.TASK_AREA_START_MOVE,e),this._isStarted=!0},t.prototype.onMoveByPointer=function(e){this._isStarted?this.onMove(e):this.onBeforeStart(e)},t.prototype.onMove=function(e){e.preventDefault(),this.raiseEvent(o.TaskAreaStateEventNames.TASK_AREA_PROCESS_MOVE,e)},t.prototype.onEnd=function(e){e.preventDefault(),this.raiseEvent(o.TaskAreaStateEventNames.TASK_AREA_END_MOVE,e),this.raiseEvent(o.TaskAreaStateEventNames.STATE_EXIT,e),this._isStarted=!1},t}(i.TaskAreaStateBase);t.TaskAreaScrollState=s},5867:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TaskAreaStateBase=void 0;var r=n(9279),i=n(8900),o=n(6907),s=n(3714),a=n(2153),l=n(3461),c=n(9155),u=n(1404),d=function(){function e(e,t,n){this.position=new i.Point(-1,-1),this.isCursorInArea=!1,this._listener=e,this._taskArea=t,this._cellSize=n}return e.prototype.start=function(){},e.prototype.finish=function(){},e.prototype.isTouchEvent=function(e){return c.TaskAreaDomHelper.isTouchEvent(e)},e.prototype.isPointerEvent=function(e){return c.TaskAreaDomHelper.isPointerEvent(e)},e.prototype.isMouseEvent=function(e){return c.TaskAreaDomHelper.isMouseEvent(e)},e.prototype.onMouseWheel=function(e){},e.prototype.onScroll=function(e){this.raiseEvent(u.TaskAreaStateEventNames.TASK_AREA_SCROLL)},e.prototype.onKeyDown=function(e){this.isCursorInArea&&this.raiseEvent(u.TaskAreaStateEventNames.TASK_AREA_KEY_DOWN,e,null,{code:this.getShortcutCode(e)})},e.prototype.onContextMenu=function(e){var t=this.getClickedRowIndex(e),n=c.TaskAreaDomHelper.isConnectorLine(e);n||this.raiseTaskSelection(e,t),e.stopPropagation(),e.preventDefault(),r.Browser.WebKitFamily&&(e.returnValue=!1),this.raiseEvent(u.TaskAreaStateEventNames.CONTEXTMENU_SHOW,e,t,{type:n?"dependency":"task"})},e.prototype.onTaskPointerEnter=function(e){this.onTaskPointerEnterBase(e)},e.prototype.onDocumentPointerMove=function(e){this.processPointerMove(e)},e.prototype.onDocumentPointerDown=function(e){this.processPointerDown(e)},e.prototype.onDocumentPointerUp=function(e){this.processPointerUp(e)},e.prototype.onTaskTouchStart=function(e){this.onTaskPointerEnterBase(e)},e.prototype.onTouchMove=function(e){this.processPointerMove(e)},e.prototype.onTouchStart=function(e){this.processPointerDown(e)},e.prototype.onTouchEnd=function(e){this.processPointerUp(e)},e.prototype.onMouseDown=function(e){this.processPointerDown(e)},e.prototype.onMouseUp=function(e){},e.prototype.onMouseMove=function(e){this.processPointerMove(e)},e.prototype.onTaskHover=function(e){this.onTaskPointerEnterBase(e)},e.prototype.onTaskLeave=function(e){this.raiseEvent(u.TaskAreaStateEventNames.TASK_LEAVE,e,this.getClickedRowIndex(e))},e.prototype.onClick=function(e){var t=this.getClickedRowIndex(e);this.raiseTaskSelection(e,t),this.raiseEvent(u.TaskAreaStateEventNames.TASK_AREA_CLICK,e,t)},e.prototype.onDblClick=function(e){e.preventDefault(),this.raiseEvent(u.TaskAreaStateEventNames.TASK_AREA_DBLCLICK,e,this.getClickedRowIndex(e))},e.prototype.onTaskAreaLeave=function(e){(!this.isPointerEvent(e)||c.TaskAreaDomHelper.isMousePointer(e))&&(this.isCursorInArea=!1,this.raiseEvent(u.TaskAreaStateEventNames.TASK_EDIT_END,e)),this.onTaskAreaLeaveInternal(e)},e.prototype.onTaskPointerEnterBase=function(e){this.isTouchEvent(e)||this.isPointerEvent(e)||e.preventDefault(),this.raiseEvent(u.TaskAreaStateEventNames.TASK_EDIT_START,e,this.getClickedRowIndex(e))},e.prototype.processPointerDown=function(e){var t=this.isTouchEvent(e),n=this.isPointerEvent(e);(t||n)&&this.checkAndProcessTouchOutsideArea(e)||(this.position=new i.Point(s.EvtUtils.getEventX(e),s.EvtUtils.getEventY(e)),(t||n)&&e.preventDefault(),t?this.onTouchStartInternal(e):n?this.onDocumentPointerDownInternal(e):this.onMouseDownInternal(e))},e.prototype.processPointerMove=function(e){var t,n=this.isTouchEvent(e),r=this.isPointerEvent(e);null!==(t=this.position)&&void 0!==t||(this.position=new i.Point(s.EvtUtils.getEventX(e),s.EvtUtils.getEventY(e)));var o=Math.abs(this.position.x-s.EvtUtils.getEventX(e))>2||Math.abs(this.position.y-s.EvtUtils.getEventY(e))>2;!((n||r)&&this.checkAndProcessTouchOutsideArea(e))&&o&&((n||r)&&e.preventDefault(),this.isTouchEvent(e)?this.onTouchMoveInternal(e):this.isPointerEvent(e)?this.onDocumentPointerMoveInternal(e):(this.isCursorInArea=!0,this.onMouseMoveInternal(e)))},e.prototype.processPointerUp=function(e){this.checkAndProcessTouchOutsideArea(e)||(e.preventDefault(),this.isTouchEvent(e)?this.onTouchEndInternal(e):this.onDocumentPointerUpInternal(e))},e.prototype.onMouseDownInternal=function(e){},e.prototype.onMouseMoveInternal=function(e){},e.prototype.onDocumentPointerUpInternal=function(e){},e.prototype.onDocumentPointerDownInternal=function(e){},e.prototype.onDocumentPointerMoveInternal=function(e){},e.prototype.onTouchStartInternal=function(e){},e.prototype.onTouchEndInternal=function(e){},e.prototype.onTouchMoveInternal=function(e){},e.prototype.onTaskAreaLeaveInternal=function(e){},e.prototype.checkAndProcessTouchOutsideArea=function(e){var t=!this.isInTaskArea(e);return t&&this.raiseEvent(u.TaskAreaStateEventNames.TASK_EDIT_END,e),this.isCursorInArea=!t,t},e.prototype.raiseEvent=function(e,t,n,r){var i=new l.TaskAreaEventArgs(e,t,n,r),o=this._listener.getHandler(e);return o&&o(i)},e.prototype.raiseDependencySelection=function(e,t){this.raiseEvent(u.TaskAreaStateEventNames.DEPENDENCY_SELECTION,e,null,{key:t})},e.prototype.raiseTaskSelection=function(e,t){o.DomUtils.isItParent(this._taskArea,s.EvtUtils.getEventSource(e))&&!c.TaskAreaDomHelper.isConnectorLine(e)&&this.raiseEvent(u.TaskAreaStateEventNames.TASK_SELECTION,e,t)},e.prototype.getClickedRowIndex=function(e){if(!e)return-1;var t=s.EvtUtils.getEventY(e)-o.DomUtils.getAbsolutePositionY(this._taskArea);return Math.floor(t/this._cellSize.height)},e.prototype.getRelativePos=function(e){var t=o.DomUtils.getAbsolutePositionX(this._taskArea),n=o.DomUtils.getAbsolutePositionY(this._taskArea);return new i.Point(e.x-t,e.y-n)},e.prototype.isInTaskArea=function(e){return o.DomUtils.isItParent(this._taskArea,s.EvtUtils.getEventSource(e))},e.prototype.getShortcutCode=function(e){var t=a.KeyUtils.getEventKeyCode(e),n=0;return e.altKey&&(n|=a.ModifierKey.Alt),e.ctrlKey&&(n|=a.ModifierKey.Ctrl),e.shiftKey&&(n|=a.ModifierKey.Shift),e.metaKey&&r.Browser.MacOSPlatform&&(n|=a.ModifierKey.Meta),n|t},e}();t.TaskAreaStateBase=d},1404:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TaskAreaStateEventNames=void 0;var n=function(){function e(){}return e.TASK_AREA_CLICK="taskAreaClick",e.TASK_AREA_DBLCLICK="taskAreaDblClick",e.TASK_AREA_SCROLL="taskAreaScroll",e.TASK_AREA_START_MOVE="taskAreaStartMove",e.TASK_AREA_PROCESS_MOVE="taskAreaProcessMove",e.TASK_AREA_END_MOVE="taskAreaEndMove",e.TASK_AREA_ZOOM_IN="taskAreaZoomIn",e.TASK_AREA_ZOOM_OUT="taskAreaZoomOut",e.CONTEXTMENU_SHOW="contextMenuShow",e.CONTEXTMENU_HIDE="contextMenuHide",e.DEPENDENCY_SELECTION="dependencySelection",e.STATE_EXIT="stateExit",e.TASK_SELECTION="taskSelection",e.TASK_EDIT_START="taskEditStart",e.TASK_EDIT_END="taskEditEnd",e.TASK_LEAVE="taskLeave",e.TASK_PROCESS_MOVE="taskProcessMove",e.TASK_END_MOVE="taskEndMove",e.TASK_PROCESS_PROGRESS="taskProcessProgress",e.TASK_END_PROGRESS="taskEndProgress",e.TASK_PROCESS_START="taskProcessStart",e.TASK_CONFIRM_START="taskConfirmStart",e.TASK_PROCESS_END="taskProcessEnd",e.TASK_CONFIRM_END="taskConfirmEnd",e.GET_DEPENDENCY_POINTS="getDependencyPoints",e.DEPENDENCY_START="dependencyStart",e.DEPENDENCY_PROCESS="dependencyProcess",e.DEPENDENCY_END="dependencyEnd",e.TASK_AREA_KEY_DOWN="taskAreaKeyDown",e.GET_POINTERS_INFO="getPointersInfo",e.GET_COORDINATES_REF_POINT="getCoordinatesRefPoint",e}();t.TaskAreaStateEventNames=n},6591:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TaskAreaZoomState=void 0;var r=n(655),i=n(8900),o=n(3714),s=n(5596),a=n(5867),l=n(1404),c=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._isInZooming=!1,t}return r.__extends(t,e),t.prototype.onMouseWheel=function(e){e.ctrlKey&&(e.preventDefault(),e.stopPropagation(),this._isInZooming||this.processZoom(e,o.EvtUtils.getWheelDelta(e)>0,50))},t.prototype.onDocumentPointerUpInternal=function(e){this.onEndZoom(e)},t.prototype.onDocumentPointerMoveInternal=function(e){this.onTouchZoom(e)},t.prototype.onTouchEndInternal=function(e){this.onEndZoom(e)},t.prototype.onTouchMoveInternal=function(e){this.onTouchZoom(e)},t.prototype.onTouchZoom=function(e){var t;if(e.stopPropagation(),e.preventDefault(),!this._isInZooming){var n=this.getTouchDistance(e);null!==(t=this.prevDistance)&&void 0!==t||(this.prevDistance=n);var r=this.prevDistance-n;Math.abs(r)>3&&(this.processZoom(e,r>0,1e3),this.prevDistance=n)}},t.prototype.processZoom=function(e,t,n){var r=this;this._isInZooming=!0,setTimeout((function(){r._isInZooming=!1}),n);var i=t?l.TaskAreaStateEventNames.TASK_AREA_ZOOM_IN:l.TaskAreaStateEventNames.TASK_AREA_ZOOM_OUT;this.raiseEvent(i,e,null,{leftPos:this.getLeftPosition(e)})},t.prototype.onEndZoom=function(e){this.prevDistance=null,this.raiseEvent(l.TaskAreaStateEventNames.STATE_EXIT,e)},t.prototype.getTouchDistance=function(e){var t=this.GetTouchPoints(e);return this.getDistance(t[0],t[1])},t.prototype.GetTouchPoints=function(e){var t,n,r,o;if(this.isTouchEvent(e)){var s=e.touches;return[new i.Point(s[0].pageX,s[0].pageY),new i.Point(s[1].pageX,s[1].pageY)]}var a=this.raiseEvent(l.TaskAreaStateEventNames.GET_POINTERS_INFO,e);return[new i.Point(null===(t=a[0])||void 0===t?void 0:t.pageX,null===(n=a[0])||void 0===n?void 0:n.pageY),new i.Point(null===(r=a[1])||void 0===r?void 0:r.pageX,null===(o=a[1])||void 0===o?void 0:o.pageY)]},t.prototype.getDistance=function(e,t){return s.Metrics.euclideanDistance(e,t)},t.prototype.getLeftPosition=function(e){var t=0;return this.isTouchEvent(e)||this.isPointerEvent(e)?t=this.getZoomMiddlePoint(e).x:this.isMouseEvent(e)&&(t=this.getMouseZoomLeftPos(e)),t},t.prototype.getMouseZoomLeftPos=function(e){var t=this.raiseEvent(l.TaskAreaStateEventNames.GET_COORDINATES_REF_POINT);return o.EvtUtils.getEventX(e)-t.x},t.prototype.getZoomMiddlePoint=function(e){var t=this.raiseEvent(l.TaskAreaStateEventNames.GET_COORDINATES_REF_POINT),n=this.GetTouchPoints(e),r=this.convertScreenToChartCoordinates(n[0],t),o=this.convertScreenToChartCoordinates(n[1],t);return new i.Point((r.x+o.x)/2,(r.y+o.y)/2)},t.prototype.convertScreenToChartCoordinates=function(e,t){return new i.Point(e.x-t.x,e.y-t.y)},t}(a.TaskAreaStateBase);t.TaskAreaZoomState=c},6262:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TaskDragBaseState=void 0;var r=n(655),i=n(8900),o=n(3714),s=n(5867),a=n(1404),l=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r.__extends(t,e),t.prototype.onMouseDownInternal=function(e){this.onStart(e)},t.prototype.onMouseUp=function(e){this.onEnd(e)},t.prototype.onMouseMoveInternal=function(e){this.onDrag(e)},t.prototype.onTouchStartInternal=function(e){this.onStart(e)},t.prototype.onTouchEndInternal=function(e){this.onEnd(e)},t.prototype.onTouchMoveInternal=function(e){this.onDrag(e)},t.prototype.onDocumentPointerDownInternal=function(e){this.onStart(e)},t.prototype.onDocumentPointerUpInternal=function(e){this.onEnd(e)},t.prototype.onDocumentPointerMoveInternal=function(e){this.onDrag(e)},t.prototype.onStart=function(e){this.currentPosition=new i.Point(o.EvtUtils.getEventX(e),o.EvtUtils.getEventY(e)),this.raiseDependencySelection(e,null),this.onStartInternal(e)},t.prototype.onDrag=function(e){var t;e.preventDefault();var n=new i.Point(o.EvtUtils.getEventX(e),o.EvtUtils.getEventY(e));null!==(t=this.currentPosition)&&void 0!==t||(this.currentPosition=n),this.onDragInternal(n),this.currentPosition=n},t.prototype.onEnd=function(e){this.onEndInternal(e),this.raiseEvent(a.TaskAreaStateEventNames.STATE_EXIT,e)},t.prototype.onStartInternal=function(e){},t.prototype.onEndInternal=function(e){},t.prototype.onDragInternal=function(e){},t.prototype.finish=function(){this.raiseEvent(a.TaskAreaStateEventNames.TASK_EDIT_END)},t}(s.TaskAreaStateBase);t.TaskDragBaseState=l},2395:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TaskEditState=void 0;var r=n(655),i=n(3714),o=n(2449),s=n(9155),a=n(1404),l=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r.__extends(t,e),t.prototype.onStartInternal=function(e){var t;null!==(t=this._source)&&void 0!==t||(this._source=s.TaskAreaDomHelper.getEventSource(i.EvtUtils.getEventSource(e)))},t.prototype.onEndInternal=function(e){this.raiseEvent(this.getEventOnEndKey(),e)},t.prototype.onDragInternal=function(e){var t=this.getRelativePos(e);this.raiseEvent(this.getEventOnDragKey(),null,null,{position:t})},t.prototype.getEventOnDragKey=function(){switch(this._source){case o.TaskAreaEventSource.TaskEdit_Start:return a.TaskAreaStateEventNames.TASK_PROCESS_START;case o.TaskAreaEventSource.TaskEdit_End:return a.TaskAreaStateEventNames.TASK_PROCESS_END;case o.TaskAreaEventSource.TaskEdit_Progress:return a.TaskAreaStateEventNames.TASK_PROCESS_PROGRESS}},t.prototype.getEventOnEndKey=function(){switch(this._source){case o.TaskAreaEventSource.TaskEdit_Start:return a.TaskAreaStateEventNames.TASK_CONFIRM_START;case o.TaskAreaEventSource.TaskEdit_End:return a.TaskAreaStateEventNames.TASK_CONFIRM_END;case o.TaskAreaEventSource.TaskEdit_Progress:return a.TaskAreaStateEventNames.TASK_END_PROGRESS}},t}(n(6262).TaskDragBaseState);t.TaskEditState=l},7295:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TaskMoveState=void 0;var r=n(655),i=n(1404),o=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r.__extends(t,e),t.prototype.onEndInternal=function(e){this.raiseEvent(i.TaskAreaStateEventNames.TASK_END_MOVE,e)},t.prototype.onDragInternal=function(e){this.raiseEvent(i.TaskAreaStateEventNames.TASK_PROCESS_MOVE,null,null,{delta:e.x-this.currentPosition.x})||this.raiseEvent(i.TaskAreaStateEventNames.STATE_EXIT)},t}(n(6262).TaskDragBaseState);t.TaskMoveState=o},3461:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TaskAreaEventArgs=void 0;var r=n(2491),i=function(e,t,n,i){this.rowIndex=-1,this.info={},this.eventName=e,this.triggerEvent=t,(0,r.isDefined)(n)&&(this.rowIndex=n),i&&(this.info=i)};t.TaskAreaEventArgs=i},8269:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TaskAreaEventsListener=void 0;var r=n(8900),i=n(2491),o=n(6907),s=n(3714),a=n(2153),l=n(3336),c=n(1404),u=function(){function e(e){this._owner=e}return Object.defineProperty(e.prototype,"taskEditController",{get:function(){var e;return null===(e=this._owner)||void 0===e?void 0:e.taskEditController},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"renderHelper",{get:function(){var e;return null===(e=this._owner)||void 0===e?void 0:e.renderHelper},enumerable:!1,configurable:!0}),e.prototype.getHandler=function(e){return this.handlers[e]},e.prototype.setHandler=function(e,t){this.handlers[e]=t},Object.defineProperty(e.prototype,"handlers",{get:function(){var e;return null!==(e=this._handlers)&&void 0!==e||(this._handlers=this.createTaskAreaEventHandlers()),this._handlers},enumerable:!1,configurable:!0}),e.prototype.createTaskAreaEventHandlers=function(){var e={};return e[c.TaskAreaStateEventNames.TASK_AREA_CLICK]=this.taskAreaClickHandler.bind(this),e[c.TaskAreaStateEventNames.TASK_AREA_DBLCLICK]=this.taskAreaDblClickHandler.bind(this),e[c.TaskAreaStateEventNames.TASK_AREA_SCROLL]=this.taskAreaScrollHandler.bind(this),e[c.TaskAreaStateEventNames.TASK_AREA_START_MOVE]=this.taskAreaStartMoveHandler.bind(this),e[c.TaskAreaStateEventNames.TASK_AREA_PROCESS_MOVE]=this.taskAreaProcessMoveHandler.bind(this),e[c.TaskAreaStateEventNames.TASK_AREA_END_MOVE]=this.taskAreaEndMoveHandler.bind(this),e[c.TaskAreaStateEventNames.TASK_AREA_ZOOM_IN]=this.taskAreaZoomInHandler.bind(this),e[c.TaskAreaStateEventNames.TASK_AREA_ZOOM_OUT]=this.taskAreaZoomOutHandler.bind(this),e[c.TaskAreaStateEventNames.CONTEXTMENU_SHOW]=this.taskAreaContextMenuShowHandler.bind(this),e[c.TaskAreaStateEventNames.CONTEXTMENU_HIDE]=this.taskAreaContextMenuHideHandler.bind(this),e[c.TaskAreaStateEventNames.TASK_SELECTION]=this.taskSelectionHandler.bind(this),e[c.TaskAreaStateEventNames.DEPENDENCY_SELECTION]=this.dependencySelectionHandler.bind(this),e[c.TaskAreaStateEventNames.TASK_EDIT_START]=this.taskEditStartHandler.bind(this),e[c.TaskAreaStateEventNames.TASK_EDIT_END]=this.taskEditEndHandler.bind(this),e[c.TaskAreaStateEventNames.TASK_LEAVE]=this.taskLeaveHandler.bind(this),e[c.TaskAreaStateEventNames.TASK_PROCESS_MOVE]=this.taskProcessMoveHandler.bind(this),e[c.TaskAreaStateEventNames.TASK_END_MOVE]=this.taskEndMoveHandler.bind(this),e[c.TaskAreaStateEventNames.TASK_PROCESS_PROGRESS]=this.taskProcessProgressHandler.bind(this),e[c.TaskAreaStateEventNames.TASK_END_PROGRESS]=this.taskEndProgressHandler.bind(this),e[c.TaskAreaStateEventNames.TASK_PROCESS_START]=this.taskProcessStartHandler.bind(this),e[c.TaskAreaStateEventNames.TASK_CONFIRM_START]=this.taskConfirmStartHandler.bind(this),e[c.TaskAreaStateEventNames.TASK_PROCESS_END]=this.taskProcessEndHandler.bind(this),e[c.TaskAreaStateEventNames.TASK_CONFIRM_END]=this.taskConfirmEndHandler.bind(this),e[c.TaskAreaStateEventNames.GET_DEPENDENCY_POINTS]=this.getDependencyPoints.bind(this),e[c.TaskAreaStateEventNames.DEPENDENCY_START]=this.dependencyStartHandler.bind(this),e[c.TaskAreaStateEventNames.DEPENDENCY_END]=this.dependencyEndHandler.bind(this),e[c.TaskAreaStateEventNames.DEPENDENCY_PROCESS]=this.dependencyProcessHandler.bind(this),e[c.TaskAreaStateEventNames.TASK_AREA_KEY_DOWN]=this.onTaskAreaKeyDown.bind(this),e[c.TaskAreaStateEventNames.GET_COORDINATES_REF_POINT]=this.getCoordinatesRefPoint.bind(this),e[c.TaskAreaStateEventNames.STATE_EXIT]=function(){},e},e.prototype.taskAreaClickHandler=function(e){return this._owner.onTaskAreaClick(e.rowIndex,e.triggerEvent)},e.prototype.taskAreaDblClickHandler=function(e){this._owner.onTaskAreaDblClick(e.rowIndex,e.triggerEvent)},e.prototype.taskSelectionHandler=function(e){this._owner.onTaskSelectionChanged(e.rowIndex,e.triggerEvent)},e.prototype.taskAreaContextMenuShowHandler=function(e){this._owner.onTaskAreaContextMenu(e.rowIndex,e.triggerEvent,e.info.type)},e.prototype.taskAreaContextMenuHideHandler=function(){this._owner.hidePopupMenu()},e.prototype.taskAreaScrollHandler=function(e){this._owner.updateView()},e.prototype.dependencySelectionHandler=function(e){var t=e.info.key,n=this.taskEditController.dependencyId;(t!==n||!t&&(0,i.isDefined)(n))&&this._owner.selectDependency(t)},Object.defineProperty(e.prototype,"ganttMovingHelper",{get:function(){var e;return null!==(e=this._ganttMovingHelper)&&void 0!==e||(this._ganttMovingHelper=new l.GanttMovingHelper(this._owner.renderHelper.taskAreaContainer)),this._ganttMovingHelper},enumerable:!1,configurable:!0}),e.prototype.taskAreaStartMoveHandler=function(e){this.ganttMovingHelper.startMoving(e.triggerEvent)},e.prototype.taskAreaProcessMoveHandler=function(e){this.ganttMovingHelper.movingInfo&&(this.ganttMovingHelper.onMouseMove(e.triggerEvent),e.triggerEvent.preventDefault())},e.prototype.taskAreaEndMoveHandler=function(e){this.ganttMovingHelper.onMouseUp(e.triggerEvent)},e.prototype.taskAreaZoomInHandler=function(e){this._owner.zoomIn(e.info.leftPos)},e.prototype.taskAreaZoomOutHandler=function(e){this._owner.zoomOut(e.info.leftPos)},e.prototype.getCoordinatesRefPoint=function(e){var t=o.DomUtils.getAbsolutePositionX(this.renderHelper.taskAreaContainer.getElement()),n=o.DomUtils.getAbsolutePositionY(this.renderHelper.taskAreaContainer.getElement());return new r.Point(t,n)},e.prototype.taskEditStartHandler=function(e){this.taskEditController.show(e.rowIndex),this.taskEditController.showTaskInfo(s.EvtUtils.getEventX(e.triggerEvent))},e.prototype.taskLeaveHandler=function(e){this.taskEditController.cancel()},e.prototype.taskEditEndHandler=function(e){this.taskEditController.endEditing()},e.prototype.taskEndMoveHandler=function(e){this.taskEditController.confirmMove()},e.prototype.taskProcessMoveHandler=function(e){var t;return this.taskEditController.processMove(null!==(t=e.info.delta)&&void 0!==t?t:0)},e.prototype.taskEndProgressHandler=function(e){this.taskEditController.confirmProgress()},e.prototype.taskProcessProgressHandler=function(e){this.taskEditController.processProgress(e.info.position)},e.prototype.taskProcessStartHandler=function(e){this.taskEditController.processStart(e.info.position)},e.prototype.taskConfirmStartHandler=function(e){this.taskEditController.confirmStart()},e.prototype.taskProcessEndHandler=function(e){this.taskEditController.processEnd(e.info.position)},e.prototype.taskConfirmEndHandler=function(e){this.taskEditController.confirmEnd()},e.prototype.getDependencyPoints=function(e){var t={};return t.successorStart=new r.Point(o.DomUtils.getAbsolutePositionX(this.taskEditController.dependencySuccessorStart)+this.taskEditController.dependencySuccessorStart.clientWidth/2,o.DomUtils.getAbsolutePositionY(this.taskEditController.dependencySuccessorStart)+this.taskEditController.dependencySuccessorStart.clientHeight/2),t.successorFinish=new r.Point(o.DomUtils.getAbsolutePositionX(this.taskEditController.dependencySuccessorFinish)+this.taskEditController.dependencySuccessorFinish.clientWidth/2,o.DomUtils.getAbsolutePositionY(this.taskEditController.dependencySuccessorFinish)+this.taskEditController.dependencySuccessorFinish.clientHeight/2),t},e.prototype.dependencyStartHandler=function(e){this.taskEditController.startDependency(e.info.pos)},e.prototype.dependencyEndHandler=function(e){this.taskEditController.endDependency(e.info.type)},e.prototype.dependencyProcessHandler=function(e){this.taskEditController.processDependency(e.info.pos),this._owner.viewModel.tasks.items[e.rowIndex]&&this.taskEditController.showDependencySuccessor(e.rowIndex)},e.prototype.onTaskAreaKeyDown=function(e){var t=e.info.code;t==(a.ModifierKey.Ctrl|a.KeyCode.Key_z)&&this._owner.history.undo(),t==(a.ModifierKey.Ctrl|a.KeyCode.Key_y)&&this._owner.history.redo(),t==a.KeyCode.Delete&&this.taskEditController.deleteSelectedDependency()},e}();t.TaskAreaEventsListener=u},3682:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TaskAreaManager=t.TOUCH_ACTION_NONE=void 0;var r=n(9279),i=n(6907),o=n(3714),s=n(6958);t.TOUCH_ACTION_NONE="dx-gantt-touch-action";var a=function(){function e(e,t,n){this._eventListener=e,this._taskArea=t,this._cellSize=n,this.attachEvents()}return e.prototype.attachEvents=function(){window.PointerEvent?this.attachPointerEvents():r.Browser.TouchUI?this.attachTouchEvents():this.attachMouseEvents(),this.attachCommonEvents()},e.prototype.detachEvents=function(){window.PointerEvent?this.detachPointerEvents():r.Browser.TouchUI?this.detachTouchEvents():this.detachMouseEvents(),this.detachCommonEvents()},e.prototype.attachEventsOnTask=function(e){window.PointerEvent?null==e||e.addEventListener("pointerenter",this.onTaskPointerEnterHandler):r.Browser.TouchUI?null==e||e.addEventListener("touchstart",this.onTaskTouchStartHandler):(null==e||e.addEventListener("mouseenter",this.onTaskMouseEnterHandler),null==e||e.addEventListener("mouseleave",this.onTaskMouseLeaveHandler))},e.prototype.detachEventsOnTask=function(e){window.PointerEvent?null==e||e.removeEventListener("pointerenter",this.onTaskPointerEnterHandler):r.Browser.TouchUI?null==e||e.removeEventListener("touchstart",this.onTaskTouchStartHandler):(null==e||e.removeEventListener("mouseenter",this.onTaskMouseEnterHandler),null==e||e.removeEventListener("mouseleave",this.onTaskMouseLeaveHandler))},e.prototype.attachCommonEvents=function(){this.taskAreaAddEventListener("contextmenu",this.onContextMenuHandler),this.taskAreaAddEventListener(o.EvtUtils.getMouseWheelEventName(),this.onMouseWheelHandler),this.taskAreaAddEventListener("scroll",this.onScrollHandler),document.addEventListener("keydown",this.onKeyDownHandler)},e.prototype.detachCommonEvents=function(){this.taskAreaRemoveEventListener("contextmenu",this.onContextMenuHandler),this.taskAreaRemoveEventListener(o.EvtUtils.getMouseWheelEventName(),this.onMouseWheelHandler),this.taskAreaRemoveEventListener("scroll",this.onScrollHandler),document.removeEventListener("keydown",this.onKeyDownHandler)},e.prototype.attachPointerEvents=function(){i.DomUtils.addClassName(this.taskArea,t.TOUCH_ACTION_NONE),document.addEventListener("pointerdown",this.onDocumentPointerDownHandler),document.addEventListener("pointerup",this.onDocumentPointerUpHandler),document.addEventListener("pointermove",this.onDocumentPointerMoveHandler),document.addEventListener("pointercancel",this.onDocumentPointerCancelUpHandler),this.taskAreaAddEventListener("pointerleave",this.onTaskAreaPointerLeaveHandler)},e.prototype.detachPointerEvents=function(){document.removeEventListener("pointerdown",this.onDocumentPointerDownHandler),document.removeEventListener("pointerup",this.onDocumentPointerUpHandler),document.removeEventListener("pointermove",this.onDocumentPointerMoveHandler),document.removeEventListener("pointercancel ",this.onDocumentPointerCancelUpHandler),this.taskAreaRemoveEventListener("pointerleave",this.onTaskAreaPointerLeaveHandler),i.DomUtils.removeClassName(this.taskArea,t.TOUCH_ACTION_NONE)},e.prototype.attachTouchEvents=function(){i.DomUtils.addClassName(this.taskArea,t.TOUCH_ACTION_NONE),document.addEventListener("touchstart",this.onTouchStartHandler),document.addEventListener("touchend",this.onTouchEndHandler),document.addEventListener("touchmove",this.onTouchMoveHandler)},e.prototype.detachTouchEvents=function(){document.removeEventListener("touchstart",this.onTouchStartHandler),document.removeEventListener("touchend",this.onTouchEndHandler),document.removeEventListener("touchmove",this.onTouchMoveHandler),i.DomUtils.removeClassName(this.taskArea,t.TOUCH_ACTION_NONE)},e.prototype.attachMouseEvents=function(){this.taskAreaAddEventListener("click",this.onMouseClickHandler),this.taskAreaAddEventListener("dblclick",this.onMouseDblClickHandler),this.taskAreaAddEventListener("mousedown",this.onMouseDownHandler),this.taskAreaAddEventListener("mouseleave",this.onTaskAreaMouseLeaveHandler),document.addEventListener("mousemove",this.onMouseMoveHandler),document.addEventListener("mouseup",this.onMouseUpHandler)},e.prototype.detachMouseEvents=function(){this.taskAreaRemoveEventListener("click",this.onMouseClickHandler),this.taskAreaRemoveEventListener("dblclick",this.onMouseDblClickHandler),this.taskAreaRemoveEventListener("mouseleave",this.onTaskAreaMouseLeaveHandler),this.taskAreaRemoveEventListener("mousedown",this.onMouseDownHandler),document.removeEventListener("mousemove",this.onMouseMoveHandler),document.removeEventListener("mouseup",this.onMouseUpHandler)},Object.defineProperty(e.prototype,"stateController",{get:function(){var e;return null!==(e=this._stateController)&&void 0!==e||(this._stateController=new s.TaskAreaStateController(this._eventListener,this._taskArea,this._cellSize)),this._stateController},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"taskArea",{get:function(){return this._taskArea},enumerable:!1,configurable:!0}),e.prototype.taskAreaAddEventListener=function(e,t){this.taskArea.addEventListener(e,t)},e.prototype.taskAreaRemoveEventListener=function(e,t){this.taskArea.removeEventListener(e,t)},Object.defineProperty(e.prototype,"onContextMenuHandler",{get:function(){var e,t=this;return null!==(e=this._onContextMenuHandler)&&void 0!==e||(this._onContextMenuHandler=function(e){t.stateController.onContextMenu(e)}),this._onContextMenuHandler},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onMouseWheelHandler",{get:function(){var e,t=this;return null!==(e=this._onMouseWheelHandler)&&void 0!==e||(this._onMouseWheelHandler=function(e){t.stateController.onMouseWheel(e)}),this._onMouseWheelHandler},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onScrollHandler",{get:function(){var e,t=this;return null!==(e=this._onScrollHandler)&&void 0!==e||(this._onScrollHandler=function(e){t.stateController.onScroll(e)}),this._onScrollHandler},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onKeyDownHandler",{get:function(){var e,t=this;return null!==(e=this._onKeyDownHandler)&&void 0!==e||(this._onKeyDownHandler=function(e){t.stateController.onKeyDown(e)}),this._onKeyDownHandler},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onTaskPointerEnterHandler",{get:function(){var e,t=this;return null!==(e=this._onTaskPointerEnterHandler)&&void 0!==e||(this._onTaskPointerEnterHandler=function(e){t.stateController.onTaskPointerEnter(e)}),this._onTaskPointerEnterHandler},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onTaskAreaPointerLeaveHandler",{get:function(){var e,t=this;return null!==(e=this._onTaskAreaPointerLeaveHandler)&&void 0!==e||(this._onTaskAreaPointerLeaveHandler=function(e){t.stateController.onTaskAreaPointerLeave(e)}),this._onTaskAreaPointerLeaveHandler},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onDocumentPointerDownHandler",{get:function(){var e,t=this;return null!==(e=this._onDocumentPointerDownHandler)&&void 0!==e||(this._onDocumentPointerDownHandler=function(e){t.stateController.onDocumentPointerDown(e)}),this._onDocumentPointerDownHandler},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onDocumentPointerUpHandler",{get:function(){var e,t=this;return null!==(e=this._onDocumentPointerUpHandler)&&void 0!==e||(this._onDocumentPointerUpHandler=function(e){t.stateController.onDocumentPointerUp(e)}),this._onDocumentPointerUpHandler},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onDocumentPointerCancelUpHandler",{get:function(){var e,t=this;return null!==(e=this._onDocumentPointerCancelHandler)&&void 0!==e||(this._onDocumentPointerCancelHandler=function(e){t.stateController.onDocumentPointerCancel(e)}),this._onDocumentPointerCancelHandler},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onDocumentPointerMoveHandler",{get:function(){var e,t=this;return null!==(e=this._onDocumentPointerMoveHandler)&&void 0!==e||(this._onDocumentPointerMoveHandler=function(e){t.stateController.onDocumentPointerMove(e)}),this._onDocumentPointerMoveHandler},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onTouchStartHandler",{get:function(){var e,t=this;return null!==(e=this._onTouchStartHandler)&&void 0!==e||(this._onTouchStartHandler=function(e){t.stateController.onTouchStart(e)}),this._onTouchStartHandler},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onTouchEndHandler",{get:function(){var e,t=this;return null!==(e=this._onTouchEndHandler)&&void 0!==e||(this._onTouchEndHandler=function(e){t.stateController.onTouchEnd(e)}),this._onTouchEndHandler},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onTouchMoveHandler",{get:function(){var e,t=this;return null!==(e=this._onTouchMoveHandler)&&void 0!==e||(this._onTouchMoveHandler=function(e){t.stateController.onTouchMove(e)}),this._onTouchMoveHandler},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onTaskTouchStartHandler",{get:function(){var e,t=this;return null!==(e=this._onTaskTouchStartHandler)&&void 0!==e||(this._onTaskTouchStartHandler=function(e){t.stateController.onTaskTouchStart(e)}),this._onTaskTouchStartHandler},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onMouseClickHandler",{get:function(){var e,t=this;return null!==(e=this._onMouseClickHandler)&&void 0!==e||(this._onMouseClickHandler=function(e){t.stateController.onClick(e)}),this._onMouseClickHandler},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onMouseDblClickHandler",{get:function(){var e,t=this;return null!==(e=this._onMouseDblClickHandler)&&void 0!==e||(this._onMouseDblClickHandler=function(e){t.stateController.onDblClick(e)}),this._onMouseDblClickHandler},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onMouseDownHandler",{get:function(){var e,t=this;return null!==(e=this._onMouseDownHandler)&&void 0!==e||(this._onMouseDownHandler=function(e){t.stateController.onMouseDown(e)}),this._onMouseDownHandler},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onTaskAreaMouseLeaveHandler",{get:function(){var e,t=this;return null!==(e=this._onTaskAreaMouseLeaveHandler)&&void 0!==e||(this._onTaskAreaMouseLeaveHandler=function(e){t.stateController.onTaskAreaMouseLeave(e)}),this._onTaskAreaMouseLeaveHandler},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onMouseMoveHandler",{get:function(){var e,t=this;return null!==(e=this._onMouseMoveHandler)&&void 0!==e||(this._onMouseMoveHandler=function(e){t.stateController.onMouseMove(e)}),this._onMouseMoveHandler},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onMouseUpHandler",{get:function(){var e,t=this;return null!==(e=this._onMouseUpHandler)&&void 0!==e||(this._onMouseUpHandler=function(e){t.stateController.onMouseUp(e)}),this._onMouseUpHandler},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onTaskMouseEnterHandler",{get:function(){var e,t=this;return null!==(e=this._onTaskMouseEnterHandler)&&void 0!==e||(this._onTaskMouseEnterHandler=function(e){t.stateController.onTaskElementHover(e)}),this._onTaskMouseEnterHandler},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onTaskMouseLeaveHandler",{get:function(){var e,t=this;return null!==(e=this._onTaskMouseLeaveHandler)&&void 0!==e||(this._onTaskMouseLeaveHandler=function(e){t.stateController.onTaskElementLeave(e)}),this._onTaskMouseLeaveHandler},enumerable:!1,configurable:!0}),e.DBLCLICK_INTERVAL=300,e.MS_POINTER_ACTIVE_CLASS="ms-pointer-active",e}();t.TaskAreaManager=a},2366:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.GanttView=void 0;var r=n(639),i=n(9279),o=n(7156),s=n(1284),a=n(858),l=n(7880),c=n(9201),u=n(9954),d=n(8695),p=n(6893),h=n(8725),f=n(2491),g=n(3452),y=n(9650),m=n(2978),v=n(1074),T=n(5351),b=n(6353),k=n(9057),S=n(5098),_=n(9640),w=n(8478),E=n(5846),D=n(2449),I=n(1408),C=n(6626),P=n(3714),M=n(6907),A=n(8900),O=n(8269),x=n(4730),R=n(6124),L=function(){function e(e,t,n){var i=this;this.currentSelectedTaskID="",this.isFocus=!1,this._updateWithModelReloadLockedCounter=0,this.scaleCount=2,this.tickSize=new b.Size(0,0),this.currentZoom=1,this.stripLinesUpdaterId=null,this.ganttOwner=t,this.settings=T.Settings.parse(n),this.initValidationController(),this.renderHelper=new v.RenderHelper(this),this.renderHelper.initMarkup(e),this.loadOptionsFromGanttOwner(),this.renderHelper.init(this.tickSize,this.range,this.settings.viewType,this.viewModel,this.settings.firstDayOfWeek),this.commandManager=new o.CommandManager(this),this.barManager=new r.BarManager(this.commandManager,this.ganttOwner.bars),this.initTaskEditController(),this.history=new h.History(this._getHistoryListener()),this.initFullScreenModeHelper(),this.updateView(),this._scrollTimeOut=setTimeout((function(){i.scrollLeftByViewType()}),0),this.initializeStripLinesUpdater(),this.initGanttViewApi()}return e.prototype.initGanttViewApi=function(){this.ganttViewApi=new C.GanttViewApi(this)},e.prototype._getHistoryListener=function(){return{onTransactionStart:this.onHistoryTransactionStart.bind(this),onTransactionEnd:this.onHistoryTransactionEnd.bind(this)}},e.prototype.onHistoryTransactionStart=function(){this.lockUpdateWithReload()},e.prototype.onHistoryTransactionEnd=function(){this.unlockUpdateWithReload()},e.prototype.lockUpdateWithReload=function(){this._updateWithModelReloadLockedCounter++},e.prototype.unlockUpdateWithReload=function(){this._updateWithModelReloadLockedCounter--,0===this._updateWithModelReloadLockedCounter&&this._pendingUpdateInfo&&(this.updateWithDataReload(this._pendingUpdateInfo.keepExpandState),this._pendingUpdateInfo=null)},e.prototype.initValidationController=function(){var e=this,t=E.ValidationControllerSettings.parse({getViewModel:function(){return e.viewModel},getHistory:function(){return e.history},getModelManipulator:function(){return e.modelManipulator},getRange:function(){return e.range},getValidationSettings:function(){return e.settings.validation},updateOwnerInAutoParentMode:function(){e.updateOwnerInAutoParentMode()},getIsValidateDependenciesRequired:function(){return e.isValidateDependenciesRequired()}});this.validationController=new w.ValidationController(t)},e.prototype.initTaskEditController=function(){var e=this,t=_.TaskEditSettings.parse({destroyTemplate:function(t){e.destroyTemplate(t)},formatDate:function(t){return e.getDateFormat(t)},getRenderHelper:function(){return e.renderHelper},getGanttSettings:function(){return e.settings},getViewModel:function(){return e.viewModel},getCommandManager:function(){return e.commandManager},getModelManipulator:function(){return e.modelManipulator},getValidationController:function(){return e.validationController}});this.taskEditController=new S.TaskEditController(t)},Object.defineProperty(e.prototype,"taskAreaEventsListener",{get:function(){var e;return null!==(e=this._taskAreaEventsListener)&&void 0!==e||(this._taskAreaEventsListener=new O.TaskAreaEventsListener(this)),this._taskAreaEventsListener},enumerable:!1,configurable:!0}),e.prototype.initFullScreenModeHelper=function(){var e=this,t=u.FullScreenHelperSettings.parse({getMainElement:function(){return e.getOwnerControlMainElement()},adjustControl:function(){e.adjustOwnerControl()}});this.fullScreenModeHelper=new d.FullScreenModeHelper(t)},e.prototype.getDateRange=function(e,t){var n=this.getVisibleAreaTime(),r=this.settings.startDateRange||c.DateUtils.adjustStartDateByViewType(new Date(e.getTime()-n),this.settings.viewType,this.settings.firstDayOfWeek),i=this.settings.endDateRange||c.DateUtils.adjustEndDateByViewType(new Date(t.getTime()+n),this.settings.viewType,this.settings.firstDayOfWeek);return this.settings.startDateRange&&r>i?i=r:this.settings.endDateRange&&r>i&&(r=i),new a.DateRange(r,i)},e.prototype.getVisibleAreaTime=function(){return Math.ceil(this.renderHelper.getTaskAreaContainerWidth()/this.tickSize.width)*c.DateUtils.getTickTimeSpan(this.settings.viewType)},e.prototype.zoomIn=function(e){void 0===e&&(e=this.renderHelper.getTaskAreaContainerWidth()/2),this.ganttViewApi.zoomIn(e)},e.prototype.zoomOut=function(e){void 0===e&&(e=this.renderHelper.getTaskAreaContainerWidth()/2),this.ganttViewApi.zoomOut(e)},e.prototype.scrollToDate=function(e){if(e){var t=e instanceof Date?c.DateUtils.getOrCreateUTCDate(e):c.DateUtils.parse(e);this.scrollToDateCore(t,0)}},e.prototype.showDialog=function(e,t,n,r){this.ganttOwner.showDialog(e,t,n,r)},e.prototype.showPopupMenu=function(e){this.ganttOwner.showPopupMenu(e)},e.prototype.hidePopupMenu=function(){this.ganttOwner.hidePopupMenu&&this.ganttOwner.hidePopupMenu()},e.prototype.collapseAll=function(){this.ganttOwner.collapseAll()},e.prototype.expandAll=function(){this.ganttOwner.expandAll()},e.prototype.onGanttViewContextMenu=function(e,t,n){return this.ganttOwner.onGanttViewContextMenu(e,t,n)},e.prototype.changeGanttTaskSelection=function(e,t){this.ganttOwner.changeGanttTaskSelection(e,t)},e.prototype.hideTaskEditControl=function(){this.taskEditController.hide()},e.prototype.scrollLeftByViewType=function(){var e=c.DateUtils.roundStartDate(this.dataRange.start,this.settings.viewType);this.scrollToDateCore(e,1)},e.prototype.scrollToDateCore=function(e,t){this.renderHelper.setTaskAreaContainerScrollLeftToDate(e,t)},e.prototype.onVisualModelChanged=function(){this.resetAndUpdate()},e.prototype.initializeStripLinesUpdater=function(){var e=this;this.settings.stripLines.showCurrentTime&&(this.stripLinesUpdaterId=setInterval((function(){e.renderHelper.recreateStripLines()}),Math.max(this.settings.stripLines.currentTimeUpdateInterval,100)))},e.prototype.clearStripLinesUpdater=function(){this.stripLinesUpdaterId&&clearInterval(this.stripLinesUpdaterId),this.stripLinesUpdaterId=null},e.prototype.getGanttViewStartDate=function(e){if(!e)return new Date;var t=e.map((function(e){return"string"==typeof e.start?new Date(e.start):e.start})).filter((function(e){return(0,f.isDefined)(e)}));return t.length>0?t.reduce((function(e,t){return t<e?t:e}),t[0]):new Date},e.prototype.getGanttViewEndDate=function(e){if(!e)return new Date;var t=e.map((function(e){return"string"==typeof e.end?new Date(e.end):e.end})).filter((function(e){return(0,f.isDefined)(e)}));return t.length>0?t.reduce((function(e,t){return t>e?t:e}),t[0]):new Date},e.prototype.getTask=function(e){var t=this.getViewItem(e);return null==t?void 0:t.task},e.prototype.getViewItem=function(e){var t;return null===(t=this.viewModel)||void 0===t?void 0:t.items[e]},e.prototype.isValidateDependenciesRequired=function(){return this.settings.validation.validateDependencies&&this.settings.showDependencies},e.prototype.updateTickSizeWidth=function(){this.tickSize.width=this.renderHelper.etalonScaleItemWidths*this.currentZoom},e.prototype.updateView=function(){this.onBeginUpdateView(),this.renderHelper.setTimeScaleContainerScrollLeft(this.taskAreaContainerScrollLeft),this.processScroll(!1),this.processScroll(!0),this.ganttOwner.onGanttScroll(this.taskAreaContainerScrollTop),this.onEndUpdateView()},e.prototype.onBeginUpdateView=function(){this[e.taskAreaScrollTopKey]=this.renderHelper.taskAreaContainerScrollTop,this[e.taskAreaScrollLeftKey]=this.renderHelper.taskAreaContainerScrollLeft},e.prototype.onEndUpdateView=function(){delete this[e.taskAreaScrollTopKey],delete this[e.taskAreaScrollLeftKey],delete this[e.taskTextHeightKey]},Object.defineProperty(e.prototype,"taskAreaContainerScrollTop",{get:function(){var t;return null!==(t=this[e.taskAreaScrollTopKey])&&void 0!==t?t:this.renderHelper.taskAreaContainerScrollTop},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"taskAreaContainerScrollLeft",{get:function(){var t;return null!==(t=this[e.taskAreaScrollLeftKey])&&void 0!==t?t:this.renderHelper.taskAreaContainerScrollLeft},enumerable:!1,configurable:!0}),e.prototype.processScroll=function(e){this.hideTaskEditControl(),this.renderHelper.processScroll(e)},e.prototype.allowTaskAreaBorders=function(e){return e?this.settings.areHorizontalBordersEnabled:this.settings.areVerticalBordersEnabled},e.prototype.getScaleItemText=function(e,t){return this.renderHelper.getScaleItemText(e,t)},e.prototype.getTaskText=function(e){return this.renderHelper.getTaskText(e)},e.prototype.rowHasChildren=function(e){var t=this.getViewItem(e);return(null==t?void 0:t.children.length)>0},e.prototype.rowHasSelection=function(e){var t=this.getViewItem(e);return null==t?void 0:t.selected},e.prototype.getAllVisibleTaskIndices=function(e,t){return this.viewModel.getAllVisibleTaskIndices(e,t)},e.prototype.getVisibleDependencyKeysByTaskRange=function(e){if(!this.settings.showDependencies)return[];var t=this.viewModel,n=e.map((function(e){return t.tasks.items[e].internalId}));return t.dependencies.items.filter((function(e){return n.indexOf(e.successorId)>-1||n.indexOf(e.predecessorId)>-1})).map((function(e){return e.internalId}))},e.prototype.getTreeListTableStyle=function(){var e,t;return null===(t=(e=this.ganttOwner).getTreeListTableStyle)||void 0===t?void 0:t.call(e)},e.prototype.getTreeListColCount=function(){var e,t;return null===(t=(e=this.ganttOwner).getTreeListColCount)||void 0===t?void 0:t.call(e)},e.prototype.getTreeListHeaderInfo=function(e){var t,n;return null===(n=(t=this.ganttOwner).getTreeListHeaderInfo)||void 0===n?void 0:n.call(t,e)},e.prototype.getTreeListCellInfo=function(e,t,n){var r,i;return null===(i=(r=this.ganttOwner).getTreeListCellInfo)||void 0===i?void 0:i.call(r,e,t,n)},e.prototype.getTreeListEmptyDataCellInfo=function(){var e,t;return null===(t=(e=this.ganttOwner).getTreeListEmptyDataCellInfo)||void 0===t?void 0:t.call(e)},e.prototype.exportToPdf=function(e){var t;return null!==(t=e.docCreateMethod)&&void 0!==t||(e.docCreateMethod=this.getDefaultPdfDocCreateMethod()),new m.PdfGanttExporter(new p.GanttExportCalculator(this,e)).export()},e.prototype.getDefaultPdfDocCreateMethod=function(){var e;return null===(e=window.jspdf)||void 0===e?void 0:e.jsPDF},e.prototype.getTaskDependencies=function(e){return this.viewModel.dependencies.items.filter((function(t){return t.predecessorId==e||t.successorId==e}))},e.prototype.isHighlightRowElementAllowed=function(e){var t=this.getViewItem(e);return e%2!=0&&this.settings.areAlternateRowsEnabled||(null==t?void 0:t.children.length)>0},e.prototype.calculateAutoViewType=function(e,t){var n=(t.getTime()-e.getTime())/36e5;return n>8760?D.ViewType.Years:n>720?D.ViewType.Months:n>168?D.ViewType.Weeks:n>24?D.ViewType.Days:n>6?D.ViewType.SixHours:n>1?D.ViewType.Hours:D.ViewType.TenMinutes},e.prototype.getExternalTaskAreaContainer=function(e){return this.ganttOwner.getExternalTaskAreaContainer(e)},e.prototype.prepareExternalTaskAreaContainer=function(e,t){return this.ganttOwner.prepareExternalTaskAreaContainer(e,t)},e.prototype.getHeaderHeight=function(){return this.ganttOwner.getHeaderHeight()},e.prototype.changeTaskExpanded=function(e,t){var n=this.getTaskByPublicId(e);n&&this.viewModel.changeTaskExpanded(n.internalId,t)},e.prototype.expandTask=function(e){this.viewModel.changeTaskExpanded(e,!0)},e.prototype.collapseTask=function(e){this.viewModel.changeTaskExpanded(e,!1)},e.prototype.showTask=function(e){this.viewModel.changeTaskVisibility(e,!0)},e.prototype.hideTask=function(e){this.viewModel.changeTaskVisibility(e,!1)},e.prototype.getTaskVisibility=function(e){return this.viewModel.getTaskVisibility(e)},e.prototype.unselectCurrentSelectedTask=function(){this.unselectTask(this.currentSelectedTaskID)},e.prototype.getTaskSelected=function(e){return this.viewModel.getTaskSelected(e)},e.prototype.setViewType=function(e,t){void 0===t&&(t=!0),this.ganttViewApi.setViewType(e,t)},e.prototype.setViewTypeRange=function(e,t){this.ganttViewApi.setViewTypeRange(e,t)},e.prototype.setTaskTitlePosition=function(e){this.settings.taskTitlePosition!==e&&(this.settings.taskTitlePosition=e,this.resetAndUpdate())},e.prototype.setShowResources=function(e){this.settings.showResources!==e&&(this.settings.showResources=e,this.resetAndUpdate())},e.prototype.toggleResources=function(){this.setShowResources(!this.settings.showResources)},e.prototype.setShowDependencies=function(e){this.settings.showDependencies!==e&&(this.settings.showDependencies=e,this.resetAndUpdate())},e.prototype.toggleDependencies=function(){this.setShowDependencies(!this.settings.showDependencies)},e.prototype.setFirstDayOfWeek=function(e){this.settings.firstDayOfWeek!==e&&(this.settings.firstDayOfWeek=e,this.resetAndUpdate())},e.prototype.setStartDateRange=function(e){l.DateTimeUtils.areDatesEqual(this.settings.startDateRange,e)||(this.settings.startDateRange=new Date(e),this.resetAndUpdate())},e.prototype.setEndDateRange=function(e){l.DateTimeUtils.areDatesEqual(this.settings.endDateRange,e)||(this.settings.endDateRange=new Date(e),this.resetAndUpdate())},e.prototype.loadOptionsFromGanttOwner=function(){var e,t=this;this.tickSize.height=this.ganttOwner.getRowHeight();var n=this.ganttOwner.getGanttTasksData();this.dataRange=new a.DateRange(this.getGanttViewStartDate(n),this.getGanttViewEndDate(n)),null==this.settings.viewType&&(this.settings.viewType=this.calculateAutoViewType(this.dataRange.start,this.dataRange.end)),this.updateTickSizeWidth(),this.range=this.getDateRange(this.dataRange.start,this.dataRange.end),this.dispatcher=new g.ModelChangesDispatcher;var r=this.ganttOwner.getModelChangesListener();r&&this.dispatcher.onModelChanged.add(r),this.viewModel=new I.ViewVisualModel(this,n,this.ganttOwner.getGanttDependenciesData(),this.ganttOwner.getGanttResourcesData(),this.ganttOwner.getGanttResourceAssignmentsData(),this.range,this.ganttOwner.getGanttWorkTimeRules()),this.modelManipulator=new y.ModelManipulator(this.viewModel,this.dispatcher),null===(e=this.history)||void 0===e||e.historyItems.forEach((function(e){return e.setModelManipulator(t.modelManipulator)}))},e.prototype.resetAndUpdate=function(){this.range=this.getDateRange(this.dataRange.start,this.dataRange.end),this.viewModel.updateRange(this.range),this.renderHelper.resetAndUpdate(this.tickSize,this.range,this.settings.viewType,this.viewModel,this.settings.firstDayOfWeek),i.Browser.IE&&this.taskEditController.createElements(),this.updateView()},e.prototype.cleanMarkup=function(){this.setNormalScreenMode(),this.renderHelper.taskAreaManagerDetachEvents(),this.taskEditController.detachEvents(),this.clearStripLinesUpdater(),this.renderHelper.reset(),clearTimeout(this._scrollTimeOut)},e.prototype.checkAndProcessModelChanges=function(){var e=this.ganttOwner.getGanttTasksData(),t=this.viewModel.refreshTaskDataIfRequires(e);return t&&this.resetAndUpdate(),t},e.prototype.updateHistoryObsoleteInsertedKey=function(e,t,n){var r;null===(r=this.history)||void 0===r||r.updateObsoleteInsertedKey(e,t,n),n===R.GanttDataObjectNames.dependency&&this.renderHelper.updateRenderedConnectorLinesId(e,t)},e.prototype.updateRowHeights=function(e){if(this.tickSize.height!==e){this.tickSize.height=e;var t=this.renderHelper.getTaskAreaContainerScrollLeft();this.resetAndUpdate(),this.renderHelper.setTaskAreaContainerScrollLeft(t)}},e.prototype.selectTask=function(e){this.selectDependency(null),this.viewModel.changeTaskSelected(e,!0),this.currentSelectedTaskID=e,this.updateBarManager()},e.prototype.unselectTask=function(e){this.viewModel.changeTaskSelected(e,!1),this.updateBarManager()},e.prototype.selectTaskById=function(e){this.unselectCurrentSelectedTask();var t=this.getTaskByPublicId(e);t&&this.selectTask(t.internalId)},e.prototype.selectDependency=function(e){this.taskEditController.selectDependency(e),this.renderHelper.createConnectorLines()},e.prototype.getTaskAreaContainer=function(){return this.renderHelper.taskAreaContainer},e.prototype.setWidth=function(e){this.renderHelper.setMainElementWidth(e)},e.prototype.setHeight=function(e){this.renderHelper.setMainElementHeight(e)},e.prototype.setAllowSelection=function(e){this.settings.allowSelectTask=e},e.prototype.setEditingSettings=function(e){this.settings.editing=e,this.updateBarManager()},e.prototype.setValidationSettings=function(e){this.settings.validation=e},e.prototype.setRowLinesVisible=function(e){this.settings.areHorizontalBordersEnabled=e,this.renderHelper.prepareTaskAreaContainer(),this.resetAndUpdate()},e.prototype.setStripLines=function(e){this.settings.stripLines=k.StripLineSettings.parse(e),this.clearStripLinesUpdater(),this.initializeStripLinesUpdater(),this.renderHelper.recreateStripLines()},e.prototype.deleteTask=function(e){var t=this.getTaskByPublicId(e.toString());t&&this.commandManager.removeTaskCommand.execute(t.internalId,!1,!0)},e.prototype.insertTask=function(e){if(e){var t=null!=e.parentId?String(e.parentId):null,n=this.getTaskByPublicId(t),r=this.viewModel.getRootTaskId(),i="string"==typeof e.start?new Date(e.start):e.start,o="string"==typeof e.end?new Date(e.end):e.end,s={parentId:r&&t===r?t:null==n?void 0:n.internalId,title:e.title,start:i,end:o,progress:parseInt(e.progress)||0,color:e.color};if(this.commandManager.createTaskCommand.execute(s))return this.getLastInsertedTaskId()}return""},e.prototype.updateTask=function(e,t){var n=this.getTaskByPublicId(e.toString()),r=this._getTaskDataForUpdate(t,n);r&&this.commandManager.updateTaskCommand.execute(n.internalId,r)},e.prototype.getTaskData=function(e){var t=this.getTaskByPublicId(e.toString());if(t)return this.viewModel.getTaskObjectForDataSource(t)},e.prototype.insertDependency=function(e){if(e){var t=String(e.predecessorId),n=this.getTaskByPublicId(t),r=String(e.successorId),i=this.getTaskByPublicId(r),o=e.type;n&&i&&this.validationController.canCreateDependency(t,r)&&this.commandManager.createDependencyCommand.execute(n.internalId,i.internalId,o)}},e.prototype.deleteDependency=function(e){var t=this.viewModel.convertPublicToInternalKey("dependency",e);(0,f.isDefined)(t)&&this.commandManager.removeDependencyCommand.execute(t)},e.prototype.getDependencyData=function(e){return this.viewModel.getDependencyObjectForDataSource(e)},e.prototype.insertResource=function(e,t){var n=this;if(e){this.commandManager.createResourceCommand.execute(String(e.text),e.color&&String(e.color),(function(e){if((0,f.isDefined)(t))for(var r=0;r<t.length;r++)n.assignResourceToTask(e,t[r])}))}},e.prototype.deleteResource=function(e){var t=this.viewModel.convertPublicToInternalKey("resource",e);(0,f.isDefined)(t)&&this.commandManager.removeResourceCommand.execute(t)},e.prototype.assignResourceToTask=function(e,t){var n=this.viewModel.convertPublicToInternalKey("resource",e),r=this.viewModel.convertPublicToInternalKey("task",t);(0,f.isDefined)(n)&&(0,f.isDefined)(r)&&this.commandManager.assignResourceCommand.execute(n,r)},e.prototype.unassignResourceFromTask=function(e,t){var n=this.viewModel.findAssignment(e,t);n&&this.commandManager.deassignResourceCommand.execute(n.internalId)},e.prototype.unassignAllResourcesFromTask=function(e){var t=this,n=this.viewModel.convertPublicToInternalKey("task",e);this.viewModel.findAllTaskAssignments(n).forEach((function(e){return t.commandManager.deassignResourceCommand.execute(e.internalId)}))},e.prototype.getResourceData=function(e){return this.viewModel.getResourceObjectForDataSource(e)},e.prototype.getResourceAssignmentData=function(e){return this.viewModel.getResourceAssignmentObjectForDataSource(e)},e.prototype.getTaskResources=function(e){var t=this.viewModel,n=t.getItemByPublicId("task",e);return n&&t.getAssignedResources(n).items||[]},e.prototype.getVisibleTaskKeys=function(){return this.viewModel.getVisibleTasks().map((function(e){return e.id}))},e.prototype.getVisibleDependencyKeys=function(){return this.viewModel.getVisibleDependencies().map((function(e){return e.id}))},e.prototype.getVisibleResourceKeys=function(){return this.viewModel.getVisibleResources().map((function(e){return e.id}))},e.prototype.getVisibleResourceAssignmentKeys=function(){return this.viewModel.getVisibleResourceAssignments().map((function(e){return e.id}))},e.prototype.getTasksExpandedState=function(){return this.viewModel.getTasksExpandedState()},e.prototype.applyTasksExpandedState=function(e){this.viewModel.applyTasksExpandedState(e)},e.prototype.updateWithDataReload=function(e){if(this._updateWithModelReloadLockedCounter>0)this._pendingUpdateInfo={keepExpandState:e};else{var t=e&&this.getTasksExpandedState();this.loadOptionsFromGanttOwner(),e?this.applyTasksExpandedState(t):this.resetAndUpdate();var n=x.DialogBase.activeInstance;n&&n.canRefresh&&"TaskEdit"===n.getDialogName()&&n.refresh(),this.dispatcher.notifyGanttViewUpdated()}},e.prototype.onBrowserWindowResize=function(){this.fullScreenModeHelper.isInFullScreenMode?this.fullScreenModeHelper.adjustControlInFullScreenMode():this.adjustOwnerControl()},e.prototype.getTaskTreeLine=function(e){return this.viewModel.getTaskTreeLine(e).reverse()},e.prototype.isInFullScreenMode=function(){var e;return!!(null===(e=this.fullScreenModeHelper)||void 0===e?void 0:e.isInFullScreenMode)},e.prototype.setFullScreenMode=function(){this.isInFullScreenMode()||this.fullScreenModeHelper.toggle()},e.prototype.setNormalScreenMode=function(){this.isInFullScreenMode()&&this.fullScreenModeHelper.toggle()},e.prototype.setTaskValue=function(e,t,n){var r=this.commandManager.updateTaskCommand,i=this.getTaskByPublicId(e),o={};return i&&("title"===t&&(o[t]=n||""),"progress"===t&&(o[t]=n),"start"===t&&(o[t]=l.DateTimeUtils.getMinDate(n,i.end)),"end"===t&&(o[t]=l.DateTimeUtils.getMaxDate(n,i.start))),Object.keys(o).length>0&&r.execute(i.internalId,o)},e.prototype.getLastInsertedTaskId=function(){var e=this.history.historyItems.filter((function(e){return e instanceof s.CreateTaskHistoryItem})),t=e[e.length-1];return t&&t.insertedKey},e.prototype.getTaskByPublicId=function(e){return this.viewModel.tasks.getItemByPublicId(e)},e.prototype.getPrevTask=function(e){var t=this.viewModel.findItem(e);if(!t)return null;var n=(t.parent||this.viewModel.root).children.indexOf(t)-1;return n>-1?t.parent.children[n].task:t.parent.task},e.prototype.getTaskIdByInternalId=function(e){var t=this.viewModel.findItem(e),n=t&&t.task;return n?n.id:null},e.prototype.isTaskHasChildren=function(e){var t=this.viewModel.findItem(e);return t&&t.children.length>0},e.prototype.requireFirstLoadParentAutoCalc=function(){var e=this.ganttOwner;return e.getRequireFirstLoadParentAutoCalc&&e.getRequireFirstLoadParentAutoCalc()},e.prototype.updateOwnerInAutoParentMode=function(){this.viewModel.parentAutoCalc&&this.dispatcher.notifyParentDataRecalculated(this.viewModel.getCurrentTaskData())},e.prototype.getOwnerControlMainElement=function(){var e=this.ganttOwner;return e.getMainElement&&e.getMainElement()},e.prototype.adjustOwnerControl=function(){var e=this.ganttOwner;e.adjustControl&&e.adjustControl()},e.prototype.applySettings=function(e,t){void 0===t&&(t=!1);var n=T.Settings.parse(e),r=t||this.settings.equal(n);this.settings=n,r||this.resetAndUpdate()},e.prototype.getDataUpdateErrorCallback=function(){var e=this,t=this.history,n=t.getCurrentProcessingItemInfo();return function(){e.dispatcher.lock(),t.rollBackAndRemove(n),e.dispatcher.unlock(),e.updateBarManager()}},e.prototype.setTaskTooltipContentTemplate=function(e){this.settings.taskTooltipContentTemplate=e},e.prototype.setTaskProgressTooltipContentTemplate=function(e){this.settings.taskProgressTooltipContentTemplate=e},e.prototype.setTaskTimeTooltipContentTemplate=function(e){this.settings.taskTimeTooltipContentTemplate=e},e.prototype.setTaskContentTemplate=function(e){this.settings.taskContentTemplate=e},e.prototype.updateBarManager=function(){this.barManager.updateItemsState([])},e.prototype.onTaskAreaClick=function(e,t){var n,r=this.viewModel.items[e];return r&&this.onTaskClick(null===(n=r.task)||void 0===n?void 0:n.id,t)},e.prototype.onTaskAreaDblClick=function(e,t){var n=this.viewModel.items[e];n&&this.onTaskDblClick(n.task.id,t)&&this.commandManager.showTaskEditDialog.execute(n.task)},e.prototype.onTaskAreaContextMenu=function(e,t,n){var r,i,o="dependency"===n,s=t,a=this.viewModel,l=o?a.convertInternalToPublicKey("dependency",P.EvtUtils.getEventSource(t).getAttribute("dependency-id")):null===(i=null===(r=a.items[e])||void 0===r?void 0:r.task)||void 0===i?void 0:i.id;if(this.onGanttViewContextMenu(t,l,n)){var c={event:t,type:n,key:l,position:new A.Point(P.EvtUtils.getEventX(s),P.EvtUtils.getEventY(s))};this.showPopupMenu(c)}},e.prototype.onTaskSelectionChanged=function(e,t){var n=this,r=this.viewModel.items[e];this.isFocus=M.DomUtils.isItParent(this.renderHelper.taskArea,P.EvtUtils.getEventSource(t)),r&&this.isFocus&&this.settings.allowSelectTask&&setTimeout((function(){n.changeGanttTaskSelection(r.task.id,!0)}),0)},e.prototype.onTaskClick=function(e,t){return!this.ganttOwner.onTaskClick||this.ganttOwner.onTaskClick(e,t)},e.prototype.onTaskDblClick=function(e,t){return!this.ganttOwner.onTaskDblClick||this.ganttOwner.onTaskDblClick(e,t)},e.prototype.getDateFormat=function(e){return this.ganttOwner.getFormattedDateText?this.ganttOwner.getFormattedDateText(e):this.getDefaultDateFormat(e)},e.prototype.getDefaultDateFormat=function(e){return("0"+e.getDate()).slice(-2)+"/"+("0"+(e.getMonth()+1)).slice(-2)+"/"+e.getFullYear()+" "+("0"+e.getHours()).slice(-2)+":"+("0"+e.getMinutes()).slice(-2)},e.prototype.destroyTemplate=function(e){this.ganttOwner.destroyTemplate?this.ganttOwner.destroyTemplate(e):e.innerHTML=""},e.prototype.onTaskAreaSizeChanged=function(e){this.ganttOwner.onTaskAreaSizeChanged&&this.ganttOwner.onTaskAreaSizeChanged(e)},e.prototype.showTaskEditDialog=function(){this.commandManager.showTaskEditDialog.execute()},e.prototype.showTaskDetailsDialog=function(e){var t=this.getTaskByPublicId(e);t&&this.commandManager.showTaskEditDialog.execute(t,!0)},e.prototype.showResourcesDialog=function(){this.commandManager.showResourcesDialog.execute()},e.prototype.getCommandByKey=function(e){return this.commandManager.getCommand(e)},e.prototype._getTaskDataForUpdate=function(e,t){var n={};return t&&e&&((0,f.isDefined)(e.title)&&e.title!==t.title&&(n.title=e.title),(0,f.isDefined)(e.progress)&&e.progress!==t.progress&&(n.progress=e.progress),(0,f.isDefined)(e.start)&&e.start!==t.start&&(n.start=e.start),(0,f.isDefined)(e.end)&&e.end!==t.end&&(n.end=e.end),(0,f.isDefined)(e.color)&&e.color!==t.color&&(n.color=e.color)),Object.keys(n).length>0?n:null},e.prototype.updateViewDataRange=function(){var e=this.viewModel,t=e.getTaskMinStart(),n=e.getTaskMaxEnd(),r=t.getTime()<this.dataRange.start.getTime(),i=n.getTime()>this.dataRange.end.getTime();r&&(this.dataRange.start=t),i&&(this.dataRange.end=n),(r||i)&&this.resetAndUpdate()},e.cachedPrefix="cached_",e.taskAreaScrollLeftKey=e.cachedPrefix+"taskAreaScrollLeft",e.taskAreaScrollTopKey=e.cachedPrefix+"taskAreaScrollTop",e.taskTextHeightKey=e.cachedPrefix+"taskTextHeight",e}();t.GanttView=L},2449:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TaskAreaEventSource=t.TaskTitlePosition=t.Position=t.ViewType=void 0,function(e){e[e.TenMinutes=0]="TenMinutes",e[e.Hours=1]="Hours",e[e.SixHours=2]="SixHours",e[e.Days=3]="Days",e[e.Weeks=4]="Weeks",e[e.Months=5]="Months",e[e.Quarter=6]="Quarter",e[e.Years=7]="Years",e[e.FiveYears=8]="FiveYears"}(t.ViewType||(t.ViewType={})),function(e){e[e.Left=0]="Left",e[e.Top=1]="Top",e[e.Right=2]="Right",e[e.Bottom=3]="Bottom"}(t.Position||(t.Position={})),function(e){e[e.Inside=0]="Inside",e[e.Outside=1]="Outside",e[e.None=2]="None"}(t.TaskTitlePosition||(t.TaskTitlePosition={})),function(e){e[e.TaskArea=0]="TaskArea",e[e.TaskEdit_Frame=1]="TaskEdit_Frame",e[e.TaskEdit_Progress=2]="TaskEdit_Progress",e[e.TaskEdit_Start=3]="TaskEdit_Start",e[e.TaskEdit_End=4]="TaskEdit_End",e[e.TaskEdit_DependencyStart=5]="TaskEdit_DependencyStart",e[e.TaskEdit_DependencyFinish=6]="TaskEdit_DependencyFinish",e[e.Successor_Wrapper=7]="Successor_Wrapper",e[e.Successor_DependencyStart=8]="Successor_DependencyStart",e[e.Successor_DependencyFinish=9]="Successor_DependencyFinish"}(t.TaskAreaEventSource||(t.TaskAreaEventSource={}))},8877:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.EtalonSizeValues=void 0;var n=function(){this.scaleItemWidths={}};t.EtalonSizeValues=n},8695:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.FullScreenModeHelper=void 0;var r=n(9279),i=n(2217),o=n(6907),s=function(){function e(e){this._isInFullScreenMode=!1,this.fullScreenTempVars={},this.settings=e}return Object.defineProperty(e.prototype,"isInFullScreenMode",{get:function(){return this._isInFullScreenMode},enumerable:!1,configurable:!0}),e.prototype.getMainElement=function(){return this.settings.getMainElement()},e.prototype.adjustControl=function(){this.settings.adjustControl()},e.prototype.toggle=function(){return this._isInFullScreenMode=!this._isInFullScreenMode,this._isInFullScreenMode?this.setFullScreenMode():this.setNormalMode(),!0},e.prototype.setFullScreenMode=function(){this.prepareFullScreenMode(),this.adjustControlInFullScreenMode()},e.prototype.prepareFullScreenMode=function(){var e=this.getMainElement();i.AttrUtils.changeElementStyleAttribute(e,"border-top-width","0px"),i.AttrUtils.changeElementStyleAttribute(e,"border-left-width","0px"),i.AttrUtils.changeElementStyleAttribute(e,"border-right-width","0px"),i.AttrUtils.changeElementStyleAttribute(e,"border-bottom-width","0px"),this.fullScreenTempVars.scrollTop=o.DomUtils.getDocumentScrollTop(),this.fullScreenTempVars.scrollLeft=o.DomUtils.getDocumentScrollLeft(),i.AttrUtils.changeElementStyleAttribute(e,"background-color","white"),i.AttrUtils.changeElementStyleAttribute(e,"position","fixed"),i.AttrUtils.changeElementStyleAttribute(e,"top","0px"),i.AttrUtils.changeElementStyleAttribute(e,"left","0px"),i.AttrUtils.changeElementStyleAttribute(e,"z-index","1010"),i.AttrUtils.changeElementStyleAttribute(document.documentElement,"position","static"),i.AttrUtils.changeElementStyleAttribute(document.documentElement,"overflow","hidden"),this.fullScreenTempVars.bodyMargin=document.body.style.margin,document.body.style.margin="0",this.fullScreenTempVars.width=e.style.width,this.fullScreenTempVars.height=e.style.height||e.clientHeight,window.self!==window.top&&this.requestFullScreen(document.body)},e.prototype.setNormalMode=function(){this.cancelFullScreen(document);var e=this.getMainElement();i.AttrUtils.restoreElementStyleAttribute(e,"left"),i.AttrUtils.restoreElementStyleAttribute(e,"top"),i.AttrUtils.restoreElementStyleAttribute(e,"background-color"),i.AttrUtils.restoreElementStyleAttribute(document.documentElement,"overflow"),i.AttrUtils.restoreElementStyleAttribute(document.documentElement,"position"),i.AttrUtils.restoreElementStyleAttribute(e,"z-index"),document.body.style.margin=this.fullScreenTempVars.bodyMargin,i.AttrUtils.restoreElementStyleAttribute(e,"position"),i.AttrUtils.restoreElementStyleAttribute(e,"border-top-width"),i.AttrUtils.restoreElementStyleAttribute(e,"border-left-width"),i.AttrUtils.restoreElementStyleAttribute(e,"border-right-width"),i.AttrUtils.restoreElementStyleAttribute(e,"border-bottom-width"),this.setHeight(this.fullScreenTempVars.height),this.setWidth(this.fullScreenTempVars.width),document.documentElement.scrollTop=this.fullScreenTempVars.scrollTop,document.documentElement.scrollLeft=this.fullScreenTempVars.scrollLeft,this.adjustControl()},e.prototype.adjustControlInFullScreenMode=function(){var e=0==document.documentElement.clientWidth?document.body.clientWidth:document.documentElement.clientWidth,t=0==document.documentElement.clientHeight?document.body.clientHeight:document.documentElement.clientHeight;this.setWidth(e),this.setHeight(t),this.adjustControl()},e.prototype.requestFullScreen=function(e){e.requestFullscreen?e.requestFullscreen():e.mozRequestFullScreen?e.mozRequestFullScreen():e.webkitRequestFullscreen?e.webkitRequestFullscreen():e.msRequestFullscreen&&e.msRequestFullscreen()},e.prototype.cancelFullScreen=function(e){r.Browser.Firefox&&!this.getFullScreenElement(e)||(e.webkitExitFullscreen?e.webkitExitFullscreen():e.mozCancelFullScreen?e.mozCancelFullScreen():e.msExitFullscreen?e.msExitFullscreen():e.exitFullscreen&&e.exitFullscreen())},e.prototype.getFullScreenElement=function(e){return e.fullscreenElement||e.webkitFullscreenElement||e.mozFullScreenElement||e.msFullscreenElement},e.prototype.setWidth=function(e){this.getMainElement().style.width=this.isNumber(e)?e+"px":e},e.prototype.setHeight=function(e){this.getMainElement().style.height=this.isNumber(e)?e+"px":e},e.prototype.isNumber=function(e){return!isNaN(parseFloat(e))&&isFinite(e)},e}();t.FullScreenModeHelper=s},1391:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.GridElementInfo=void 0;var r=n(8900),i=n(6353),o=n(3604),s=function(){function e(t,n,s){this.id=e.id++,this.position=new r.Point(void 0,void 0),this.size=new i.Size(0,0),this.margins=new o.Margins(void 0,void 0,void 0,void 0),this.attr={},this.style={},this.additionalInfo={},t&&(this.className=t),n&&this.setPosition(n),s&&this.setSize(s)}return e.prototype.setSize=function(e){this.size.width=e.width,this.size.height=e.height},e.prototype.setPosition=function(e){this.position.x=e.x,this.position.y=e.y},e.prototype.assignToElement=function(e){this.assignPosition(e),this.assignSize(e),this.assignMargins(e),this.className&&(e.className=this.className)},e.prototype.assignPosition=function(e){null!=this.position.x&&(e.style.left=this.position.x+"px"),null!=this.position.y&&(e.style.top=this.position.y+"px")},e.prototype.assignSize=function(e){this.size.width&&(e.style.width=this.size.width+"px"),this.size.height&&(e.style.height=this.size.height+"px")},e.prototype.assignMargins=function(e){this.margins.left&&(e.style.marginLeft=this.margins.left+"px"),this.margins.top&&(e.style.marginTop=this.margins.top+"px"),this.margins.right&&(e.style.marginRight=this.margins.right+"px"),this.margins.bottom&&(e.style.marginBottom=this.margins.bottom+"px")},e.prototype.setAttribute=function(e,t){this.attr[e]=t},e.id=0,e}();t.GridElementInfo=s},1855:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.GridLayoutCalculator=void 0;var r=n(6353),i=n(858),o=n(2449),s=n(1391),a=n(8900),l=n(9201),c=n(1442),u=n(5950),d=n(7072),p=n(2491),h=function(){function e(){this.tileToDependencyMap=[],this.tileToNoWorkingIntervalsMap=[],this.minLineLength=10,this.resourceMaxWidth=500,this.minTaskWidth=2,this._taskWrapperPoints=Array(),this._taskElementInfoList=Array(),this._scaleCalculator=new d.ScaleCalculator}return e.prototype.setSettings=function(e,t,n,i,o,s,a,c){void 0===a&&(a=0),void 0===c&&(c=0),this.visibleTaskAreaSize=e,this.tickSize=t,this._viewType=s,this.range=i,this.verticalTickCount=o.itemCount,this.viewModel=o,this.elementSizeValues=n,this.taskHeight=n.taskHeight,this.parentTaskHeight=n.parentTaskHeight,this.milestoneWidth=n.milestoneWidth,this.scaleHeight=n.scaleItemHeight,this.arrowSize=new r.Size(n.connectorArrowWidth,n.connectorArrowWidth),this.lineThickness=n.connectorLineThickness,this.minConnectorSpaceFromTask=(this.tickSize.height-this.taskHeight)/2,this.tickTimeSpan=l.DateUtils.getTickTimeSpan(s),this.scrollBarHeight=a,this.createTileToNonWorkingIntervalsMap(),this._scaleCalculator.setSettings(i,s,t,c),this.reset()},Object.defineProperty(e.prototype,"viewType",{get:function(){return this._viewType},set:function(e){this._viewType!==e&&(this._viewType=e,this._scaleCalculator.setViewType(e))},enumerable:!1,configurable:!0}),e.prototype.reset=function(){this._taskWrapperPoints=new Array,this._taskElementInfoList=Array()},e.prototype.resetTaskInfo=function(e){delete this._taskWrapperPoints[e],delete this._taskElementInfoList[e]},e.prototype.getTaskAreaBorderInfo=function(e,t){var n=t?this.getVerticalGridLineHeight():this.getTotalWidth();return this.getGridBorderInfo(e,t,n)},e.prototype.getTotalWidth=function(){return this._scaleCalculator.scaleWidth},e.prototype.getScaleBorderInfo=function(e,t){var n=new s.GridElementInfo,i=this._scaleCalculator;return n.setPosition(new a.Point(i.getScaleBorderPosition(e,t),void 0)),n.setSize(new r.Size(0,this.scaleHeight)),n.className="dx-gantt-vb",n},e.prototype.getGridBorderInfo=function(e,t,n){var r=new s.GridElementInfo;return r.setPosition(this.getGridBorderPosition(e,t)),n&&r.setSize(this.getGridBorderSize(t,n)),r.className=t?"dx-gantt-vb":"dx-gantt-hb",r},e.prototype.getGridBorderPosition=function(e,t){var n=new a.Point(void 0,void 0),r=this._scaleCalculator,i=t?r.getScaleBorderPosition(e,this.viewType):(e+1)*this.tickSize.height;return t?n.x=i:n.y=i,n},e.prototype.getGridBorderSize=function(e,t){var n=new r.Size(0,0);return e?n.height=t:n.width=t,n},e.prototype.getScaleElementInfo=function(e,t){var n=new s.GridElementInfo,r=this.getScaleItemInfo(e,t);if(r){n.setPosition(r.position),n.setSize(r.size),n.className=this.getScaleItemClassName(t,n,this.getRenderedNoWorkingIntervals(n.position.x));var o=this._scaleCalculator.getScaleItems(t);(0===e||e===o.length-1)&&(n.style.overflowX="hidden",n.style.textOverflow="ellipsis"),n.additionalInfo.range=new i.DateRange(r.start,r.end)}return n},e.prototype.getScaleItemStart=function(e,t){return this._scaleCalculator.getScaleItemAdjustedStart(e,t)},e.prototype.getScaleItemClassName=function(e,t,n){var r="dx-gantt-si";return e.valueOf()==this.viewType.valueOf()&&this.isScaleItemInsideNoWorkingInterval(t,n)&&(r+=" dx-gantt-holiday-scaleItem"),r},e.prototype.getScaleItemInfo=function(e,t){return this._scaleCalculator.getScaleItem(e,t)},e.prototype.getScaleRangesInArea=function(e,t){var n=l.DateUtils.ViewTypeToScaleMap[this.viewType],r=this._scaleCalculator,i=Math.max(r.getScaleIndexByPos(e,n),0),o=r.getScaleIndexByPos(t,n);-1===o&&(o=r.topScaleItems.length-1);var s=Math.max(r.getScaleIndexByPos(e),0),a=r.getScaleIndexByPos(t);return-1===a&&(a=r.bottomScaleItems.length-1),[[i,o],[s,a]]},e.prototype.isScaleItemInsideNoWorkingInterval=function(e,t){for(var n=e.position.x,r=e.position.x+e.size.width,i=0;i<t.length;i++){var o=t[i].position.x,s=t[i].position.x+t[i].size.width;if(n>=o&&r<=s)return!0}return!1},e.prototype.getScaleItemColSpan=function(e){return this._scaleCalculator.getScaleItemColSpan(e)},e.prototype.getTaskWrapperElementInfo=function(e){var t=new s.GridElementInfo;return t.className=this.getTaskWrapperClassName(e),t.setPosition(this.getTaskWrapperPoint(e)),t.setAttribute("task-index",e),t},e.prototype.getTaskWrapperClassName=function(e){var t="dx-gantt-taskWrapper",n=this.getViewItem(e);return n.task.isMilestone()&&!n.isCustom&&(t="dx-gantt-milestoneWrapper"),n.selected&&(t+=" dx-gantt-selectedTask"),t},e.prototype.getTaskWrapperPoint=function(e){if(!(0,p.isDefined)(this._taskWrapperPoints[e])){var t=this.getViewItem(e),n=this.getTaskHeight(e),r=e*this.tickSize.height+(this.tickSize.height-n)/2,i=new a.Point(this.getPosByDate(t.task.start),r);t.task.isMilestone()&&!t.isCustom&&(i.x-=n/2),this._taskWrapperPoints[e]=i}return this._taskWrapperPoints[e].clone()},e.prototype.getTaskElementInfo=function(e,t){if(void 0===t&&(t=!1),!(0,p.isDefined)(this._taskElementInfoList[e])){var n=new s.GridElementInfo,r=this.getTask(e),i=this.viewModel.parentAutoCalc&&this.viewModel.taskHasChildrenByIndex(e);if(!r.isMilestone()){var o=this.getTaskWidth(e);n.size.width=this.getCorrectedTaskWidthByRange(e,o),n.size.width<o&&(n.additionalInfo.taskCut=!0),t&&(n.size.height=this.getTaskHeight(e))}n.className=this.getTaskClassName(e,n.size.width),r.color&&(n.style.backgroundColor=r.color,i&&(n.style.borderLeftColor=r.color,n.style.borderRightColor=r.color,n.style.borderTopColor=r.color)),this._taskElementInfoList[e]=n}return this._taskElementInfoList[e]},e.prototype.getTaskClassName=function(t,n){var r=e.taskClassName,i=this.getViewItem(t),o=this.viewModel.parentAutoCalc&&this.viewModel.taskHasChildrenByIndex(t);return i.task.isMilestone()&&!i.isCustom?r+=" "+e.milestoneClassName:(n<=this.elementSizeValues.smallTaskWidth&&(r+=" "+e.smallTaskClassName),o&&(r+=this.getAutoCalcParentTaskClassName(i.task))),r},e.prototype.getAutoCalcParentTaskClassName=function(t){var n=" "+e.parentTaskClassName;return 0==t.progress&&(n+=" dx-gantt-noPrg"),t.progress>=100&&(n+=" dx-gantt-cmpl"),n},e.prototype.getTaskPoint=function(e){var t=this.getTaskWrapperPoint(e);return this.getTask(e).isMilestone()||(t.y+=this.elementSizeValues.taskWrapperTopPadding),t},e.prototype.getTaskSize=function(e){return new r.Size(this.getTaskWidth(e),this.getTaskHeight(e))},e.prototype.getTaskWidth=function(e){var t=this.getViewItem(e);return t.isCustom&&t.size.width?t.size.width:t.task.isMilestone()&&!t.isCustom?this.getTaskHeight(e):Math.max(this.getWidthByDateRange(t.task.start,t.task.end),this.minTaskWidth)},e.prototype.getTaskHeight=function(e){var t=this.getViewItem(e);return t.task.isMilestone()&&!t.isCustom?this.milestoneWidth:this.viewModel.isTaskToCalculateByChildren(t.task.internalId)?this.parentTaskHeight:t.isCustom&&t.size.height?t.size.height:this.taskHeight},e.prototype.getTask=function(e){var t=this.getViewItem(e);return null==t?void 0:t.task},e.prototype.getViewItem=function(e){return this.viewModel.items[e]},e.prototype.getTaskProgressElementInfo=function(t){var n=new s.GridElementInfo;return n.className=e.taskProgressClassName,n.setSize(this.getTaskProgressSize(t)),n},e.prototype.getTaskProgressSize=function(e){var t=this.getTaskProgressWidth(e);return this.isTaskCutByRange(e)&&(t=this.getCorrectedTaskWidthByRange(e,t)),new r.Size(t,0)},e.prototype.getTaskProgressWidth=function(e){return this.getTaskWidth(e)*this.getTask(e).normalizedProgress/100},e.prototype.getTaskTextElementInfo=function(e,t){var n=new s.GridElementInfo;if(n.className=this.getTaskTextElementClassName(t),!t){var r=this.getTaskPoint(e).x;if(r<this.elementSizeValues.outsideTaskTextDefaultWidth){var i=Math.max(r,0);n.size.width=i,i>0?n.margins.left=-i:n.additionalInfo.hidden=!0}}return n},e.prototype.getTaskTextElementClassName=function(t){return e.taskTitleClassName.concat(" ",t?e.titleInClassName:e.titleOutClassName)},e.prototype.getTaskResourcesWrapperElementInfo=function(e){var t=new s.GridElementInfo,n=this.getTaskSize(e).width;return t.className="dx-gantt-taskResWrapper",t.setPosition(this.getTaskWrapperPoint(e)),t.position.x=t.position.x+n,t},e.prototype.getTaskResourceElementInfo=function(){var t=new s.GridElementInfo;return t.className=e.taskResourceClassName,t},e.prototype.getSelectionElementInfo=function(e){return this.getRowElementInfo(e,"dx-gantt-sel")},e.prototype.getSelectionPosition=function(e){var t=new a.Point(void 0,void 0);return t.y=e*this.tickSize.height,t},e.prototype.getSelectionSize=function(){return new r.Size(this.getTotalWidth(),this.tickSize.height)},e.prototype.getHighlightRowInfo=function(e){return this.getRowElementInfo(e,"dx-gantt-altRow")},e.prototype.getRowElementInfo=function(e,t){var n=new s.GridElementInfo;return n.className=t,n.setPosition(this.getSelectionPosition(e)),n.setSize(this.getSelectionSize()),n},e.prototype.getNoWorkingIntervalInfo=function(e){var t=new s.GridElementInfo;return t.className="dx-gantt-nwi",t.setPosition(this.getNoWorkingIntervalPosition(e.start)),t.setSize(this.getNoWorkingIntervalSize(e)),t},e.prototype.getNoWorkingIntervalPosition=function(e){var t=new a.Point(void 0,void 0);return t.x=this.getPosByDate(e),t},e.prototype.getNoWorkingIntervalSize=function(e){return new r.Size(this.getWidthByDateRange(e.start,e.end),this.getVerticalGridLineHeight())},e.prototype.getVerticalGridLineHeight=function(){return Math.max(this.visibleTaskAreaSize.height-this.scrollBarHeight,this.tickSize.height*this.verticalTickCount)},e.prototype.getConnectorInfo=function(e,t,n,r){for(var i=new Array,o=this.getConnectorPoints(t,n,r),s=0;s<o.length-1;s++)i.push(this.getConnectorLineInfo(e,o[s],o[s+1],0==s||s==o.length-2));return i.push(this.getArrowInfo(e,o,i,t,n)),this.checkAndCorrectConnectorLinesByRange(i),i.filter((function(e){return!!e}))},e.prototype.getConnectorLineInfo=function(e,t,n,r){var i=new s.GridElementInfo,o=t.x==n.x;return i.className=this.getConnectorClassName(o),i.setPosition(this.getConnectorPosition(t,n)),i.setSize(this.getConnectorSize(t,n,o,r)),i.setAttribute("dependency-id",e),i},e.prototype.getConnectorClassName=function(t){return t?e.CLASSNAMES.CONNECTOR_VERTICAL:e.CLASSNAMES.CONNECTOR_HORIZONTAL},e.prototype.getConnectorPosition=function(e,t){return new a.Point(Math.min(e.x,t.x),Math.min(e.y,t.y))},e.prototype.getConnectorSize=function(e,t,n,i){var o=new r.Size(0,0),s=i?0:1;return n?o.height=Math.abs(t.y-e.y)+s:o.width=Math.abs(t.x-e.x)+s,o},e.prototype.getArrowInfo=function(e,t,n,r,i){var o=new s.GridElementInfo,a=this.findArrowLineInfo(n,r,i),l=this.getArrowPosition(t,r,i);return o.className=this.getArrowClassName(l),o.setPosition(this.getArrowPoint(a,l)),o.setAttribute("dependency-id",e),o},e.prototype.findArrowLineInfo=function(e,t,n){return e[t<n?e.length-1:0]},e.prototype.getArrowPosition=function(e,t,n){var r=e[t<n?e.length-2:1],i=e[t<n?e.length-1:0];return r.x==i.x?r.y>i.y?o.Position.Top:o.Position.Bottom:r.x>i.x?o.Position.Left:o.Position.Right},e.prototype.getArrowClassName=function(t){var n=e.arrowClassName;switch(t){case o.Position.Left:n=n.concat(" ",e.leftArrowClassName);break;case o.Position.Top:n=n.concat(" ",e.topArrowClassName);break;case o.Position.Right:n=n.concat(" ",e.rightArrowClassName);break;case o.Position.Bottom:n=n.concat(" ",e.bottomArrowClassName)}return n},e.prototype.getArrowPositionByClassName=function(t){return t.indexOf(e.leftArrowClassName)>-1?o.Position.Left:t.indexOf(e.topArrowClassName)>-1?o.Position.Top:t.indexOf(e.rightArrowClassName)>-1?o.Position.Right:t.indexOf(e.bottomArrowClassName)>-1?o.Position.Bottom:void 0},e.prototype.getArrowPoint=function(e,t){return new a.Point(this.getArrowX(e,t),this.getArrowY(e,t))},e.prototype.getArrowX=function(e,t){switch(t){case o.Position.Left:return e.position.x-this.arrowSize.width/2;case o.Position.Right:return e.position.x+e.size.width-this.arrowSize.width/2;case o.Position.Top:case o.Position.Bottom:return e.position.x-(this.arrowSize.width-this.lineThickness)/2}},e.prototype.getArrowY=function(e,t){switch(t){case o.Position.Top:return e.position.y-this.arrowSize.height/2;case o.Position.Bottom:return e.position.y+e.size.height-this.arrowSize.height/2;case o.Position.Left:case o.Position.Right:return e.position.y-(this.arrowSize.height-this.lineThickness)/2}},e.prototype.getPosByDate=function(e){return this.getWidthByDateRange(this.range.start,e)},e.prototype.getWidthByDateRange=function(e,t){return l.DateUtils.getRangeTickCount(e,t,this.viewType)*this.tickSize.width},e.prototype.getDateByPos=function(e){if(this.viewType===o.ViewType.Months||this.viewType===o.ViewType.Quarter)return this.getDateByPosInMonthBasedViewTypes(e);var t=e/this.tickSize.width,n=new Date(this.range.start);return l.DateUtils.getDSTCorrectedTaskEnd(n,t*this.tickTimeSpan)},e.prototype.getDateByPosInMonthBasedViewTypes=function(e){return this._scaleCalculator.getDateInScale(e)},e.prototype.getConnectorPoints=function(e,t,n){switch(n){case u.DependencyType.FS:return this.getFinishToStartConnectorPoints(e,t);case u.DependencyType.SF:return this.getStartToFinishConnectorPoints(e,t);case u.DependencyType.SS:return this.getStartToStartConnectorPoints(e,t);case u.DependencyType.FF:return this.getFinishToFinishConnectorPoints(e,t);default:return new Array}},e.prototype.getFinishToStartConnectorPoints=function(e,t){return e<t?this.getTask(e).end<=this.getTask(t).start?this.getConnectorPoints_FromTopTaskRightSide_ToBottomTaskTopSide(e,t,!1):this.getConnectorPoints_FromTopTaskRightSide_ToBottomTaskLeftSide(e,t,!1):this.getTask(e).end<=this.getTask(t).start?this.getConnectorPoints_FromTopTaskBottomSide_ToBottomTaskRightSide(t,e,!1):this.getConnectorPoints_FromTopTaskLeftSide_ToBottomTaskRightSide(t,e,!0)},e.prototype.getFinishToFinishConnectorPoints=function(e,t){return e<t?this.getConnectorPoints_FromTopTaskRightSide_ToBottomTaskRightSide(e,t):this.getConnectorPoints_FromTopTaskRightSide_ToBottomTaskRightSide(t,e)},e.prototype.getStartToStartConnectorPoints=function(e,t){return e<t?this.getConnectorPoints_FromTopTaskLeftSide_ToBottomTaskLeftSide(e,t):this.getConnectorPoints_FromTopTaskLeftSide_ToBottomTaskLeftSide(t,e)},e.prototype.getStartToFinishConnectorPoints=function(e,t){return e<t?this.getTask(e).start>=this.getTask(t).end?this.getConnectorPoints_FromTopTaskLeftSide_ToBottomTaskTopSide(e,t,!0):this.getConnectorPoints_FromTopTaskLeftSide_ToBottomTaskRightSide(e,t,!1):this.getTask(e).start>=this.getTask(t).end?this.getConnectorPoints_FromTopTaskBottomSide_ToBottomTaskLeftSide(t,e,!0):this.getConnectorPoints_FromTopTaskRightSide_ToBottomTaskLeftSide(t,e,!0)},e.prototype.getConnectorPoints_FromTopTaskRightSide_ToBottomTaskTopSide=function(e,t,n){var r=new Array,i=this.getTaskPoint(e),o=this.getTaskPoint(t),s=this.getTaskRightCenter(i,e),l=this.getTask(t).isMilestone(),c=this.getTaskTopCenter(o,t),u=this.getHorizontalIndentFromTaskEdge(t,n);return r.push(new a.Point(Math.floor(s.x),Math.floor(s.y))),r.push(new a.Point(Math.floor(l?c.x:o.x+u),Math.floor(r[0].y))),r.push(new a.Point(Math.floor(r[1].x),Math.floor(c.y))),r},e.prototype.getConnectorPoints_FromTopTaskRightSide_ToBottomTaskRightSide=function(e,t){var n=new Array,r=this.getTaskPoint(e),i=this.getTaskPoint(t),o=this.getTaskRightCenter(r,e),s=this.getTaskRightCenter(i,t);return n.push(new a.Point(Math.floor(o.x),Math.floor(o.y))),n.push(new a.Point(Math.floor(Math.max(o.x,s.x)+this.minLineLength),Math.floor(n[0].y))),n.push(new a.Point(Math.floor(n[1].x),Math.floor(s.y))),n.push(new a.Point(Math.floor(s.x),Math.floor(s.y))),n},e.prototype.getConnectorPoints_FromTopTaskRightSide_ToBottomTaskLeftSide=function(e,t,n){var r=new Array,i=this.getTaskPoint(e),o=this.getTaskPoint(t),s=this.getTaskRightCenter(i,e),l=this.getTaskBottomCenter(i,e),c=this.getTaskLeftCenter(o,t),u=this.getTaskTopCenter(o,t),d=n?this.getViewItem(e):this.getViewItem(t),p=d.isCustom?(this.tickSize.height-d.size.height)/2:this.minConnectorSpaceFromTask;return r.push(new a.Point(Math.floor(s.x),Math.floor(s.y))),r.push(new a.Point(Math.floor(r[0].x+this.minLineLength),Math.floor(r[0].y))),r.push(new a.Point(Math.floor(r[1].x),Math.floor(n?l.y+p:u.y-p))),r.push(new a.Point(Math.floor(c.x-this.minLineLength),Math.floor(r[2].y))),r.push(new a.Point(Math.floor(r[3].x),Math.floor(c.y))),r.push(new a.Point(Math.floor(c.x),Math.floor(c.y))),r},e.prototype.getConnectorPoints_FromTopTaskBottomSide_ToBottomTaskRightSide=function(e,t,n){var r=new Array,i=this.getTaskPoint(e),o=this.getTaskPoint(t),s=this.getTaskBottomCenter(i,e),l=this.getTask(e).isMilestone(),c=this.getTaskRightCenter(o,t),u=this.getHorizontalIndentFromTaskEdge(e,n);return r.push(new a.Point(Math.floor(l?s.x:i.x+u),Math.floor(s.y))),r.push(new a.Point(Math.floor(r[0].x),Math.floor(c.y))),r.push(new a.Point(Math.floor(c.x),Math.floor(c.y))),r},e.prototype.getConnectorPoints_FromTopTaskBottomSide_ToBottomTaskLeftSide=function(e,t,n){var r=new Array,i=this.getTaskPoint(e),o=this.getTaskPoint(t),s=this.getTaskBottomCenter(i,e),l=this.getTask(e).isMilestone(),c=this.getTaskLeftCenter(o,t),u=this.getHorizontalIndentFromTaskEdge(e,n);return r.push(new a.Point(Math.floor(l?s.x:i.x+u),Math.floor(s.y))),r.push(new a.Point(Math.floor(r[0].x),Math.floor(c.y))),r.push(new a.Point(Math.floor(c.x),Math.floor(c.y))),r},e.prototype.getConnectorPoints_FromTopTaskLeftSide_ToBottomTaskTopSide=function(e,t,n){var r=new Array,i=this.getTaskPoint(e),o=this.getTaskPoint(t),s=this.getTaskLeftCenter(i,e),l=this.getTaskTopCenter(o,t),c=this.getTask(t).isMilestone(),u=this.getHorizontalIndentFromTaskEdge(t,n);return r.push(new a.Point(Math.floor(s.x),Math.floor(s.y))),r.push(new a.Point(Math.floor(c?l.x:o.x+u),Math.floor(r[0].y))),r.push(new a.Point(Math.floor(r[1].x),Math.floor(l.y))),r},e.prototype.getConnectorPoints_FromTopTaskLeftSide_ToBottomTaskRightSide=function(e,t,n){var r=new Array,i=this.getTaskPoint(e),o=this.getTaskPoint(t),s=this.getTaskLeftCenter(i,e),l=this.getTaskBottomCenter(i,e),c=this.getTaskRightCenter(o,t),u=this.getTaskTopCenter(o,t),d=n?this.getViewItem(e):this.getViewItem(t),p=d.isCustom?(this.tickSize.height-d.size.height)/2:this.minConnectorSpaceFromTask;return r.push(new a.Point(Math.floor(s.x),s.y)),r.push(new a.Point(Math.floor(r[0].x-this.minLineLength),r[0].y)),r.push(new a.Point(Math.floor(r[1].x),Math.floor(n?l.y+p:u.y-p))),r.push(new a.Point(Math.floor(c.x+this.minLineLength),Math.floor(r[2].y))),r.push(new a.Point(Math.floor(r[3].x),Math.floor(c.y))),r.push(new a.Point(Math.floor(c.x),Math.floor(c.y))),r},e.prototype.getConnectorPoints_FromTopTaskLeftSide_ToBottomTaskLeftSide=function(e,t){var n=new Array,r=this.getTaskPoint(e),i=this.getTaskPoint(t),o=this.getTaskLeftCenter(r,e),s=this.getTaskLeftCenter(i,t);return n.push(new a.Point(Math.floor(o.x),Math.floor(o.y))),n.push(new a.Point(Math.floor(Math.min(o.x,s.x)-this.minLineLength),Math.floor(n[0].y))),n.push(new a.Point(Math.floor(n[1].x),Math.floor(s.y))),n.push(new a.Point(Math.floor(s.x),Math.floor(s.y))),n},e.prototype.getTaskSidePoints=function(e){var t=this.getTaskPoint(e);return[this.getTaskLeftCenter(t,e),this.getTaskTopCenter(t,e),this.getTaskRightCenter(t,e),this.getTaskBottomCenter(t,e)]},e.prototype.getTaskLeftCenter=function(e,t){return new a.Point(e.x-this.getTaskEdgeCorrection(t),e.y+this.getTaskHeight(t)/2)},e.prototype.getTaskRightCenter=function(e,t){return new a.Point(e.x+this.getTaskWidth(t)+this.getTaskEdgeCorrection(t),e.y+this.getTaskHeight(t)/2)},e.prototype.getTaskTopCenter=function(e,t){return new a.Point(e.x+this.getTaskWidth(t)/2,e.y-this.getTaskEdgeCorrection(t))},e.prototype.getTaskBottomCenter=function(e,t){return new a.Point(e.x+this.getTaskWidth(t)/2,e.y+this.getTaskHeight(t)+this.getTaskEdgeCorrection(t))},e.prototype.getTaskEdgeCorrection=function(e){var t=this.getViewItem(e);return t.task.isMilestone()&&!t.isCustom?this.getTaskHeight(e)*(Math.sqrt(2)-1)/2:0},e.prototype.getHorizontalIndentFromTaskEdge=function(e,t){void 0===t&&(t=!1);var n=this.getTaskWidth(e),r=this.minLineLength<n/3?this.minLineLength:.2*n;return t?n-r:r},e.prototype.getRenderedRowColumnIndices=function(e,t){for(var n=t?this.visibleTaskAreaSize.height:this.visibleTaskAreaSize.width,r=t?this.getFirstVisibleGridCellIndex(e,this.tickSize.height):this.getFirstScaleVisibleIndex(e),i=t?this.getLastVisibleGridCellIndex(e,this.tickSize.height,n,this.verticalTickCount):this.getLastScaleVisibleIndex(e),o=new Array,s=r;s<=i;s++)o.push(s);return o},e.prototype.getRenderedScaleItemIndices=function(e,t){for(var n=e===this.viewType,r=this._scaleCalculator,i=t[0],o=t[t.length-1],s=n?i:r.getTopScaleIndexByBottomIndex(i),a=n?o:r.getTopScaleIndexByBottomIndex(o),l=new Array,c=s;c<=a;c++)l.push(c);return l},e.prototype.getFirstScaleVisibleIndex=function(e){return this._scaleCalculator.getFirstScaleIndexForRender(e)},e.prototype.getLastScaleVisibleIndex=function(e){return this._scaleCalculator.getLastScaleIndexForRender(e+this.visibleTaskAreaSize.width)},e.prototype.getFirstVisibleGridCellIndex=function(e,t){var n=Math.floor(e/t);return n=Math.max(n-10,0)},e.prototype.getLastVisibleGridCellIndex=function(e,t,n,r){var i=Math.floor((e+n)/t);return i=Math.min(i+10,r-1)},e.prototype.createTileToConnectorLinesMap=function(){this.tileToDependencyMap=[];for(var e=0;e<this.viewModel.items.length;e++)for(var t=0;t<this.viewModel.items[e].dependencies.length;t++)this.createConnecotInfo(this.viewModel.items[e].dependencies[t],this.viewModel.items[e].visibleIndex)},e.prototype.updateTileToConnectorLinesMap=function(e){this.tileToDependencyMap.forEach((function(t,n,r){r[n]=t.filter((function(t){return t.attr["dependency-id"]!=e}))}));var t=[],n=this.viewModel.items.filter((function(t){return t.dependencies.filter((function(t){return t.id==e})).length>0}))[0];if(n){var r=n.dependencies.filter((function(t){return t.id===e}))[0];t=this.createConnecotInfo(r,n.visibleIndex)}return t},e.prototype.createConnecotInfo=function(e,t){var n=this,r=e.predecessor.visibleIndex,i=e.type,o=e.id,s=this.getConnectorInfo(o,r,t,i);return s.forEach((function(e){n.addElementInfoToTileMap(e,n.tileToDependencyMap,!0)})),s},e.prototype.createTileToNonWorkingIntervalsMap=function(){this.tileToNoWorkingIntervalsMap=[];for(var e=0;e<this.viewModel.noWorkingIntervals.length;e++){var t=this.getAdjustedNoWorkingInterval(this.viewModel.noWorkingIntervals[e]);if(t){var n=this.getNoWorkingIntervalInfo(t);this.addElementInfoToTileMap(n,this.tileToNoWorkingIntervalsMap,!1)}}},e.prototype.getAdjustedNoWorkingInterval=function(e){return e.end.getTime()-e.start.getTime()<this.tickTimeSpan-1?null:new i.DateRange(l.DateUtils.getNearestScaleTickDate(e.start,this.range,this.tickTimeSpan,this.viewType),l.DateUtils.getNearestScaleTickDate(e.end,this.range,this.tickTimeSpan,this.viewType))},e.prototype.addElementInfoToTileMap=function(e,t,n){var r=n?e.position.y:e.position.x,i=n?e.size.height:e.size.width,o=2*(n?this.visibleTaskAreaSize.height:this.visibleTaskAreaSize.width);if(o>0)for(var s=Math.floor(r/o),a=Math.floor((r+i)/o),l=s;l<=a;l++)t[l]||(t[l]=new Array),t[l].push(e)},e.prototype.getRenderedConnectorLines=function(e){return this.getElementsInRenderedTiles(this.tileToDependencyMap,!0,e)},e.prototype.getRenderedNoWorkingIntervals=function(e){return this.getElementsInRenderedTiles(this.tileToNoWorkingIntervalsMap,!1,e)},e.prototype.getRenderedStripLines=function(e){var t=new Array,n=e.stripLines.map((function(e){return e.clone()}));e.showCurrentTime&&n.push(new c.StripLine(new Date,null,e.currentTimeTitle,e.currentTimeCssClass,!0));for(var r=0,i=void 0;i=n[r];r++){var o=l.DateUtils.parse(i.start),a=i.end?l.DateUtils.parse(i.end):null;if(o>=this.range.start&&o<=this.range.end||a&&a>=this.range.start&&a<=this.range.end){var u=o>this.range.start?o:this.range.start,d=new s.GridElementInfo;d.size.height=this.getVerticalGridLineHeight(),d.position.x=this.getPosByDate(u),d.size.width=a?this.getWidthByDateRange(u,a<this.range.end?a:this.range.end):0,d.className=i.isCurrent?"dx-gantt-tc":a?"dx-gantt-ti":"dx-gantt-tm",d.className+=i.cssClass?" "+i.cssClass:"",d.attr.title=i.title,t.push(d)}}return t},e.prototype.getElementsInRenderedTiles=function(e,t,n){var r=new Array,i=t?this.visibleTaskAreaSize.height:this.visibleTaskAreaSize.width;if(i>0)for(var o=Math.floor(n/(2*i)),s=Math.floor((n+i)/(2*i)),a=o;a<=s;a++)e[a]&&e[a].forEach((function(e){-1===r.indexOf(e)&&r.push(e)}));return r},e.prototype.isTaskInRenderedRange=function(e){var t=this.getViewItem(e),n=this.getTaskPoint(e);return t.task.isMilestone()?n.x+this.getTaskWidth(e)<this.getTotalWidth():n.x<this.getTotalWidth()},e.prototype.isTaskCutByRange=function(e){return!!this.getTaskElementInfo(e).additionalInfo.taskCut},e.prototype.checkAndCorrectElementDisplayByRange=function(e){e.parentElement.offsetLeft+e.offsetLeft+e.offsetWidth>this.getTotalWidth()&&(e.style.display="none")},e.prototype.checkAndCorrectArrowElementDisplayByRange=function(e){e.offsetLeft+e.offsetWidth>this.getTotalWidth()&&(e.style.display="none")},e.prototype.checkAndCorrectConnectorLinesByRange=function(e){if(null==e?void 0:e.length)for(var t=this.getTotalWidth(),n=0;n<e.length;n++){var r=e[n],i=!r.size.width;r.position.x>t?delete e[n]:!i&&r.position.x+r.size.width>t&&(r.size.width=t-r.position.x)}},e.prototype.getCorrectedTaskWidthByRange=function(e,t){var n=this.getTotalWidth()-this.getTaskPoint(e).x;return Math.min(n,t)},e.dxGanttPrefix="dx-gantt-",e.taskClassName=e.dxGanttPrefix+"task",e.milestoneClassName=e.dxGanttPrefix+"milestone",e.smallTaskClassName=e.dxGanttPrefix+"smallTask",e.parentTaskClassName=e.dxGanttPrefix+"parent",e.taskProgressClassName=e.dxGanttPrefix+"tPrg",e.taskTitleClassName=e.dxGanttPrefix+"taskTitle",e.titleInClassName=e.dxGanttPrefix+"titleIn",e.titleOutClassName=e.dxGanttPrefix+"titleOut",e.taskResourceClassName=e.dxGanttPrefix+"taskRes",e.arrowClassName=e.dxGanttPrefix+"arrow",e.leftArrowClassName=e.dxGanttPrefix+"LA",e.topArrowClassName=e.dxGanttPrefix+"TA",e.rightArrowClassName=e.dxGanttPrefix+"RA",e.bottomArrowClassName=e.dxGanttPrefix+"BA",e.CLASSNAMES={CONNECTOR_VERTICAL:"dx-gantt-conn-v",CONNECTOR_HORIZONTAL:"dx-gantt-conn-h"},e}();t.GridLayoutCalculator=h},7072:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ScaleCalculator=t.ScaleItemInfo=void 0;var r=n(8900),i=n(6353),o=n(9201),s=n(2449),a=function(e,t,n,r){this.start=e,this.end=t,this.position=n,this.size=r};t.ScaleItemInfo=a;var l=function(){function e(){this.firstDayOfWeek=0}return e.prototype.setSettings=function(e,t,n,r){void 0===r&&(r=0),this.range=e,this.viewType=t,this.tickSize=n,this.firstDayOfWeek=r,this.reset()},e.prototype.setViewType=function(e){this.viewType=e,this.reset()},e.prototype.reset=function(){delete this._bottomScaleItems,delete this._topScaleItems,delete this._scaleWidth},e.prototype.getScaleIndexByPos=function(e,t){null!=t||(t=this.viewType);var n=t===this.viewType?this.bottomScaleItems:this.topScaleItems,r=-1;if(n.findIndex)r=n.findIndex((function(t){return e>=t.position.x&&e<=t.position.x+t.size.width}));else{var i=n.filter((function(t){return e>=t.position.x&&e<=t.position.x+t.size.width}))[0];i&&(r=n.indexOf(i))}return r},e.prototype.getScaleBorderPosition=function(e,t){var n=this.getScaleItems(t)[e];if(n)return n.position.x+n.size.width},e.prototype.getScaleItems=function(e){return e===this.viewType?this.bottomScaleItems:e===o.DateUtils.ViewTypeToScaleMap[this.viewType]?this.topScaleItems:null},e.prototype.getScaleItem=function(e,t){return this.getScaleItems(t)[e]},e.prototype.getScaleItemAdjustedStart=function(e,t){var n=this.getScaleItems(t)[e];if(e>0)return n.start;var r=t!==this.viewType,i=r?o.DateUtils.adjustStartDateByViewType(this.range.start,this.viewType,this.firstDayOfWeek):this.getAdjustedBottomScaleItemStart(n.start,t,this.firstDayOfWeek);if(r&&t===s.ViewType.Months){var a=this.range.start;i=new Date(a.getFullYear(),a.getMonth(),1)}if(r&&t===s.ViewType.FiveYears){var l=5*Math.trunc(i.getFullYear()/5);i=new Date(l,i.getMonth(),i.getDate())}return i},Object.defineProperty(e.prototype,"topScaleItems",{get:function(){var e;return null!==(e=this._topScaleItems)&&void 0!==e||(this._topScaleItems=this.calculateTopScale(o.DateUtils.ViewTypeToScaleMap[this.viewType])),this._topScaleItems},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"bottomScaleItems",{get:function(){var e;return null!==(e=this._bottomScaleItems)&&void 0!==e||(this._bottomScaleItems=this.calculateBottomScale(this.viewType)),this._bottomScaleItems},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"scaleWidth",{get:function(){var e;return null!==(e=this._scaleWidth)&&void 0!==e||(this._scaleWidth=this.calculateScaleWidth()),this._scaleWidth},enumerable:!1,configurable:!0}),e.prototype.getFirstScaleIndexForRender=function(e){var t=this.getScaleIndexByPos(e);return t=Math.max(t-10,0)},e.prototype.getLastScaleIndexForRender=function(e){var t=this.getScaleIndexByPos(e);return t=-1===t?this.bottomScaleItems.length-1:Math.min(t+10,this.bottomScaleItems.length-1)},e.prototype.getTopScaleIndexByBottomIndex=function(e){var t=this.bottomScaleItems[e];return t?this.getScaleIndexByPos(t.position.x,o.DateUtils.ViewTypeToScaleMap[this.viewType]):-1},e.prototype.calculateBottomScale=function(e){for(var t=new Array,n=this.tickSize.width,l=this.range.start,c=0;l.getTime()<this.range.end.getTime();){var u=this.getNextScaleDate(l,e),d=l.getTime()===this.range.start.getTime(),p=u.getTime()>=this.range.end.getTime(),h=d||p||e>s.ViewType.Hours&&o.DateUtils.hasDST()?this.getRangeTickCount(l,u)*n:n;t.push(new a(l,u,new r.Point(c,void 0),new i.Size(h,0))),l=u,c+=h}return t},e.prototype.calculateTopScale=function(e){for(var t=new Array,n=this.range.end.getTime(),o=this.range.start,s=0,l=0;o.getTime()<n;)for(var c=this.getNextScaleDate(o,e),u=c.getTime(),d=l;d<this.bottomScaleItems.length;d++){var p=this.bottomScaleItems[d],h=p.start.getTime(),f=p.end.getTime();if(u>=h&&u<=f){var g=(u-h)/(f-h)*p.size.width+p.position.x;t.push(new a(o,c,new r.Point(s,void 0),new i.Size(g-s,0))),l=d,s=g,o=c;break}}return t},e.prototype.getDateInScale=function(e){if(e<0){var t=e/this.tickSize.width,n=o.DateUtils.getTickTimeSpan(this.viewType);return new Date(this.range.start.getTime()+t*n)}for(var r=0;r<this.bottomScaleItems.length;r++){var i=this.bottomScaleItems[r],s=i.size.width,a=i.position.x;if(e>=a&&e<=a+s){var l=i.start.getTime();t=(e-a)/s*(i.end.getTime()-l);return new Date(i.start.getTime()+t)}}return new Date(this.range.end)},e.prototype.getNextScaleDate=function(e,t){var n;switch(t){case s.ViewType.TenMinutes:n=this.getNextDateInTenMinutesScale(e);break;case s.ViewType.Hours:n=this.getNextDateInHoursScale(e);break;case s.ViewType.SixHours:n=this.getNextDateInSixHoursScale(e);break;case s.ViewType.Days:n=this.getNextDateInDaysScale(e);break;case s.ViewType.Weeks:n=this.getNextDateInWeeksScale(e,this.firstDayOfWeek);break;case s.ViewType.Months:n=this.getNextDateInMonthsScale(e);break;case s.ViewType.Quarter:n=this.getNextDateInQuartersScale(e);break;case s.ViewType.Years:n=this.getNextDateInYearsScale(e);break;case s.ViewType.FiveYears:n=this.getNextDateInFiveYearsScale(e)}return n.getTime()>this.range.end.getTime()&&(n=this.range.end),n},e.prototype.getNextTimeBySpan=function(e,t){return(Math.trunc(e/t)+1)*t},e.prototype.getNextDateInTenMinutesScale=function(e){var t=this.getNextTimeBySpan(e.getMinutes(),10);return new Date(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),t)},e.prototype.getNextDateInHoursScale=function(e){return new Date(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours()+1)},e.prototype.getNextDateInSixHoursScale=function(e){var t=this.getNextTimeBySpan(e.getHours(),6);return new Date(e.getFullYear(),e.getMonth(),e.getDate(),t)},e.prototype.getNextDateInDaysScale=function(e){return new Date(e.getFullYear(),e.getMonth(),e.getDate()+1)},e.prototype.getNextDateInWeeksScale=function(e,t){return void 0===t&&(t=0),new Date(e.getFullYear(),e.getMonth(),e.getDate()-e.getDay()+t+7)},e.prototype.getNextDateInMonthsScale=function(e){return new Date(e.getFullYear(),e.getMonth()+1,1)},e.prototype.getNextDateInQuartersScale=function(e){var t=this.getNextTimeBySpan(e.getMonth(),3);return new Date(e.getFullYear(),t,1)},e.prototype.getNextDateInYearsScale=function(e){return new Date(e.getFullYear()+1,0,1)},e.prototype.getNextDateInFiveYearsScale=function(e){var t=this.getNextTimeBySpan(e.getFullYear(),5);return new Date(t,0,1)},e.prototype.getAdjustedBottomScaleItemStart=function(e,t,n){switch(void 0===n&&(n=0),t){case s.ViewType.TenMinutes:return new Date(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),10*Math.floor(e.getMinutes()/10));case s.ViewType.SixHours:return new Date(e.getFullYear(),e.getMonth(),e.getDate(),6*Math.floor(e.getHours()/6));case s.ViewType.Hours:return new Date(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours());case s.ViewType.Days:return new Date(e.getFullYear(),e.getMonth(),e.getDate());case s.ViewType.Weeks:return new Date(e.getFullYear(),e.getMonth(),e.getDate()-e.getDay()+n);case s.ViewType.Months:return new Date(e.getFullYear(),e.getMonth(),1);case s.ViewType.Quarter:return new Date(e.getFullYear(),3*Math.floor(e.getMonth()/3),1);case s.ViewType.Years:return new Date(e.getFullYear(),0,1);default:return new Date}},e.prototype.calculateScaleWidth=function(){return this.bottomScaleItems.reduce((function(e,t){return e+t.size.width}),0)},e.prototype.getScaleItemColSpan=function(e){return e.valueOf()===this.viewType.valueOf()?1:this.viewType===s.ViewType.TenMinutes?6:this.viewType===s.ViewType.Hours?24:this.viewType===s.ViewType.SixHours?4:this.viewType===s.ViewType.Days?7:this.viewType===s.ViewType.Weeks?4.29:this.viewType===s.ViewType.Months?12:this.viewType===s.ViewType.Quarter?4:this.viewType===s.ViewType.Years?5:1},e.prototype.getRangeTickCount=function(e,t){return o.DateUtils.getRangeTickCount(e,t,this.viewType)},e}();t.ScaleCalculator=l},9377:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TaskAreaContainer=void 0;var n=function(){function e(e,t){this.element=e,this.onScrollHandler=function(){t.updateView()},this.element.addEventListener("scroll",this.onScrollHandler)}return Object.defineProperty(e.prototype,"scrollTop",{get:function(){return this.element.scrollTop},set:function(e){this.element.scrollTop=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"scrollLeft",{get:function(){return this.element.scrollLeft},set:function(e){this.element.scrollLeft=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"scrollWidth",{get:function(){return this.element.scrollWidth},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"scrollHeight",{get:function(){return this.element.scrollHeight},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isExternal",{get:function(){return!1},enumerable:!1,configurable:!0}),e.prototype.getWidth=function(){return this.element.offsetWidth},e.prototype.getHeight=function(){return this.element.offsetHeight},e.prototype.getElement=function(){return this.element},e.prototype.detachEvents=function(){this.element.removeEventListener("scroll",this.onScrollHandler)},e}();t.TaskAreaContainer=n},6923:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ConnectorLinesRender=void 0;var r=n(658),i=n(1855),o=function(){function e(e){this._connectorLinesToElementsMap={},this._renderedConnectorLines=[],this._renderHelper=e}return Object.defineProperty(e.prototype,"taskEditController",{get:function(){return this._renderHelper.taskEditController},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"taskAreaContainerScrollTop",{get:function(){return this._renderHelper.ganttViewTaskAreaContainerScrollTop},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"gridLayoutCalculator",{get:function(){return this._renderHelper.gridLayoutCalculator},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"connectorLinesToElementsMap",{get:function(){return this._connectorLinesToElementsMap},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"taskArea",{get:function(){return this._renderHelper.taskArea},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"invalidTaskDependencies",{get:function(){return this._renderHelper.invalidTaskDependencies},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"showDependencies",{get:function(){return this._renderHelper.showDependencies},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"renderedConnectorLines",{get:function(){return this._renderedConnectorLines},enumerable:!1,configurable:!0}),e.prototype.reset=function(){this._connectorLinesToElementsMap={},this._renderedConnectorLines=[]},e.prototype.createConnectorLineElement=function(e){if(this.showDependencies){var t=e.attr["dependency-id"];if(!this.invalidTaskDependencies.some((function(e){return e.id==t}))){this.taskEditController.isDependencySelected(t)&&(e.className=e.className+" active");var n=e.className.indexOf(i.GridLayoutCalculator.arrowClassName)>-1,o=r.RenderElementUtils.create(e,null,this.taskArea,this.connectorLinesToElementsMap);return n&&this.gridLayoutCalculator.checkAndCorrectArrowElementDisplayByRange(o),o}}},e.prototype.removeConnectorLineElement=function(e){r.RenderElementUtils.remove(e,null,this.taskArea,this.connectorLinesToElementsMap)},e.prototype.recreateConnectorLineElement=function(e,t){var n=this;void 0===t&&(t=!1);var r=[];this._renderedConnectorLines=this.renderedConnectorLines.filter((function(t){return t.attr["dependency-id"]!=e||(r.push(t),!1)}));var i=r.length>0;r.forEach((function(e){n.removeConnectorLineElement(e)})),r=this.gridLayoutCalculator.updateTileToConnectorLinesMap(e),(i||t)&&r.forEach((function(e){n.createConnectorLineElement(e),n.renderedConnectorLines.push(e)}))},e.prototype.recreateConnectorLineElements=function(){var e=this,t=this.gridLayoutCalculator.getRenderedConnectorLines(this.taskAreaContainerScrollTop);r.RenderElementUtils.recreate(this.renderedConnectorLines,t,(function(t){e.removeConnectorLineElement(t)}),(function(t){e.createConnectorLineElement(t)})),this._renderedConnectorLines=t},e.prototype.updateRenderedConnectorLinesId=function(e,t){for(var n in this._renderedConnectorLines.forEach((function(n){n.attr["dependency-id"]===e&&(n.attr["dependency-id"]=t)})),this.connectorLinesToElementsMap)if(Object.prototype.hasOwnProperty.call(this.connectorLinesToElementsMap,n)){var r=this.connectorLinesToElementsMap[n];r.getAttribute("dependency-id")===e&&r.setAttribute("dependency-id",t)}this.gridLayoutCalculator.updateTileToConnectorLinesMap(e),this.gridLayoutCalculator.updateTileToConnectorLinesMap(t)},e}();t.ConnectorLinesRender=o},1419:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.CustomTaskRender=void 0;var r=n(2449),i=n(658),o=function(){function e(e,t){this._pendingTemplateFuncsToRender=[],this._renderHelper=e,this._taskRender=t}return Object.defineProperty(e.prototype,"gridLayoutCalculator",{get:function(){return this._renderHelper.gridLayoutCalculator},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"tickSize",{get:function(){return this._renderHelper.tickSize},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"taskTitlePosition",{get:function(){return this._renderHelper.taskTitlePosition},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"taskElements",{get:function(){return this._taskRender.taskElements},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"taskArea",{get:function(){return this._renderHelper.taskArea},enumerable:!1,configurable:!0}),e.prototype.getViewItem=function(e){return this._renderHelper.getViewItem(e)},e.prototype.getTask=function(e){return this._renderHelper.getTask(e)},e.prototype.destroyTemplate=function(e){this._renderHelper.destroyTemplate(e)},e.prototype.getTaskDependencies=function(e){return this._renderHelper.getTaskDependencies(e)},e.prototype.getTaskResources=function(e){return this._renderHelper.getTaskResources(e)},e.prototype.attachEventsOnTask=function(e){this._renderHelper.attachEventsOnTask(e)},e.prototype.recreateConnectorLineElement=function(e,t){void 0===t&&(t=!1),this._renderHelper.recreateConnectorLineElement(e,t)},e.prototype.createTaskSelectionElement=function(e){this._taskRender.createTaskSelectionElement(e)},e.prototype.createCustomTaskElement=function(e,t){if(this._saveTemplateFuncToStack(e,t),!this._hasRepeatedTemplateRenderCall(e)){var n=this.getViewItem(e);n.isCustom=!1;var r=document.createElement("DIV"),i=this.createCustomTaskInformation(e);n.isCustom=!0,t(r,i,this.onTaskTemplateContainerRendered.bind(this),e)}},e.prototype.onTaskTemplateContainerRendered=function(e,t){var n=this;if(this._hasRepeatedTemplateRenderCall(t)){e&&this._renderHelper.destroyTemplate(e);var r=this._getLastPendingTemplateFunc(t);setTimeout((function(){return n.createCustomTaskElement(t,r)}))}else this.drawCustomTask(e,t);this._clearTemplateFuncsStack(t)},e.prototype._saveTemplateFuncToStack=function(e,t){var n,r;null!==(n=(r=this._pendingTemplateFuncsToRender)[e])&&void 0!==n||(r[e]=[]),this._pendingTemplateFuncsToRender[e].push(t)},e.prototype._clearTemplateFuncsStack=function(e){this._pendingTemplateFuncsToRender[e]=[]},e.prototype._hasRepeatedTemplateRenderCall=function(e){return this._pendingTemplateFuncsToRender[e].length>1},e.prototype._getLastPendingTemplateFunc=function(e){var t=this._pendingTemplateFuncsToRender[e];return t[t.length-1]},e.prototype.createCustomTaskWrapperElement=function(e,t){i.RenderElementUtils.create(t,e,this.taskArea,this.taskElements)},e.prototype.createCustomTaskVisualElement=function(e,t){return i.RenderElementUtils.create(t,e,this.taskElements[e])},e.prototype.drawCustomTask=function(e,t){var n=this;if(this.taskElements[t]){var r=this.getViewItem(t);if(r.visible=!!e.innerHTML,this.taskElements[t].innerHTML=e.innerHTML,r.size.height=this.taskElements[t].offsetHeight,r.size.width=this.taskElements[t].offsetWidth,this.destroyTemplate(this.taskElements[t]),this._taskRender.removeTaskElement(t),r.visible){var i=this.gridLayoutCalculator.getTaskWrapperElementInfo(t);this.createCustomTaskWrapperElement(t,i),this.taskElements[t].appendChild(e),this.attachEventsOnTask(t)}else{var o=this.getTaskDependencies(r.task.internalId);o.length&&(this._taskRender.addInvalidTaskDependencies(o),o.forEach((function(e){return n.recreateConnectorLineElement(e.internalId,!0)})))}this._taskRender.isHighlightRowElementAllowed(t)&&this._taskRender.createHighlightRowElement(t),r.selected&&this.createTaskSelectionElement(t)}},e.prototype.createCustomTaskInformation=function(e){var t=this.getTask(e),n=this.getViewItem(e),i=this.gridLayoutCalculator.getTaskWrapperElementInfo(e),o=this.gridLayoutCalculator.getTaskElementInfo(e,this.taskTitlePosition!==r.TaskTitlePosition.Inside);this.createCustomTaskWrapperElement(e,i);var s=this.createCustomTaskVisualElement(e,o);this._taskRender.createTaskTextElement(e,s);var a=this.getTaskResources(t.id);return{cellSize:this.tickSize,isMilestone:t.isMilestone(),isParent:!!(null==n?void 0:n.children.length),taskData:t,taskHTML:s,taskPosition:i.position,taskResources:a,taskSize:o.size}},e}();t.CustomTaskRender=o},9385:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.EtalonsHelper=void 0;var r=n(6907),i=n(2449),o=n(1391),s=n(658),a=function(){function e(e){this._renderHelper=e}return Object.defineProperty(e.prototype,"mainElement",{get:function(){return this._renderHelper.mainElement},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"etalonSizeValues",{get:function(){return this._renderHelper.etalonSizeValues},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"scaleCount",{get:function(){return this._renderHelper.scaleCount},enumerable:!1,configurable:!0}),e.prototype.getScaleItemTextTemplate=function(e){return this._renderHelper.getScaleItemTextTemplate(e)},e.prototype.getHeaderHeight=function(){return this._renderHelper.getHeaderHeight()},e.prototype.getSmallTaskWidth=function(e){return this._renderHelper.getSmallTaskWidth(e)},e.prototype.createEtalonElementsContainer=function(){var e=document.createElement("DIV");return e.style.visibility="hidden",e.style.position="absolute",e.style.left="-1000px",this.mainElement.appendChild(e),e},e.prototype.createEtalonElements=function(e){var t=[],n=s.RenderElementUtils.create(new o.GridElementInfo("dx-gantt-taskWrapper"),null,e),r=s.RenderElementUtils.create(new o.GridElementInfo("dx-gantt-task"),null,n);s.RenderElementUtils.create(new o.GridElementInfo("dx-gantt-taskTitle dx-gantt-titleIn"),null,r).innerText="WWW",t.push(n);var i=s.RenderElementUtils.create(new o.GridElementInfo("dx-gantt-taskWrapper"),null,e);s.RenderElementUtils.create(new o.GridElementInfo("dx-gantt-task dx-gantt-milestone"),null,i),t.push(i);for(var a=["dx-gantt-conn-h","dx-gantt-arrow","dx-gantt-si","dx-gantt-taskTitle dx-gantt-titleOut"],l=0;l<a.length;l++){var c=new o.GridElementInfo(a[l]);t.push(s.RenderElementUtils.create(c,null,e))}var u=s.RenderElementUtils.create(new o.GridElementInfo("dx-gantt-taskWrapper"),null,e),d=s.RenderElementUtils.create(new o.GridElementInfo("dx-gantt-task dx-gantt-parent"),null,u);return s.RenderElementUtils.create(new o.GridElementInfo("dx-gantt-taskTitle dx-gantt-titleIn"),null,d).innerText="WWW",t.push(u),t},e.prototype.calculateEtalonSizeValues=function(){var e=this.createEtalonElementsContainer(),t=this.createEtalonElements(e);this.calculateEtalonSizeValuesCore(t),this.mainElement.removeChild(e)},e.prototype.calculateEtalonSizeValuesCore=function(e){this.etalonSizeValues.taskHeight=e[0].firstChild.offsetHeight,this.etalonSizeValues.milestoneWidth=e[1].firstChild.offsetWidth,this.etalonSizeValues.taskWrapperTopPadding=r.DomUtils.pxToInt(r.DomUtils.getCurrentStyle(e[0]).paddingTop),this.etalonSizeValues.connectorLineThickness=r.DomUtils.getVerticalBordersWidth(e[2]),this.etalonSizeValues.connectorArrowWidth=r.DomUtils.getHorizontalBordersWidth(e[3]);for(var t=0;t<=i.ViewType.Years;t++)e[4].innerText=this.getScaleItemTextTemplate(t),this.etalonSizeValues.scaleItemWidths[t]=e[4].offsetWidth;this.etalonSizeValues.smallTaskWidth=this.getSmallTaskWidth(r.DomUtils.getCurrentStyle(e[0].firstChild.firstChild).paddingLeft),this.etalonSizeValues.outsideTaskTextDefaultWidth=r.DomUtils.pxToFloat(r.DomUtils.getCurrentStyle(e[5]).width),this.etalonSizeValues.scaleItemHeight=this.getHeaderHeight()/this.scaleCount,this.etalonSizeValues.parentTaskHeight=e[e.length-1].firstChild.offsetHeight},e}();t.EtalonsHelper=a},4289:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ScaleCellPreparedArguments=void 0;var n=function(){function e(e){this.info=e}return Object.defineProperty(e.prototype,"scaleIndex",{get:function(){var e;return null===(e=this.info)||void 0===e?void 0:e.scaleIndex},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"scaleType",{get:function(){var e;return null===(e=this.info)||void 0===e?void 0:e.scaleType},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"start",{get:function(){var e,t;return null===(t=null===(e=this.info)||void 0===e?void 0:e.range)||void 0===t?void 0:t.start},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"end",{get:function(){var e,t;return null===(t=null===(e=this.info)||void 0===e?void 0:e.range)||void 0===t?void 0:t.end},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"scaleElement",{get:function(){var e;return null===(e=this.info)||void 0===e?void 0:e.scaleElement},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"separatorElement",{get:function(){var e;return null===(e=this.info)||void 0===e?void 0:e.separatorElement},enumerable:!1,configurable:!0}),e}();t.ScaleCellPreparedArguments=n},2121:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.MainElementsRender=void 0;var n=function(){function e(){}return e.prototype.createMainElement=function(e){var t=document.createElement("DIV");return t.style.width=e.offsetWidth+"px",t.style.height=e.offsetHeight+"px",t},e.prototype.createHeader=function(){var e=document.createElement("DIV");return e.className="dx-gantt-header",e},e}();t.MainElementsRender=n},1027:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoWorkingIntervalRender=void 0;var r=n(658),i=function(){function e(e){this._noWorkingIntervalsToElementsMap={},this._renderedNoWorkingIntervals=[],this._renderHelper=e}return Object.defineProperty(e.prototype,"noWorkingIntervalsToElementsMap",{get:function(){return this._noWorkingIntervalsToElementsMap},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"taskAreaContainerScrollLeft",{get:function(){return this._renderHelper.ganttTaskAreaContainerScrollLeft},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"gridLayoutCalculator",{get:function(){return this._renderHelper.gridLayoutCalculator},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"taskArea",{get:function(){return this._renderHelper.taskArea},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"renderedNoWorkingIntervals",{get:function(){return this._renderedNoWorkingIntervals},set:function(e){this._renderedNoWorkingIntervals=e},enumerable:!1,configurable:!0}),e.prototype.reset=function(){this._noWorkingIntervalsToElementsMap={},this._renderedNoWorkingIntervals=[]},e.prototype.createNoWorkingIntervalElement=function(e){return r.RenderElementUtils.create(e,null,this.taskArea,this.noWorkingIntervalsToElementsMap)},e.prototype.removeNoWorkingIntervalElement=function(e){r.RenderElementUtils.remove(e,null,this.taskArea,this.noWorkingIntervalsToElementsMap)},e.prototype.recreateNoWorkingIntervalElements=function(){var e=this,t=this.gridLayoutCalculator.getRenderedNoWorkingIntervals(this.taskAreaContainerScrollLeft);r.RenderElementUtils.recreate(this.renderedNoWorkingIntervals,t,(function(t){e.removeNoWorkingIntervalElement(t)}),(function(t){e.createNoWorkingIntervalElement(t)})),this.renderedNoWorkingIntervals=t},e}();t.NoWorkingIntervalRender=i},658:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.RenderElementUtils=void 0;var n=function(){function e(){}return e.create=function(e,t,n,r){var i=document.createElement("DIV");for(var o in e.assignToElement(i),n.appendChild(i),r&&(r instanceof Array&&null!==t?r[t]=i:r[e.id]=i),e.attr)Object.prototype.hasOwnProperty.call(e.attr,o)&&i.setAttribute(o,e.attr[o]);for(var o in e.style)Object.prototype.hasOwnProperty.call(e.style,o)&&(i.style[o]=e.style[o]);return i},e.remove=function(e,t,n,r){var i;r instanceof Array&&null!==t?(i=r[t],delete r[t]):(i=r[e.id],delete r[e.id]),i&&i.parentNode==n&&n.removeChild(i)},e.recreate=function(e,t,n,r){e.filter((function(e){return-1===t.indexOf(e)})).forEach((function(e){n(e)})),t.filter((function(t){return-1===e.indexOf(t)})).forEach((function(e){r(e)}))},e}();t.RenderElementUtils=n},1074:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.RenderHelper=void 0;var r=n(6923),i=n(9385),o=n(8877),s=n(2366),a=n(1855),l=n(1027),c=n(4966),u=n(6700),d=n(8148),p=n(3682),h=n(2349),f=n(2290),g=n(2121),y=n(6353),m=n(9377),v=n(8380),T=function(){function e(e){this.hlRowElements=[],this.renderedColIndices=[],this.renderedRowIndices=[],this.invalidTaskDependencies=[],this.etalonSizeValues=new o.EtalonSizeValues,this._gridLayoutCalculator=new a.GridLayoutCalculator,this._ganttView=e,this._connectorLinesRender=new r.ConnectorLinesRender(this),this._etalonsHelper=new i.EtalonsHelper(this),this._noWorkingIntervalRender=new l.NoWorkingIntervalRender(this),this._resourceRender=new c.ResourseRender(this),this._scaleRender=new u.ScaleRender(this),this._stripLinesRender=new d.StripLinesRender(this),this._taskAreaRender=new h.TaskAreaRender(this),this._taskRender=new f.TaskRender(this),this._mainElementsRender=new g.MainElementsRender}return e.prototype.reset=function(){this.invalidTaskDependencies=[],this._taskAreaRender.reset(),this._taskRender.reset(),this._taskArea.innerHTML="",this._scaleRender.reset(),this.hlRowElements=[],this.renderedRowIndices=[],this.renderedColIndices=[],this._connectorLinesRender.reset(),this._stripLinesRender.reset(),this._noWorkingIntervalRender.reset()},e.prototype.createMainElement=function(e){this.mainElement=this._mainElementsRender.createMainElement(e),e.appendChild(this.mainElement)},e.prototype.createHeader=function(){this.header=this._mainElementsRender.createHeader(),this.mainElement.appendChild(this.header)},e.prototype.init=function(e,t,n,r,i){void 0===i&&(i=0),this._elementTextHelper.setFont(this.mainElement),this.setupHelpers(e,t,n,r,i),this.setSizeForTaskArea(),this.createTimeScale(),this._taskAreaManager=new p.TaskAreaManager(this._ganttView.taskAreaEventsListener,this.taskArea,this.tickSize)},e.prototype.initMarkup=function(e){this._elementTextHelper=new v.ElementTextHelper(this.ganttViewSettings.cultureInfo),this.createMainElement(e),this.createHeader(),this._etalonsHelper.calculateEtalonSizeValues(),this._taskAreaRender.createTaskAreaContainer()},e.prototype.processScroll=function(e){this._taskAreaRender.recreateTaskAreaBordersAndTaskElements(e),e?this._connectorLinesRender.recreateConnectorLineElements():(this._noWorkingIntervalRender.recreateNoWorkingIntervalElements(),this._stripLinesRender.recreateStripLines(),this._scaleRender.recreateScalesElements())},Object.defineProperty(e.prototype,"ganttViewSettings",{get:function(){return this._ganttView.settings},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"taskTextHeightKey",{get:function(){return s.GanttView.taskTextHeightKey},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"showResources",{get:function(){return this.ganttViewSettings.showResources},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"showDependencies",{get:function(){return this.ganttViewSettings.showDependencies},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"viewModelItems",{get:function(){return this._ganttView.viewModel.items},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"stripLines",{get:function(){return this.ganttViewSettings.stripLines},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"range",{get:function(){return this._ganttView.range},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"viewType",{get:function(){return this.ganttViewSettings.viewType},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"taskTitlePosition",{get:function(){return this.ganttViewSettings.taskTitlePosition},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"tickSize",{get:function(){return this._ganttView.tickSize},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"ganttViewTaskAreaContainerScrollTop",{get:function(){return this._ganttView.taskAreaContainerScrollTop},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"ganttTaskAreaContainerScrollLeft",{get:function(){return this._ganttView.taskAreaContainerScrollLeft},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"scaleCount",{get:function(){return this._ganttView.scaleCount},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"areHorizontalBordersEnabled",{get:function(){return this.ganttViewSettings.areHorizontalBordersEnabled},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"taskEditController",{get:function(){return this._ganttView.taskEditController},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"dispatcher",{get:function(){return this._ganttView.dispatcher},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"taskArea",{get:function(){return this._taskArea},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"taskAreaManager",{get:function(){var e;return null!==(e=this._taskAreaManager)&&void 0!==e||(this._taskAreaManager=new p.TaskAreaManager(this._ganttView.taskAreaEventsListener,this.taskArea,this.tickSize)),this._taskAreaManager},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"taskAreaContainerScrollTop",{get:function(){return this._taskAreaRender.taskAreaContainer.scrollTop},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"taskAreaContainerScrollLeft",{get:function(){return this._taskAreaRender.taskAreaContainer.scrollLeft},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"taskAreaContainer",{get:function(){return this._taskAreaRender.taskAreaContainer},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isExternalTaskAreaContainer",{get:function(){return this._taskAreaRender.taskAreaContainer.isExternal},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"fakeTaskWrapper",{get:function(){return this._taskRender.fakeTaskWrapper},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"taskElements",{get:function(){return this._taskRender.taskElements},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"selectionElements",{get:function(){return this._taskRender.selectionElements},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"scaleElements",{get:function(){return this._scaleRender.scaleElements},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"scaleBorders",{get:function(){return this._scaleRender.scaleBorders},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"timeScaleContainer",{get:function(){return this._scaleRender.timeScaleContainer},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"gridLayoutCalculator",{get:function(){return this._gridLayoutCalculator},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"etalonScaleItemWidths",{get:function(){return this.etalonSizeValues.scaleItemWidths[this.viewType]},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"elementTextHelperCultureInfo",{get:function(){return this._elementTextHelper.cultureInfo},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"noWorkingIntervalsToElementsMap",{get:function(){return this._noWorkingIntervalRender.noWorkingIntervalsToElementsMap},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"stripLinesMap",{get:function(){return this._stripLinesRender.stripLinesMap},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"renderedConnectorLines",{get:function(){return this._connectorLinesRender.renderedConnectorLines},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"allConnectorLines",{get:function(){return this.gridLayoutCalculator.tileToDependencyMap.reduce((function(e,t){return e.concat(t)}),[])},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"resourcesElements",{get:function(){return this._resourceRender.resourcesElements},enumerable:!1,configurable:!0}),e.prototype.setupHelpers=function(e,t,n,r,i){void 0===i&&(i=0);var o=new y.Size(this._taskAreaRender.taskAreaContainer.getWidth(),this._taskAreaRender.taskAreaContainer.getHeight()),s=this._taskAreaRender.taskAreaContainer.getHeight()-this._taskAreaRender.taskAreaContainer.getElement().clientHeight;this._gridLayoutCalculator.setSettings(o,e,this.etalonSizeValues,t,r,n,s,i),this._elementTextHelper.setSettings(t.start.getTime(),n,r.items)},e.prototype.resetAndUpdate=function(e,t,n,r,i){void 0===i&&(i=0);var o=this.getTaskAreaContainerScrollLeft();this.reset(),this.setupHelpers(e,t,n,r,i),this._scaleRender.createTimeScaleAreas(),this.setSizeForTaskArea(),this.setTaskAreaContainerScrollLeft(o)},e.prototype.createConnectorLines=function(){this._gridLayoutCalculator.createTileToConnectorLinesMap(),this._connectorLinesRender.recreateConnectorLineElements()},e.prototype.getTargetDateByPos=function(e){return this._gridLayoutCalculator.getDateByPos(this._taskAreaRender.taskAreaContainer.scrollLeft+e)},e.prototype.getExternalTaskAreaContainer=function(e){return this._ganttView.getExternalTaskAreaContainer(e)},e.prototype.prepareExternalTaskAreaContainer=function(e,t){return this._ganttView.prepareExternalTaskAreaContainer(e,t)},e.prototype.isAllowTaskAreaBorders=function(e){return this._ganttView.allowTaskAreaBorders(e)},e.prototype.getHeaderHeight=function(){return this._ganttView.getHeaderHeight()},e.prototype.getViewItem=function(e){return this._ganttView.getViewItem(e)},e.prototype.getTask=function(e){return this._ganttView.getTask(e)},e.prototype.hasTaskTemplate=function(){return!!this._ganttView.settings.taskContentTemplate},e.prototype.destroyTemplate=function(e){this._ganttView.destroyTemplate(e)},e.prototype.getTaskDependencies=function(e){return this._ganttView.getTaskDependencies(e)},e.prototype.getTaskResources=function(e){return this._ganttView.getTaskResources(e)},e.prototype.isHighlightRowElementAllowed=function(e){return this._ganttView.isHighlightRowElementAllowed(e)},e.prototype.updateRenderedConnectorLinesId=function(e,t){this._connectorLinesRender.updateRenderedConnectorLinesId(e,t)},e.prototype.recreateConnectorLineElement=function(e,t){void 0===t&&(t=!1),this._connectorLinesRender.recreateConnectorLineElement(e,t)},e.prototype.recreateConnectorLineElemensts=function(){this._connectorLinesRender.recreateConnectorLineElements()},e.prototype.setMainElementWidth=function(e){this.mainElement.style.width=e+"px"},e.prototype.setMainElementHeight=function(e){this.mainElement.style.height=e+"px"},e.prototype.createResources=function(e){this._resourceRender.createResourcesWrapperElement(e),this._resourceRender.createResources(e)},e.prototype.createTimeScale=function(){this._scaleRender.createTimeScaleContainer(this.header),this._scaleRender.createTimeScaleAreas()},e.prototype.setTimeScaleContainerScrollLeft=function(e){this._scaleRender.setTimeScaleContainerScrollLeft(e)},e.prototype.recreateStripLines=function(){this._stripLinesRender.recreateStripLines&&this._stripLinesRender.recreateStripLines()},e.prototype.createTaskArea=function(e){this._taskArea=this._taskAreaRender.createTaskArea(),this._taskArea.setAttribute("task-edit-enabled",this.isTaskUpdateAllowed().toString()),e.appendChild(this._taskArea)},e.prototype.isTaskUpdateAllowed=function(){var e=this.ganttViewSettings.editing;return e.enabled&&e.allowTaskUpdate},e.prototype.setSizeForTaskArea=function(){var e=this.getTaskAreaWidth(),t=this.getTaskAreaHeight();this._taskAreaRender.setSizeForTaskArea(e,t),this._ganttView.onTaskAreaSizeChanged({width:e,height:t})},e.prototype.getTaskAreaWidth=function(){return this.gridLayoutCalculator.getTotalWidth()},e.prototype.getTaskAreaHeight=function(){return this.gridLayoutCalculator.getVerticalGridLineHeight()},e.prototype.getTaskAreaContainerScrollLeft=function(){return this._taskAreaRender.taskAreaContainer.scrollLeft},e.prototype.setTaskAreaContainerScrollLeft=function(e){this._taskAreaRender.taskAreaContainer.scrollLeft=e},e.prototype.setTaskAreaContainerScrollLeftToDate=function(e,t){this._taskAreaRender.taskAreaContainer.scrollLeft=Math.round(this._gridLayoutCalculator.getPosByDate(e))+t},e.prototype.getTaskAreaContainer=function(e){return new m.TaskAreaContainer(e,this._ganttView)},e.prototype.prepareTaskAreaContainer=function(){this._taskAreaRender.prepareTaskAreaContainer()},e.prototype.getTaskAreaContainerWidth=function(){return this._taskAreaRender.taskAreaContainer.getWidth()},e.prototype.createHighlightRowElement=function(e){this._taskAreaRender.createHighlightRowElement(e)},e.prototype.getSmallTaskWidth=function(e){return this._taskRender.getSmallTaskWidth(e)},e.prototype.createTaskElement=function(e){this._taskRender.createTaskElement(e,this._ganttView.settings.taskContentTemplate)},e.prototype.removeTaskElement=function(e){this._taskRender.removeTaskElement(e)},e.prototype.recreateTaskElement=function(e){this._taskRender.recreateTaskElement(e)},e.prototype.createDefaultTaskElement=function(e){this._taskRender.createDefaultTaskElement(e)},e.prototype.getScaleItemText=function(e,t){var n=this._gridLayoutCalculator.getScaleItemStart(e,t);return this.getScaleItemTextByStart(n,t)},e.prototype.getScaleItemTextByStart=function(e,t){return this._elementTextHelper.getScaleItemText(e,t)},e.prototype.getTextWidth=function(e){return this._elementTextHelper.getTextWidth(e)},e.prototype.getTaskVisibility=function(e){return this.gridLayoutCalculator.isTaskInRenderedRange(e)&&this._elementTextHelper.getTaskVisibility(e)},e.prototype.getTaskResourcesVisibility=function(e){return this.getTaskVisibility(e)&&!this.gridLayoutCalculator.isTaskCutByRange(e)},e.prototype.getScaleItemTextTemplate=function(e){return this._elementTextHelper.getScaleItemTextTemplate(e)},e.prototype.getTaskText=function(e){return this._elementTextHelper.getTaskText(e)},e.prototype.taskAreaManagerDetachEvents=function(){this.taskAreaManager.detachEvents()},e.prototype.attachEventsOnTask=function(e){this.taskAreaManager.attachEventsOnTask(this._taskRender.taskElements[e])},e.prototype.detachEventsOnTask=function(e){this.taskAreaManager.detachEventsOnTask(this._taskRender.taskElements[e])},e}();t.RenderHelper=T},4966:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ResourseRender=void 0;var r=n(658),i=function(){function e(e){this._resourcesElements=[],this._renderHelper=e}return Object.defineProperty(e.prototype,"gridLayoutCalculator",{get:function(){return this._renderHelper.gridLayoutCalculator},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"taskArea",{get:function(){return this._renderHelper.taskArea},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"resourcesElements",{get:function(){return this._resourcesElements},enumerable:!1,configurable:!0}),e.prototype.getViewItem=function(e){return this._renderHelper.getViewItem(e)},e.prototype.getTaskResourcesVisibility=function(e){return this._renderHelper.getTaskResourcesVisibility(e)},e.prototype.createResources=function(e){for(var t=this.getViewItem(e).resources.items,n=0;n<t.length;n++)this.createResourceElement(e,t[n])},e.prototype.createResourcesWrapperElement=function(e){var t=this.gridLayoutCalculator.getTaskResourcesWrapperElementInfo(e);r.RenderElementUtils.create(t,e,this.taskArea,this.resourcesElements),this.resourcesElements[e].style.display=this.getTaskResourcesVisibility(e)?"":"none"},e.prototype.createResourceElement=function(e,t){var n=this.gridLayoutCalculator.getTaskResourceElementInfo();t.color&&(n.style.backgroundColor=t.color);var i=r.RenderElementUtils.create(n,e,this.resourcesElements[e]);i.innerText=t.text,this.gridLayoutCalculator.checkAndCorrectElementDisplayByRange(i)},e}();t.ResourseRender=i},6700:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ScaleRender=void 0;var r=n(6907),i=n(2449),o=n(9201),s=n(4289),a=n(658),l=function(){function e(e){this._scaleBorders=[],this._scaleElements=[],this._renderedScaleItemIndices=[],this._timeScaleAreas=new Array,this._renderHelper=e}return Object.defineProperty(e.prototype,"gridLayoutCalculator",{get:function(){return this._renderHelper.gridLayoutCalculator},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"etalonSizeValues",{get:function(){return this._renderHelper.etalonSizeValues},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"timeScaleContainer",{get:function(){return this._timeScaleContainer},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"scaleCount",{get:function(){return this._renderHelper.scaleCount},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"range",{get:function(){return this._renderHelper.range},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"viewType",{get:function(){return this._renderHelper.viewType},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"timeScaleAreas",{get:function(){return this._timeScaleAreas},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"scaleElements",{get:function(){return this._scaleElements},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"scaleBorders",{get:function(){return this._scaleBorders},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"renderedColIndices",{get:function(){return this._renderHelper.renderedColIndices},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"renderedScaleItemIndices",{get:function(){return this._renderedScaleItemIndices},enumerable:!1,configurable:!0}),e.prototype.getScaleItemText=function(e,t){return this._renderHelper.getScaleItemText(e,t)},e.prototype.getTaskAreaWidth=function(){return this._renderHelper.getTaskAreaWidth()},e.prototype.reset=function(){this._scaleBorders=[],this._scaleElements=[],this._renderedScaleItemIndices=[],this._timeScaleAreas=[],this._timeScaleContainer.innerHTML=""},e.prototype.setTimeScaleContainerScrollLeft=function(e){this._timeScaleContainer.scrollLeft=e},e.prototype.createTimeScaleContainer=function(e){var t=document.createElement("DIV");t.className="dx-gantt-tsac",t.style.height=this.etalonSizeValues.scaleItemHeight*this.scaleCount+"px",this._timeScaleContainer=t,e.appendChild(this.timeScaleContainer)},e.prototype.createTimeScaleArea=function(){var e=document.createElement("DIV");return e.className="dx-gantt-tsa",e.style.width=this.getTaskAreaWidth()+"px",e.style.height=this.etalonSizeValues.scaleItemHeight+"px",this.timeScaleContainer.appendChild(e),this.timeScaleAreas.unshift(e),e},e.prototype.createTimeScaleAreas=function(){for(var e=0;e<this.scaleCount;e++)this.createTimeScaleArea()},e.prototype.createScaleElementCore=function(e,t,n,r){return r[n]||(r[n]=[]),a.RenderElementUtils.create(t,e,this.timeScaleAreas[n],r[n])},e.prototype.createScaleElement=function(e,t,n,o){var s=this._renderHelper.getTextWidth("a"),a=this.createScaleElementCore(e,o,t,this.scaleElements);if(a.style.lineHeight=this.etalonSizeValues.scaleItemHeight+"px",(null==o?void 0:o.size.width)>5*s){var l=this.getScaleItemText(e,n);a.innerText=l,n===i.ViewType.Quarter&&(a.style.padding="0");var c=getComputedStyle(a);o.size.width-r.DomUtils.pxToInt(c.paddingLeft)-r.DomUtils.pxToInt(c.paddingRight)<this._renderHelper.getTextWidth(l)&&(a.title=l)}return a},e.prototype.createScaleBorder=function(e,t,n){var r=this.gridLayoutCalculator.getScaleBorderInfo(e,n);return this.createScaleElementCore(e,r,t,this.scaleBorders)},e.prototype.createScaleElementAndBorder=function(e,t,n){var r=this.gridLayoutCalculator.getScaleElementInfo(e,n),i=this.createScaleElement(e,t,n,r),o=this.createScaleBorder(e,t,n);this.onScaleCellPrepared(n,t,i,o,r.additionalInfo.range)},e.prototype.removeScaleElementAndBorder=function(e,t){a.RenderElementUtils.remove(null,e,this.timeScaleAreas[t],this.scaleElements[t]),a.RenderElementUtils.remove(null,e,this.timeScaleAreas[t],this.scaleBorders[t])},e.prototype.recreateScalesElements=function(){this.recreateScaleElements(this.viewType,0),this.recreateScaleElements(o.DateUtils.ViewTypeToScaleMap[this.viewType],1)},e.prototype.recreateScaleElements=function(e,t){var n=this,r=this.gridLayoutCalculator.getRenderedScaleItemIndices(e,this.renderedColIndices),i=this.renderedScaleItemIndices[e-this.viewType]||[];a.RenderElementUtils.recreate(i,r,(function(e){n.removeScaleElementAndBorder(e,t)}),(function(r){n.createScaleElementAndBorder(r,t,e)})),this.renderedScaleItemIndices[e-this.viewType]=r},Object.defineProperty(e.prototype,"dispatcher",{get:function(){return this._renderHelper.dispatcher},enumerable:!1,configurable:!0}),e.prototype.onScaleCellPrepared=function(e,t,n,r,i){var o=new s.ScaleCellPreparedArguments({scaleType:e,scaleIndex:t,range:i,scaleElement:n,separatorElement:r});this.dispatcher.notifyScaleCellPrepared(o)},e}();t.ScaleRender=l},8148:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.StripLinesRender=void 0;var r=n(658),i=function(){function e(e){this._stripLinesMap=[],this._renderedStripLines=[],this._renderHelper=e}return Object.defineProperty(e.prototype,"gridLayoutCalculator",{get:function(){return this._renderHelper.gridLayoutCalculator},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"taskArea",{get:function(){return this._renderHelper.taskArea},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"stripLinesMap",{get:function(){return this._stripLinesMap},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"renderedStripLines",{get:function(){return this._renderedStripLines},set:function(e){this._renderedStripLines=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"stripLines",{get:function(){return this._renderHelper.stripLines},enumerable:!1,configurable:!0}),e.prototype.reset=function(){this._renderedStripLines=[]},e.prototype.recreateStripLines=function(){var e=this,t=this.gridLayoutCalculator.getRenderedStripLines(this.stripLines);r.RenderElementUtils.recreate(this.renderedStripLines,t,(function(t){r.RenderElementUtils.remove(t,null,e.taskArea,e.stripLinesMap)}),(function(t){return r.RenderElementUtils.create(t,null,e.taskArea,e.stripLinesMap)})),this.renderedStripLines=t},e}();t.StripLinesRender=i},2349:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TaskAreaRender=void 0;var r=n(6907),i=n(658),o=function(){function e(e){this._vertTaskAreaBorders=[],this._horTaskAreaBorders=[],this._isExternalTaskAreaContainer=!1,this._renderHelper=e}return Object.defineProperty(e.prototype,"gridLayoutCalculator",{get:function(){return this._renderHelper.gridLayoutCalculator},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"taskArea",{get:function(){return this._renderHelper.taskArea},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"mainElement",{get:function(){return this._renderHelper.mainElement},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"etalonSizeValues",{get:function(){return this._renderHelper.etalonSizeValues},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"scaleCount",{get:function(){return this._renderHelper.scaleCount},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"tickSize",{get:function(){return this._renderHelper.tickSize},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"taskAreaContainerScrollTop",{get:function(){return this._renderHelper.ganttViewTaskAreaContainerScrollTop},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"taskAreaContainerScrollLeft",{get:function(){return this._renderHelper.ganttTaskAreaContainerScrollLeft},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"areHorizontalBordersEnabled",{get:function(){return this._renderHelper.areHorizontalBordersEnabled},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"renderedRowIndices",{get:function(){return this._renderHelper.renderedRowIndices},set:function(e){this._renderHelper.renderedRowIndices=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"renderedColIndices",{get:function(){return this._renderHelper.renderedColIndices},set:function(e){this._renderHelper.renderedColIndices=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"vertTaskAreaBorders",{get:function(){return this._vertTaskAreaBorders},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"horTaskAreaBorders",{get:function(){return this._horTaskAreaBorders},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"hlRowElements",{get:function(){return this._renderHelper.hlRowElements},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"taskAreaContainer",{get:function(){return this._taskAreaContainer},enumerable:!1,configurable:!0}),e.prototype.getExternalTaskAreaContainer=function(e){return this._renderHelper.getExternalTaskAreaContainer(e)},e.prototype.prepareExternalTaskAreaContainer=function(e,t){return this._renderHelper.prepareExternalTaskAreaContainer(e,t)},e.prototype.isAllowTaskAreaBorders=function(e){return this._renderHelper.isAllowTaskAreaBorders(e)},e.prototype.getTaskAreaContainerElement=function(){return this._taskAreaContainer.getElement()},e.prototype.initTaskAreaContainer=function(e){this._renderHelper.createTaskArea(e),this._taskAreaContainer=this.getExternalTaskAreaContainer(e),this._isExternalTaskAreaContainer=!!this._taskAreaContainer,null==this.taskAreaContainer&&(this._taskAreaContainer=this._renderHelper.getTaskAreaContainer(e))},e.prototype.createTaskElement=function(e){this._renderHelper.createTaskElement(e)},e.prototype.removeTaskElement=function(e){this._renderHelper.removeTaskElement(e)},e.prototype.reset=function(){this._horTaskAreaBorders=[],this._vertTaskAreaBorders=[]},e.prototype.prepareTaskAreaContainer=function(){var e="dx-gantt-tac-hb",t=this.getTaskAreaContainerElement();this.areHorizontalBordersEnabled?r.DomUtils.addClassName(t,e):r.DomUtils.removeClassName(t,e);var n=parseInt(getComputedStyle(t).getPropertyValue("margin-top"))||0,i="calc(100% - ".concat(this.etalonSizeValues.scaleItemHeight*this.scaleCount+n,"px)");this._isExternalTaskAreaContainer?this.prepareExternalTaskAreaContainer(t,{height:i}):t.style.height=i},e.prototype.createTaskAreaContainer=function(){var e=document.createElement("DIV");e.className="dx-gantt-tac",this.mainElement.appendChild(e),this.initTaskAreaContainer(e),this.prepareTaskAreaContainer()},e.prototype.createTaskAreaBorder=function(e,t){var n=this.gridLayoutCalculator.getTaskAreaBorderInfo(e,t);i.RenderElementUtils.create(n,e,this.taskArea,this.getTaskAreaBordersDictionary(t))},e.prototype.createTaskArea=function(){var e=document.createElement("DIV");return e.id="dx-gantt-ta",e},e.prototype.removeTaskAreaBorder=function(e,t){i.RenderElementUtils.remove(null,e,this.taskArea,this.getTaskAreaBordersDictionary(t))},e.prototype.createTaskAreaBorderAndTaskElement=function(e,t){this.isAllowTaskAreaBorders(t)&&this.createTaskAreaBorder(e,!t),t&&this.createTaskElement(e)},e.prototype.removeTaskAreaBorderAndTaskElement=function(e,t){this.isAllowTaskAreaBorders(t)&&this.removeTaskAreaBorder(e,!t),t&&this.removeTaskElement(e)},e.prototype.recreateTaskAreaBordersAndTaskElements=function(e){var t=this,n=e?this.taskAreaContainerScrollTop:this.taskAreaContainerScrollLeft,r=this.gridLayoutCalculator.getRenderedRowColumnIndices(n,e),o=e?this.renderedRowIndices:this.renderedColIndices;i.RenderElementUtils.recreate(o,r,(function(n){t.removeTaskAreaBorderAndTaskElement(n,e)}),(function(n){t.createTaskAreaBorderAndTaskElement(n,e)})),e?this.renderedRowIndices=r:this.renderedColIndices=r,this.gridLayoutCalculator.createTileToConnectorLinesMap()},e.prototype.getTaskAreaBordersDictionary=function(e){return e?this.vertTaskAreaBorders:this.horTaskAreaBorders},e.prototype.setSizeForTaskArea=function(e,t){this.taskArea.style.width=e+"px",this.taskArea.style.height=t+"px"},e.prototype.createHighlightRowElement=function(e){var t=this.gridLayoutCalculator.getHighlightRowInfo(e);i.RenderElementUtils.create(t,e,this.taskArea,this.hlRowElements)},e}();t.TaskAreaRender=o},2290:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TaskRender=void 0;var r=n(6907),i=n(2449),o=n(1419),s=n(658),a=function(){function e(e){this._selectionElements=[],this._taskElements=[],this._renderHelper=e,this.customTaskRender=new o.CustomTaskRender(e,this)}return Object.defineProperty(e.prototype,"gridLayoutCalculator",{get:function(){return this._renderHelper.gridLayoutCalculator},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"taskElements",{get:function(){return this._taskElements},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"selectionElements",{get:function(){return this._selectionElements},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"taskArea",{get:function(){return this._renderHelper.taskArea},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isExternalTaskAreaContainer",{get:function(){return this._renderHelper.isExternalTaskAreaContainer},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"resourcesElements",{get:function(){return this._renderHelper.resourcesElements},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"hlRowElements",{get:function(){return this._renderHelper.hlRowElements},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"renderedRowIndices",{get:function(){return this._renderHelper.renderedRowIndices},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"taskTitlePosition",{get:function(){return this._renderHelper.taskTitlePosition},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"showResources",{get:function(){return this._renderHelper.showResources},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"areHorizontalBordersEnabled",{get:function(){return this._renderHelper.areHorizontalBordersEnabled},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"taskTextHeightKey",{get:function(){return this._renderHelper.taskTextHeightKey},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"viewModelItems",{get:function(){return this._renderHelper.viewModelItems},enumerable:!1,configurable:!0}),e.prototype.isHighlightRowElementAllowed=function(e){return this._renderHelper.isHighlightRowElementAllowed(e)},e.prototype.getTaskVisibility=function(e){return this._renderHelper.getTaskVisibility(e)},e.prototype.getTaskText=function(e){return this._renderHelper.getTaskText(e)},Object.defineProperty(e.prototype,"invalidTaskDependencies",{get:function(){return this._renderHelper.invalidTaskDependencies},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"fakeTaskWrapper",{get:function(){var e;return null!==(e=this._fakeTaskWrapper)&&void 0!==e||(this._fakeTaskWrapper=this.createFakeTaskWrapper()),this._fakeTaskWrapper},enumerable:!1,configurable:!0}),e.prototype.getViewItem=function(e){return this._renderHelper.getViewItem(e)},e.prototype.getTask=function(e){return this._renderHelper.getTask(e)},e.prototype.createHighlightRowElement=function(e){this._renderHelper.createHighlightRowElement(e)},e.prototype.getTaskDependencies=function(e){return this._renderHelper.getTaskDependencies(e)},e.prototype.addInvalidTaskDependencies=function(e){this._renderHelper.invalidTaskDependencies=this._renderHelper.invalidTaskDependencies.concat(e)},e.prototype.removeInvalidTaskDependencies=function(e){this._renderHelper.invalidTaskDependencies=this._renderHelper.invalidTaskDependencies.filter((function(t){return t.predecessorId!=e||t.successorId!=e}))},e.prototype.createResources=function(e){this.showResources&&this._renderHelper.createResources(e)},e.prototype.attachEventsOnTask=function(e){this._renderHelper.attachEventsOnTask(e)},e.prototype.detachEventsOnTask=function(e){this._renderHelper.detachEventsOnTask(e)},e.prototype.recreateConnectorLineElement=function(e,t){void 0===t&&(t=!1),this._renderHelper.recreateConnectorLineElement(e,t)},e.prototype.renderTaskElement=function(e){this._renderHelper.createTaskElement(e)},e.prototype.reset=function(){var e=this;this._taskElements.forEach((function(t,n){return e.removeTaskElement(n)})),this._selectionElements=[],this._taskElements=[]},e.prototype.createTaskWrapperElement=function(e){var t=this.gridLayoutCalculator.getTaskWrapperElementInfo(e);s.RenderElementUtils.create(t,e,this.taskArea,this.taskElements),this.taskElements[e].style.display=this.getTaskVisibility(e)?"":"none"},e.prototype.createTaskElement=function(e,t){var n=this.getViewItem(e);if(t&&this.customTaskRender.createCustomTaskElement(e,t),!n.task.isValid()||!n.visible){var r=this.getTaskDependencies(n.task.internalId);return this.addInvalidTaskDependencies(r),void(n.selected&&this.createTaskSelectionElement(e))}n.isCustom||this.createDefaultTaskElement(e)},e.prototype.createTaskVisualElement=function(e){var t=this.gridLayoutCalculator.getTaskElementInfo(e,this.taskTitlePosition!==i.TaskTitlePosition.Inside),n=s.RenderElementUtils.create(t,e,this.taskElements[e]);return this.attachEventsOnTask(e),n},e.prototype.createDefaultTaskElement=function(e){var t=this.getViewItem(e);this.isHighlightRowElementAllowed(e)&&this.createHighlightRowElement(e),t.selected&&this.createTaskSelectionElement(e),this.createTaskWrapperElement(e),this.taskTitlePosition===i.TaskTitlePosition.Outside&&this.createTaskTextElement(e,this.taskElements[e]);var n=this.createTaskVisualElement(e);t.task.isMilestone()||(this.taskTitlePosition===i.TaskTitlePosition.Inside&&this.createTaskTextElement(e,n),this.createTaskProgressElement(e,n)),this.createResources(e)},e.prototype.removeTaskElement=function(e){var t=this.getTask(e);if(t&&this.removeInvalidTaskDependencies(t.id),this.detachEventsOnTask(e),this._renderHelper.hasTaskTemplate()){var n=this.taskElements[e],r=null==n?void 0:n.firstElementChild;r&&(this._renderHelper.destroyTemplate(r),n.removeChild(r))}s.RenderElementUtils.remove(null,e,this.taskArea,this.taskElements),s.RenderElementUtils.remove(null,e,this.taskArea,this.resourcesElements),s.RenderElementUtils.remove(null,e,this.taskArea,this.selectionElements),this.isHighlightRowElementAllowed(e)&&s.RenderElementUtils.remove(null,e,this.taskArea,this.hlRowElements),this.gridLayoutCalculator.resetTaskInfo(e)},e.prototype.recreateTaskElement=function(e){var t=this,n=this.renderedRowIndices.filter((function(t){return t===e})).length>0,r=this.getTask(e);if(r){n&&(this.removeTaskElement(e),this.renderTaskElement(e));var i=this.getTaskDependencies(r.internalId);i.length&&i.forEach((function(e){return t.recreateConnectorLineElement(e.internalId,!0)}))}},e.prototype.createFakeTaskWrapper=function(){var e,t,n=null!==(t=null===(e=this.viewModelItems.filter((function(e){return e.task&&!e.task.isMilestone}))[0])||void 0===e?void 0:e.visibleIndex)&&void 0!==t?t:0,r=this.gridLayoutCalculator,i=s.RenderElementUtils.create(r.getTaskWrapperElementInfo(n),null,this.taskArea),o=s.RenderElementUtils.create(r.getTaskElementInfo(n),null,i);return this.createTaskTextElement(n,o),this.createTaskProgressElement(n,o),i.style.display="none",i},e.prototype.createTaskProgressElement=function(e,t){var n=this.gridLayoutCalculator.getTaskProgressElementInfo(e);s.RenderElementUtils.create(n,e,t)},e.prototype.getTextWidth=function(e){return this._renderHelper.getTextWidth(e)},Object.defineProperty(e.prototype,"minTextWidth",{get:function(){var e;return null!==(e=this._minTextWidth)&&void 0!==e||(this._minTextWidth=5*this.getTextWidth("a")),this._minTextWidth},enumerable:!1,configurable:!0}),e.prototype.createTaskTextElement=function(t,n){var o,a,l=this.gridLayoutCalculator.getTaskTextElementInfo(t,this.taskTitlePosition===i.TaskTitlePosition.Inside);if(!l.additionalInfo.hidden){var c=s.RenderElementUtils.create(l,t,n),u=this.getTaskText(t);if(this.taskTitlePosition===i.TaskTitlePosition.Outside&&l.size.width>0){var d=getComputedStyle(c),p=l.size.width-r.DomUtils.pxToInt(d.paddingLeft);if(p>=this.minTextWidth){var h=r.DomUtils.pxToInt(d.paddingRight),f=u?this.getTextWidth(u):0;h&&f>p-h&&(h=Math.min(e.minTitleOutRightPadding,p-this.minTextWidth),c.style.paddingRight=h+"px"),f>p-h&&(c.style.overflowX="hidden",c.style.textOverflow="ellipsis")}else c.style.display="none"}u||(null!==(o=this[a=this.taskTextHeightKey])&&void 0!==o||(this[a]=this.getTaskTextHeight(c)),c.style.height=this[this.taskTextHeightKey]),c.innerText=u}},e.prototype.createTaskSelectionElement=function(e){var t=this.gridLayoutCalculator.getSelectionElementInfo(e);this.isExternalTaskAreaContainer&&!this.areHorizontalBordersEnabled&&t.size.height++,s.RenderElementUtils.create(t,e,this.taskArea,this.selectionElements)},e.prototype.getTaskTextHeight=function(e){e.innerText="WWW";var t=getComputedStyle(e).height;return e.innerText="",t},e.prototype.getSmallTaskWidth=function(e){var t=0;if(null!=e&&""!==e){var n=e.indexOf("rem");if(n>-1)try{t=parseFloat(e.substr(0,n))*parseFloat(getComputedStyle(document.documentElement).fontSize)}catch(e){}else t=r.DomUtils.pxToInt(e)}return 2*t},e.minTitleOutRightPadding=5,e}();t.TaskRender=a},2990:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.EditingSettings=void 0;var r=n(2491),i=function(){function e(){this.enabled=!1,this.allowDependencyDelete=!0,this.allowDependencyInsert=!0,this.allowTaskDelete=!0,this.allowTaskInsert=!0,this.allowTaskUpdate=!0,this.allowResourceDelete=!0,this.allowResourceInsert=!0,this.allowResourceUpdate=!0,this.allowTaskResourceUpdate=!0,this.taskHoverDelay=0}return e.parse=function(t){var n=new e;return t&&((0,r.isDefined)(t.enabled)&&(n.enabled=t.enabled),(0,r.isDefined)(t.allowDependencyDelete)&&(n.allowDependencyDelete=t.allowDependencyDelete),(0,r.isDefined)(t.allowDependencyInsert)&&(n.allowDependencyInsert=t.allowDependencyInsert),(0,r.isDefined)(t.allowTaskDelete)&&(n.allowTaskDelete=t.allowTaskDelete),(0,r.isDefined)(t.allowTaskInsert)&&(n.allowTaskInsert=t.allowTaskInsert),(0,r.isDefined)(t.allowTaskUpdate)&&(n.allowTaskUpdate=t.allowTaskUpdate),(0,r.isDefined)(t.allowResourceDelete)&&(n.allowResourceDelete=t.allowResourceDelete),(0,r.isDefined)(t.allowResourceInsert)&&(n.allowResourceInsert=t.allowResourceInsert),(0,r.isDefined)(t.allowResourceUpdate)&&(n.allowResourceUpdate=t.allowResourceUpdate),(0,r.isDefined)(t.allowTaskResourceUpdate)&&(n.allowTaskResourceUpdate=t.allowTaskResourceUpdate),(0,r.isDefined)(t.taskHoverDelay)&&(n.taskHoverDelay=t.taskHoverDelay)),n},e.prototype.equal=function(e){var t=!0;return t=(t=(t=(t=(t=(t=(t=(t=(t=(t=t&&this.enabled===e.enabled)&&this.allowDependencyDelete===e.allowDependencyDelete)&&this.allowDependencyInsert===e.allowDependencyInsert)&&this.allowTaskDelete===e.allowTaskDelete)&&this.allowTaskInsert===e.allowTaskInsert)&&this.allowTaskUpdate===e.allowTaskUpdate)&&this.allowResourceDelete===e.allowResourceDelete)&&this.allowResourceInsert===e.allowResourceInsert)&&this.allowResourceUpdate===e.allowResourceUpdate)&&this.allowTaskResourceUpdate===e.allowTaskResourceUpdate},e}();t.EditingSettings=i},9954:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.FullScreenHelperSettings=void 0;var r=n(2491),i=function(){function e(){}return e.parse=function(t){var n=new e;return t&&((0,r.isDefined)(t.getMainElement)&&(n.getMainElement=t.getMainElement),(0,r.isDefined)(t.adjustControl)&&(n.adjustControl=t.adjustControl)),n},e}();t.FullScreenHelperSettings=i},9640:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TaskEditSettings=void 0;var r=n(655),i=n(2491),o=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r.__extends(t,e),t.parse=function(e){var n=new t;return e&&((0,i.isDefined)(e.getCommandManager)&&(n.getCommandManager=e.getCommandManager),(0,i.isDefined)(e.getViewModel)&&(n.getViewModel=e.getViewModel),(0,i.isDefined)(e.getGanttSettings)&&(n.getGanttSettings=e.getGanttSettings),(0,i.isDefined)(e.getRenderHelper)&&(n.getRenderHelper=e.getRenderHelper),(0,i.isDefined)(e.destroyTemplate)&&(n.destroyTemplate=e.destroyTemplate),(0,i.isDefined)(e.formatDate)&&(n.formatDate=e.formatDate),(0,i.isDefined)(e.getModelManipulator)&&(n.getModelManipulator=e.getModelManipulator),(0,i.isDefined)(e.getValidationController)&&(n.getValidationController=e.getValidationController)),n},t}(n(9080).TooltipSettings);t.TaskEditSettings=o},9080:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TooltipSettings=void 0;var r=n(2491),i=function(){function e(){}return e.parse=function(t){var n=new e;return t&&((0,r.isDefined)(t.getHeaderHeight)&&(n.getHeaderHeight=t.getHeaderHeight),(0,r.isDefined)(t.getTaskTooltipContentTemplate)&&(n.getTaskTooltipContentTemplate=t.getTaskTooltipContentTemplate),(0,r.isDefined)(t.getTaskProgressTooltipContentTemplate)&&(n.getTaskProgressTooltipContentTemplate=t.getTaskProgressTooltipContentTemplate),(0,r.isDefined)(t.getTaskTimeTooltipContentTemplate)&&(n.getTaskTimeTooltipContentTemplate=t.getTaskTimeTooltipContentTemplate),(0,r.isDefined)(t.destroyTemplate)&&(n.destroyTemplate=t.destroyTemplate),(0,r.isDefined)(t.formatDate)&&(n.formatDate=t.formatDate),(0,r.isDefined)(t.getTaskAreaContainer)&&(n.getTaskAreaContainer=t.getTaskAreaContainer)),n},e}();t.TooltipSettings=i},5846:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ValidationControllerSettings=void 0;var r=n(2491),i=function(){function e(){}return e.parse=function(t){var n=new e;return t&&((0,r.isDefined)(t.getViewModel)&&(n.getViewModel=t.getViewModel),(0,r.isDefined)(t.getHistory)&&(n.getHistory=t.getHistory),(0,r.isDefined)(t.getModelManipulator)&&(n.getModelManipulator=t.getModelManipulator),(0,r.isDefined)(t.getRange)&&(n.getRange=t.getRange),(0,r.isDefined)(t.getValidationSettings)&&(n.getValidationSettings=t.getValidationSettings),(0,r.isDefined)(t.updateOwnerInAutoParentMode)&&(n.updateOwnerInAutoParentMode=t.updateOwnerInAutoParentMode),(0,r.isDefined)(t.getIsValidateDependenciesRequired)&&(n.getIsValidateDependenciesRequired=t.getIsValidateDependenciesRequired)),n},e}();t.ValidationControllerSettings=i},5351:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Settings=void 0;var r=n(2491),i=n(7880),o=n(2449),s=n(2990),a=n(9057),l=n(2762),c=n(9820),u=function(){function e(){this.viewType=void 0,this.taskTitlePosition=o.TaskTitlePosition.Inside,this.showResources=!0,this.showDependencies=!0,this.areHorizontalBordersEnabled=!0,this.areVerticalBordersEnabled=!0,this.areAlternateRowsEnabled=!0,this.allowSelectTask=!0,this.firstDayOfWeek=0,this.editing=new s.EditingSettings,this.validation=new l.ValidationSettings,this.stripLines=new a.StripLineSettings,this.viewTypeRange=new c.ViewTypeRangeSettings}return e.parse=function(t){var n=new e;return t&&((0,r.isDefined)(t.viewType)&&(n.viewType=t.viewType),(0,r.isDefined)(t.taskTitlePosition)&&(n.taskTitlePosition=t.taskTitlePosition),(0,r.isDefined)(t.showResources)&&(n.showResources=t.showResources),(0,r.isDefined)(t.showDependencies)&&(n.showDependencies=t.showDependencies),(0,r.isDefined)(t.areHorizontalBordersEnabled)&&(n.areHorizontalBordersEnabled=t.areHorizontalBordersEnabled),(0,r.isDefined)(t.areVerticalBordersEnabled)&&(n.areHorizontalBordersEnabled=t.areHorizontalBordersEnabled),(0,r.isDefined)(t.areAlternateRowsEnabled)&&(n.areAlternateRowsEnabled=t.areAlternateRowsEnabled),(0,r.isDefined)(t.allowSelectTask)&&(n.allowSelectTask=t.allowSelectTask),(0,r.isDefined)(t.firstDayOfWeek)&&(n.firstDayOfWeek=t.firstDayOfWeek),(0,r.isDefined)(t.startDateRange)&&(n.startDateRange=new Date(t.startDateRange)),(0,r.isDefined)(t.endDateRange)&&(n.endDateRange=new Date(t.endDateRange)),(0,r.isDefined)(t.editing)&&(n.editing=s.EditingSettings.parse(t.editing)),(0,r.isDefined)(t.validation)&&(n.validation=l.ValidationSettings.parse(t.validation)),(0,r.isDefined)(t.stripLines)&&(n.stripLines=a.StripLineSettings.parse(t.stripLines)),(0,r.isDefined)(t.viewTypeRange)&&(n.viewTypeRange=c.ViewTypeRangeSettings.parse(t.viewTypeRange)),(0,r.isDefined)(t.taskTooltipContentTemplate)&&(n.taskTooltipContentTemplate=t.taskTooltipContentTemplate),(0,r.isDefined)(t.taskProgressTooltipContentTemplate)&&(n.taskProgressTooltipContentTemplate=t.taskProgressTooltipContentTemplate),(0,r.isDefined)(t.taskTimeTooltipContentTemplate)&&(n.taskTimeTooltipContentTemplate=t.taskTimeTooltipContentTemplate),(0,r.isDefined)(t.taskContentTemplate)&&(n.taskContentTemplate=t.taskContentTemplate),(0,r.isDefined)(t.cultureInfo)&&(n.cultureInfo=t.cultureInfo)),n},e.prototype.equal=function(e){var t=!0;return t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=t&&this.viewType===e.viewType)&&this.taskTitlePosition===e.taskTitlePosition)&&this.showResources===e.showResources)&&this.showDependencies===e.showDependencies)&&this.areHorizontalBordersEnabled===e.areHorizontalBordersEnabled)&&this.areAlternateRowsEnabled===e.areAlternateRowsEnabled)&&this.allowSelectTask===e.allowSelectTask)&&this.editing.equal(e.editing))&&this.validation.equal(e.validation))&&this.stripLines.equal(e.stripLines))&&i.DateTimeUtils.areDatesEqual(this.startDateRange,e.startDateRange))&&i.DateTimeUtils.areDatesEqual(this.endDateRange,e.endDateRange)},e}();t.Settings=u},1442:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.StripLine=void 0;var r=n(2491),i=function(){function e(e,t,n,r,i){this.isCurrent=!1,this.start=e,this.end=t,this.title=n,this.cssClass=r,this.isCurrent=i}return e.parse=function(t){var n=new e;return t&&((0,r.isDefined)(t.start)&&(n.start=t.start),(0,r.isDefined)(t.end)&&(n.end=t.end),(0,r.isDefined)(t.title)&&(n.title=t.title),(0,r.isDefined)(t.cssClass)&&(n.cssClass=t.cssClass)),n},e.prototype.clone=function(){return new e(this.start,this.end,this.title,this.cssClass,this.isCurrent)},e.prototype.equal=function(e){var t=!0;return t=(t=(t=(t=t&&this.start==e.start)&&this.end==e.end)&&this.title==e.title)&&this.cssClass==e.cssClass},e}();t.StripLine=i},9057:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.StripLineSettings=void 0;var r=n(2491),i=n(1442),o=function(){function e(){this.showCurrentTime=!1,this.currentTimeUpdateInterval=6e4,this.stripLines=[]}return e.parse=function(t){var n=new e;if(t&&((0,r.isDefined)(t.showCurrentTime)&&(n.showCurrentTime=t.showCurrentTime),(0,r.isDefined)(t.currentTimeUpdateInterval)&&(n.currentTimeUpdateInterval=t.currentTimeUpdateInterval),(0,r.isDefined)(t.currentTimeTitle)&&(n.currentTimeTitle=t.currentTimeTitle),(0,r.isDefined)(t.currentTimeCssClass)&&(n.currentTimeCssClass=t.currentTimeCssClass),(0,r.isDefined)(t.stripLines)))for(var o=0;o<t.stripLines.length;o++)n.stripLines.push(i.StripLine.parse(t.stripLines[o]));return n},e.prototype.equal=function(e){var t=!0;if(t=(t=(t=(t=(t=t&&this.showCurrentTime==e.showCurrentTime)&&this.currentTimeUpdateInterval==e.currentTimeUpdateInterval)&&this.currentTimeTitle==e.currentTimeTitle)&&this.currentTimeCssClass==e.currentTimeCssClass)&&this.stripLines.length===e.stripLines.length)for(var n=0;n<e.stripLines.length;n++)t=t&&this.stripLines[n].equal(e.stripLines[n]);return t},e}();t.StripLineSettings=o},2762:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ValidationSettings=void 0;var r=n(2491),i=function(){function e(){this.validateDependencies=!1,this.autoUpdateParentTasks=!1,this.enablePredecessorGap=!1}return e.parse=function(t){var n=new e;return t&&((0,r.isDefined)(t.validateDependencies)&&(n.validateDependencies=t.validateDependencies),(0,r.isDefined)(t.autoUpdateParentTasks)&&(n.autoUpdateParentTasks=t.autoUpdateParentTasks),(0,r.isDefined)(t.enablePredecessorGap)&&(n.enablePredecessorGap=t.enablePredecessorGap)),n},e.prototype.equal=function(e){var t=!0;return t=(t=(t=t&&this.validateDependencies===e.validateDependencies)&&this.autoUpdateParentTasks===e.autoUpdateParentTasks)&&this.enablePredecessorGap===e.enablePredecessorGap},e}();t.ValidationSettings=i},9820:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ViewTypeRangeSettings=void 0;var r=n(2491),i=n(2449),o=function(){function e(){this.min=i.ViewType.TenMinutes,this.max=i.ViewType.Years}return e.parse=function(t){var n=new e;return t&&((0,r.isDefined)(t.min)&&(n.min=t.min),(0,r.isDefined)(t.max)&&(n.max=t.max)),n},e.prototype.equal=function(e){var t=!0;return t=(t=t&&this.min===e.min)&&this.max===e.max},e}();t.ViewTypeRangeSettings=o},9201:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DateUtils=void 0;var r=n(2449),i=function(){function e(){}var t;return e.getDaysInQuarter=function(t){var n=3*Math.floor(t.getMonth()/3);return[n,n+1,n+2].reduce((function(n,r){return n+e.getDaysInMonth(r,t.getFullYear())}),0)},e.getDaysInMonth=function(e,t){return new Date(t,e+1,0).getDate()},e.getOffsetInMonths=function(e,t){return 12*(t.getFullYear()-e.getFullYear())+t.getMonth()-e.getMonth()},e.getOffsetInQuarters=function(e,t){return 4*(t.getFullYear()-e.getFullYear())+Math.floor(t.getMonth()/3)-Math.floor(e.getMonth()/3)},e.getNearestScaleTickDate=function(e,t,n,r){var i=new Date,o=t.start.getTime(),s=t.end.getTime();if(i.setTime(e.getTime()),e.getTime()<o)i.setTime(o);else if(e.getTime()>s)i.setTime(s);else if(this.needCorrectDate(e,o,n,r)){var a=this.getNearestLeftTickTime(e,o,n,r),l=this.getNextTickTime(a,n,r);Math.abs(e.getTime()-a)>Math.abs(e.getTime()-l)?i.setTime(l):i.setTime(a)}return i},e.needCorrectDate=function(e,t,n,i){return i==r.ViewType.Months?e.getTime()!==new Date(e.getFullYear(),e.getMonth(),1).getTime():(e.getTime()-t)%n!=0},e.getNearestLeftTickTime=function(e,t,n,i){return i==r.ViewType.Months?new Date(e.getFullYear(),e.getMonth(),1).getTime():t+Math.floor((e.getTime()-t)/n)*n},e.getNextTickTime=function(e,t,n){if(n==r.ViewType.Months){var i=new Date;return i.setTime(e),i.setMonth(i.getMonth()+1),i.getTime()}return e+t},e.adjustStartDateByViewType=function(e,t,n){switch(void 0===n&&(n=0),t){case r.ViewType.TenMinutes:return new Date(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours());case r.ViewType.SixHours:case r.ViewType.Hours:return new Date(e.getFullYear(),e.getMonth(),e.getDate());case r.ViewType.Days:case r.ViewType.Weeks:return new Date(e.getFullYear(),e.getMonth(),e.getDate()-e.getDay()+n);case r.ViewType.Months:case r.ViewType.Quarter:case r.ViewType.Years:return new Date(e.getFullYear(),0,1);default:return new Date}},e.adjustEndDateByViewType=function(e,t,n){switch(void 0===n&&(n=0),t){case r.ViewType.TenMinutes:return new Date(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours()+1);case r.ViewType.SixHours:case r.ViewType.Hours:return new Date(e.getFullYear(),e.getMonth(),e.getDate()+1);case r.ViewType.Days:case r.ViewType.Weeks:return new Date(e.getFullYear(),e.getMonth(),e.getDate()+7-e.getDay()+n);case r.ViewType.Months:case r.ViewType.Quarter:case r.ViewType.Years:return new Date(e.getFullYear()+1,0,1);default:return new Date}},e.roundStartDate=function(e,t){switch(t){case r.ViewType.TenMinutes:case r.ViewType.Hours:return new Date(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours()-1);case r.ViewType.SixHours:case r.ViewType.Days:return new Date(e.getFullYear(),e.getMonth(),e.getDate()-1);case r.ViewType.Weeks:return new Date(e.getFullYear(),e.getMonth(),e.getDate()-e.getDay());case r.ViewType.Months:return new Date(e.getFullYear(),e.getMonth()-1);case r.ViewType.Quarter:case r.ViewType.Years:return new Date(e.getFullYear()-1,0,1);default:return new Date}},e.getTickTimeSpan=function(t){switch(t){case r.ViewType.TenMinutes:return e.msPerHour/6;case r.ViewType.Hours:return e.msPerHour;case r.ViewType.SixHours:return 6*e.msPerHour;case r.ViewType.Days:return e.msPerDay;case r.ViewType.Weeks:return e.msPerWeek;case r.ViewType.Months:return e.msPerMonth;case r.ViewType.Quarter:return 3*e.msPerMonth;case r.ViewType.Years:return e.msPerYear}},e.getRangeTickCount=function(t,n,i){return i===r.ViewType.Months?this.getRangeTickCountInMonthsViewType(t,n):i===r.ViewType.Quarter?this.getRangeTickCountInQuarterViewType(t,n):e.getRangeMSPeriod(t,n)/e.getTickTimeSpan(i)},e.getRangeMSPeriod=function(t,n){return n.getTime()-e.getDSTTotalDelta(t,n)-t.getTime()},e.getRangeTickCountInMonthsViewType=function(t,n){var r=new Date(t.getFullYear(),t.getMonth(),1),i=new Date(n.getFullYear(),n.getMonth(),1);return e.getOffsetInMonths(r,i)+(n.getTime()-i.getTime())/(e.getDaysInMonth(n.getMonth(),n.getFullYear())*e.msPerDay)-(t.getTime()-r.getTime())/(e.getDaysInMonth(t.getMonth(),t.getFullYear())*e.msPerDay)},e.getRangeTickCountInQuarterViewType=function(t,n){var r=new Date(t.getFullYear(),3*Math.floor(t.getMonth()/3),1),i=new Date(n.getFullYear(),3*Math.floor(n.getMonth()/3),1);return e.getOffsetInQuarters(r,i)+(n.getTime()-i.getTime())/(e.getDaysInQuarter(i)*e.msPerDay)-(t.getTime()-r.getTime())/(e.getDaysInQuarter(r)*e.msPerDay)},e.parse=function(e){return"function"==typeof e?new Date(e()):new Date(e)},e.getOrCreateUTCDate=function(e){var t=e.getTimezoneOffset();return t?new Date(e.valueOf()+6e4*t):e},e.getTimezoneOffsetDiff=function(e,t){return t.getTimezoneOffset()-e.getTimezoneOffset()},e.getDSTDelta=function(t,n){var r=e.getTimezoneOffsetDiff(t,n)*e.msPerMinute;return r>0?r:0},e.getDSTTotalDelta=function(t,n){if(!e.hasDST())return 0;for(var r=t,i=0,o=r.getFullYear(),s=r.getMonth();r<n;){s>=5?(o++,s=0):s=5;var a=new Date(o,s,1);a>n&&(a=n),i+=e.getDSTDelta(r,a),r=a}return i},e.getDSTCorrectedTaskEnd=function(t,n){var r=t.getTime()+n,i=e.getDSTTotalDelta(t,new Date(r));return new Date(r+i)},e.hasDST=function(){var t=(new Date).getFullYear(),n=new Date(t,0,1),r=new Date(t,5,1);return 0!==e.getTimezoneOffsetDiff(n,r)},e.msPerMinute=6e4,e.msPerHour=36e5,e.msPerDay=24*e.msPerHour,e.msPerWeek=7*e.msPerDay,e.msPerMonth=30*e.msPerDay,e.msPerYear=365*e.msPerDay,e.ViewTypeToScaleMap=((t={})[r.ViewType.TenMinutes]=r.ViewType.Hours,t[r.ViewType.Hours]=r.ViewType.Days,t[r.ViewType.SixHours]=r.ViewType.Days,t[r.ViewType.Days]=r.ViewType.Weeks,t[r.ViewType.Weeks]=r.ViewType.Months,t[r.ViewType.Months]=r.ViewType.Years,t[r.ViewType.Quarter]=r.ViewType.Years,t[r.ViewType.Years]=r.ViewType.FiveYears,t),e}();t.DateUtils=i},8380:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ElementTextHelper=void 0;var r=n(6907),i=n(2449),o=n(9201),s=function(){function e(e){this.longestAbbrMonthName=null,this.longestMonthName=null,this.longestAbbrDayName=null;var t=document.createElement("canvas");this.textMeasureContext=t.getContext("2d"),this.cultureInfo=e}return e.prototype.setFont=function(e){var t=r.DomUtils.getCurrentStyle(e),n=t.font?t.font:t.fontStyle+" "+t.fontVariant+" "+t.fontWeight+" "+t.fontSize+" / "+t.lineHeight+" "+t.fontFamily;this.textMeasureContext.font=n},e.prototype.setSettings=function(e,t,n){this.startTime=e,this.viewType=t,this.modelItems=n},e.prototype.getScaleItemText=function(e,t){var n=this.viewType.valueOf()===t.valueOf();switch(t){case i.ViewType.TenMinutes:return this.getTenMinutesScaleItemText(e);case i.ViewType.Hours:case i.ViewType.SixHours:return this.getHoursScaleItemText(e);case i.ViewType.Days:return this.getDaysScaleItemText(e,n);case i.ViewType.Weeks:return this.getWeeksScaleItemText(e,n);case i.ViewType.Months:return this.getMonthsScaleItemText(e,n);case i.ViewType.Quarter:return this.getQuarterScaleItemText(e,n);case i.ViewType.Years:return this.getYearsScaleItemText(e);case i.ViewType.FiveYears:return this.getFiveYearsScaleItemText(e)}},e.prototype.getTenMinutesScaleItemText=function(e){var t=e.getMinutes()+1;return(10*Math.ceil(t/10)).toString()},e.prototype.getThirtyMinutesScaleItemText=function(e){return e.getMinutes()<30?"30":"60"},e.prototype.getHoursScaleItemText=function(e){var t=e.getHours(),n=this.getHourDisplayText(t),r=t<12?this.getAmText():this.getPmText();return this.getHoursScaleItemTextCore(n,r)},e.prototype.getDaysScaleItemText=function(e,t){return this.getDayTotalText(e,!0,t,t,!t)},e.prototype.getWeeksScaleItemText=function(e,t){var n=o.DateUtils.getDSTCorrectedTaskEnd(e,o.DateUtils.msPerWeek-o.DateUtils.msPerDay);return this.getWeeksScaleItemTextCore(this.getDayTotalText(e,t,!0,t,!t),this.getDayTotalText(n,t,!0,t,!t))},e.prototype.getMonthsScaleItemText=function(e,t){var n=this.getMonthNames(),r=t?"":e.getFullYear().toString();return this.getMonthsScaleItemTextCore(n[e.getMonth()],r)},e.prototype.getQuarterScaleItemText=function(e,t){var n=this.getQuarterNames(),r=t?"":e.getFullYear().toString();return this.getMonthsScaleItemTextCore(n[Math.floor(e.getMonth()/3)],r)},e.prototype.getYearsScaleItemText=function(e){return e.getFullYear().toString()},e.prototype.getFiveYearsScaleItemText=function(e){return e.getFullYear().toString()+" - "+(e.getFullYear()+4).toString()},e.prototype.getHourDisplayText=function(e){return this.hasAmPm()?(0==e?12:e<=12?e:e-12).toString():e<10?"0"+e:e.toString()},e.prototype.getDayTotalText=function(e,t,n,r,i){var o=r?this.getAbbrMonthNames():this.getMonthNames(),s=n?this.getAbbrDayNames():this.getDayNames(),a=t?s[e.getDay()]:"",l=e.getDate(),c=o[e.getMonth()],u=i?e.getFullYear().toString():"";return this.getDayTotalTextCore(a,l.toString(),c,u)},e.prototype.getTaskText=function(e){var t=this.modelItems[e];return t?t.task.title:""},e.prototype.getTaskVisibility=function(e){var t=this.modelItems[e];return!!t&&t.getVisible()},e.prototype.hasAmPm=function(){return this.getAmText().length>0||this.getPmText().length>0},e.prototype.getScaleItemTextTemplate=function(e){switch(e){case i.ViewType.TenMinutes:return"00";case i.ViewType.Hours:case i.ViewType.SixHours:return this.getHoursScaleItemTextCore("00",this.getAmText());case i.ViewType.Days:return this.getDayTextTemplate();case i.ViewType.Weeks:return this.getWeekTextTemplate();case i.ViewType.Months:return this.getMonthsScaleItemTextCore(this.getLongestMonthName(),"");case i.ViewType.Quarter:return"Q4";case i.ViewType.Years:return"0000"}},e.prototype.getDayTextTemplate=function(){return this.getDayTotalTextCore(this.getLongestAbbrDayName(),"00",this.getLongestAbbrMonthName(),"")},e.prototype.getWeekTextTemplate=function(){var e=this.getDayTextTemplate();return this.getWeeksScaleItemTextCore(e,e)},e.prototype.getHoursScaleItemTextCore=function(e,t){return e+":00"+(this.hasAmPm()?" "+t:"")},e.prototype.getDayTotalTextCore=function(e,t,n,r){var i=e.length>0?e+", ":"";return i+=t+" "+n,i+=r.length>0?" "+r:""},e.prototype.getWeeksScaleItemTextCore=function(e,t){return e+" - "+t},e.prototype.getMonthsScaleItemTextCore=function(e,t){var n=e;return t.length>0&&(n+=" "+t),n},e.prototype.getLongestAbbrMonthName=function(){return null==this.longestAbbrMonthName&&(this.longestAbbrMonthName=this.getLongestText(this.getAbbrMonthNames())),this.longestAbbrMonthName},e.prototype.getLongestMonthName=function(){return null==this.longestMonthName&&(this.longestMonthName=this.getLongestText(this.getMonthNames())),this.longestMonthName},e.prototype.getLongestAbbrDayName=function(){return null==this.longestAbbrDayName&&(this.longestAbbrDayName=this.getLongestText(this.getAbbrDayNames())),this.longestAbbrDayName},e.prototype.getLongestText=function(e){var t=this,n="",r=0;return e.forEach((function(e){var i=t.getTextWidth(e);i>r&&(r=i,n=e)})),n},e.prototype.getTextWidth=function(e){return Math.round(this.textMeasureContext.measureText(e).width)},e.prototype.getAmText=function(){return this.cultureInfo.amText},e.prototype.getPmText=function(){return this.cultureInfo.pmText},e.prototype.getQuarterNames=function(){return this.cultureInfo.quarterNames},e.prototype.getMonthNames=function(){return this.cultureInfo.monthNames},e.prototype.getDayNames=function(){return this.cultureInfo.dayNames},e.prototype.getAbbrMonthNames=function(){return this.cultureInfo.abbrMonthNames},e.prototype.getAbbrDayNames=function(){return this.cultureInfo.abbrDayNames},e}();t.ElementTextHelper=s},655:(e,t,n)=>{n.r(t),n.d(t,{__assign:()=>o,__asyncDelegator:()=>k,__asyncGenerator:()=>b,__asyncValues:()=>S,__await:()=>T,__awaiter:()=>u,__classPrivateFieldGet:()=>I,__classPrivateFieldSet:()=>C,__createBinding:()=>p,__decorate:()=>a,__exportStar:()=>h,__extends:()=>i,__generator:()=>d,__importDefault:()=>D,__importStar:()=>E,__makeTemplateObject:()=>_,__metadata:()=>c,__param:()=>l,__read:()=>g,__rest:()=>s,__spread:()=>y,__spreadArray:()=>v,__spreadArrays:()=>m,__values:()=>f});var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},r(e,t)};function i(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}var o=function(){return o=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},o.apply(this,arguments)};function s(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var i=0;for(r=Object.getOwnPropertySymbols(e);i<r.length;i++)t.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(e,r[i])&&(n[r[i]]=e[r[i]])}return n}function a(e,t,n,r){var i,o=arguments.length,s=o<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(e,t,n,r);else for(var a=e.length-1;a>=0;a--)(i=e[a])&&(s=(o<3?i(s):o>3?i(t,n,s):i(t,n))||s);return o>3&&s&&Object.defineProperty(t,n,s),s}function l(e,t){return function(n,r){t(n,r,e)}}function c(e,t){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(e,t)}function u(e,t,n,r){return new(n||(n=Promise))((function(i,o){function s(e){try{l(r.next(e))}catch(e){o(e)}}function a(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(s,a)}l((r=r.apply(e,t||[])).next())}))}function d(e,t){var n,r,i,o,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(o){return function(a){return function(o){if(n)throw new TypeError("Generator is already executing.");for(;s;)try{if(n=1,r&&(i=2&o[0]?r.return:o[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,o[1])).done)return i;switch(r=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return s.label++,{value:o[1],done:!1};case 5:s.label++,r=o[1],o=[0];continue;case 7:o=s.ops.pop(),s.trys.pop();continue;default:if(!(i=s.trys,(i=i.length>0&&i[i.length-1])||6!==o[0]&&2!==o[0])){s=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){s.label=o[1];break}if(6===o[0]&&s.label<i[1]){s.label=i[1],i=o;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(o);break}i[2]&&s.ops.pop(),s.trys.pop();continue}o=t.call(e,s)}catch(e){o=[6,e],r=0}finally{n=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,a])}}}var p=Object.create?function(e,t,n,r){void 0===r&&(r=n),Object.defineProperty(e,r,{enumerable:!0,get:function(){return t[n]}})}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]};function h(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||p(t,e,n)}function f(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function g(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,i,o=n.call(e),s=[];try{for(;(void 0===t||t-- >0)&&!(r=o.next()).done;)s.push(r.value)}catch(e){i={error:e}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(i)throw i.error}}return s}function y(){for(var e=[],t=0;t<arguments.length;t++)e=e.concat(g(arguments[t]));return e}function m(){for(var e=0,t=0,n=arguments.length;t<n;t++)e+=arguments[t].length;var r=Array(e),i=0;for(t=0;t<n;t++)for(var o=arguments[t],s=0,a=o.length;s<a;s++,i++)r[i]=o[s];return r}function v(e,t,n){if(n||2===arguments.length)for(var r,i=0,o=t.length;i<o;i++)!r&&i in t||(r||(r=Array.prototype.slice.call(t,0,i)),r[i]=t[i]);return e.concat(r||Array.prototype.slice.call(t))}function T(e){return this instanceof T?(this.v=e,this):new T(e)}function b(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r,i=n.apply(e,t||[]),o=[];return r={},s("next"),s("throw"),s("return"),r[Symbol.asyncIterator]=function(){return this},r;function s(e){i[e]&&(r[e]=function(t){return new Promise((function(n,r){o.push([e,t,n,r])>1||a(e,t)}))})}function a(e,t){try{(n=i[e](t)).value instanceof T?Promise.resolve(n.value.v).then(l,c):u(o[0][2],n)}catch(e){u(o[0][3],e)}var n}function l(e){a("next",e)}function c(e){a("throw",e)}function u(e,t){e(t),o.shift(),o.length&&a(o[0][0],o[0][1])}}function k(e){var t,n;return t={},r("next"),r("throw",(function(e){throw e})),r("return"),t[Symbol.iterator]=function(){return this},t;function r(r,i){t[r]=e[r]?function(t){return(n=!n)?{value:T(e[r](t)),done:"return"===r}:i?i(t):t}:i}}function S(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t,n=e[Symbol.asyncIterator];return n?n.call(e):(e=f(e),t={},r("next"),r("throw"),r("return"),t[Symbol.asyncIterator]=function(){return this},t);function r(n){t[n]=e[n]&&function(t){return new Promise((function(r,i){(function(e,t,n,r){Promise.resolve(r).then((function(t){e({value:t,done:n})}),t)})(r,i,(t=e[n](t)).done,t.value)}))}}}function _(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e}var w=Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t};function E(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&p(t,e,n);return w(t,e),t}function D(e){return e&&e.__esModule?e:{default:e}}function I(e,t,n,r){if("a"===n&&!r)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!r:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===n?r:"a"===n?r.call(e):r?r.value:t.get(e)}function C(e,t,n,r,i){if("m"===r)throw new TypeError("Private method is not writable");if("a"===r&&!i)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!i:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===r?i.call(e,n):i?i.value=n:t.set(e,n),n}}},t={};function n(r){var i=t[r];if(void 0!==i)return i.exports;var o=t[r]={exports:{}};return e[r](o,o.exports,n),o.exports}n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r={};return(()=>{var e=r;Object.defineProperty(e,"__esModule",{value:!0}),e.GanttView=e.default=void 0,n(8721);var t=n(2366);Object.defineProperty(e,"default",{enumerable:!0,get:function(){return t.GanttView}}),Object.defineProperty(e,"GanttView",{enumerable:!0,get:function(){return t.GanttView}})})(),r})()));