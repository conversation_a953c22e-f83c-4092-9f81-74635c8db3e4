﻿using System.ComponentModel.DataAnnotations;

namespace OHMC.Core.Models
{
    public class FormularySchemeRuleAudit : BaseAudit
    {
        [Key]
        public int FormularySchemeRulesAuditId { get; set; }
        public int FormularySchemeRuleId { get; set; }
        [MaxLength(30)]
        public string FormularyType { get; set; } = string.Empty;
        [MaxLength(100)]
        public string ModelType { get; set; } = string.Empty;
        public decimal ModelParameter { get; set; }
        public int MedicalSchemeId { get; set; }
        public int MedicalSchemeBenefitPlanId { get; set; }
        [MaxLength(20)]
        public string LevelOfCare { get; set; } = string.Empty;
        public bool IsActive { get; set; }
    }
}
