using FoolProof.Core;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Localization;
using Microsoft.EntityFrameworkCore;
using OHMC.Application;
using OHMC.Core.Models.Settings;
using OHMC.DataAccess;
using OHMC.DataAccess.Interface;
using OHMC.DataAccess.Repository;
using OHMC.ProtocolsApp.Hubs;
using Serilog;
using System.Globalization;

var builder = WebApplication.CreateBuilder(args);
// Add serilog services to the container and read config from appsettings
builder.Host.UseSerilog((context, configuration) =>
    configuration.ReadFrom.Configuration(context.Configuration));

IConfiguration Configuration = builder.Configuration;

builder.Services.AddDbContext<ApplicationDbContext>(options =>
{
    options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnection"));

});

builder.Services.AddAuthentication(CookieAuthenticationDefaults.AuthenticationScheme)
    .AddCookie(opt => opt.LoginPath = "/Account/SignIn");

builder.Services.Configure<ReportConfig>(Configuration.GetSection("ReportServerSettings"));

builder.Services.AddScoped<IUserServiceRepository, UserServiceRepository>();
builder.Services.AddScoped<IHasher, Hasher>();
builder.Services.AddScoped<IINNRepository, INNRepository>();
builder.Services.AddScoped<ICancerGroupRepository, CancerGroupRepository>();
builder.Services.AddScoped<ICancerGroupTypeRepository, CancerGroupTypeRepository>();
builder.Services.AddScoped<IEvidenceRepository, EvidenceRepository>();
builder.Services.AddScoped<IRouteRepository, RouteRepository>();
builder.Services.AddScoped<IStrengthRepository, StrengthRepository>();
builder.Services.AddScoped<IDispensingUnitRepository, DispensingUnitRepository>();
builder.Services.AddScoped<IFixedSettingsRepository, FixedSettingsRepository>();
builder.Services.AddScoped<IDoseUnitRepository, DoseUnitRepository>();
builder.Services.AddScoped<IFormTypeRepository, FormTypeRepository>();
builder.Services.AddScoped<IDosageFormRepository, DosageFormRepository>();
builder.Services.AddScoped<IFormulationRepository, FormulationRepository>();
builder.Services.AddScoped<IFormulationRoutesRepository, FormulationRoutesRepository>();
builder.Services.AddScoped<IProductRepository, ProductRepository>();
builder.Services.AddScoped<IProductRoutesRepository, ProductRoutesRepository>();
builder.Services.AddScoped<IDoseRepository, DoseRepository>();
builder.Services.AddScoped<IDoseProductRepository, DoseProductRepository>();
builder.Services.AddScoped<IMedPraxProductRepository, MedPraxProductRepository>();
builder.Services.AddScoped<IDosingRegimenRepository, DosingRegimenRepository>();
builder.Services.AddScoped<IRegimenRepository, RegimenRepository>();
builder.Services.AddScoped<IRegimenDosingRegimenRepository, RegimenDosingRegimenRepository>();
builder.Services.AddScoped<IProtocolRepository, ProtocolRepository>();
builder.Services.AddScoped<IProtocolStatusRepository, ProtocolStatusRepository>();
builder.Services.AddScoped<IProtocolEvidenceRepository, ProtocolEvidenceRepository>();
builder.Services.AddScoped<IProtocolTreatmentIntentRepository, ProtocolTreatmentIntentRepository>();
builder.Services.AddScoped<IChapterRepository, ChapterRepository>();
builder.Services.AddScoped<IModalityRepository, ModalityRepository>();
builder.Services.AddScoped<IProductDocumentRepository, ProductDocumentRepository>();
builder.Services.AddScoped<IDispensingAbbreviationRepository, DispensingAbbreviationRepository>();
builder.Services.AddScoped<INDODProductRepository, NDODProductRepository>();
builder.Services.AddScoped<ITherapeuticRepository, TherapeuticRepository>();
builder.Services.AddScoped<IMappingDoseStrengthUnitRepository, MappingDoseStrengthUnitRepository>();
builder.Services.AddScoped<IRegimenStatusRepository, RegimenStatusRepository>();
builder.Services.AddScoped<INotificationRepository, NotificationRepository>();
builder.Services.AddScoped<IFormularyTypeRepository, FormularyTypeRepository>();
builder.Services.AddScoped<IPTCTypeRepository, PTCTypeRepository>();
builder.Services.AddScoped<IPhaseTypeRepository, PhaseTypeRepository>();
builder.Services.AddScoped<IBlindingTypeRepository, BlindingTypeRepository>();
builder.Services.AddScoped<IEvidenceTypeRepository, EvidenceTypeRepository>();
builder.Services.AddScoped<IOutcomeTypeRepository, OutcomeTypeRepository>();
builder.Services.AddScoped<IFormularySchemeRuleRepository, FormularySchemeRuleRepository>();
builder.Services.AddScoped<IMedicalSchemeRepository, MedicalSchemeRepository>();
builder.Services.AddScoped<IMedicalSchemeBenefitPlanRepository, MedicalSchemeBenefitPlanRepository>();
builder.Services.AddScoped<ITariffRepository, TariffRepository>();
builder.Services.AddScoped<ITariffPeriodRepository, TariffPeriodRepository>();
builder.Services.AddScoped<ITariffTypeRepository, TariffTypeRepository>();
builder.Services.AddScoped<IRegimenCostRepository, RegimenCostRepository>();
builder.Services.AddScoped<IICD10Repository, ICD10Repository>();
builder.Services.AddScoped<IOutOfStockSourceRepository, OutOfStockSourceRepository>();
builder.Services.AddScoped<IOutOfStockReasonRepository, OutOfStockReasonRepository>();
builder.Services.AddScoped<IStockStatusRepository, StockStatusRepository>();
builder.Services.AddScoped<IMedicineAvailabilityRepository, MedicineAvailabilityRepository>();
builder.Services.AddScoped<IOutOfStockInternationalSignalRepository, OutOfStockInternationalSignalRepository>();
builder.Services.AddScoped<IOutOfStockRemedyRepository, OutOfStockRemedyRepository>();
builder.Services.AddScoped<ISupplierRepository, SupplierRepository>();
builder.Services.AddScoped<IAgencyRepository, AgencyRepository>();
builder.Services.AddScoped<IProtocolICD10Repository, ProtocolICD10Repository>();
builder.Services.AddScoped<IProtocolUserReviewRepository, ProtocolUserReviewRepository>();
builder.Services.AddScoped<INetworkMemberRepository, NetworkMemberRepository>();
builder.Services.AddScoped<INetworkMemberSpecialityRepository, NetworkMemberSpecialityRepository>();
builder.Services.AddScoped<INetworkPracticeRepository, NetworkPracticeRepository>();
builder.Services.AddScoped<INetworkPracticeSpecialityRepository, NetworkPracticeSpecialityRepository>();
builder.Services.AddScoped<IOrganisationRepository, OrganisationRepository>();
builder.Services.AddScoped<IGuidelineRepository, GuidelineRepository>();
builder.Services.AddScoped<IProtocolGuidelineRepository, ProtocolGuidelineRepository>();
builder.Services.AddScoped<IProtocolHeaderRepository, ProtocolHeaderRepository>();
builder.Services.AddScoped<IQueryRepository, QueryRepository>();
builder.Services.AddScoped<IUnitOfWork, UnitOfWork>();

builder.Services.AddScoped<ICancerGroupsBL, CancerGroupsBL>();
builder.Services.AddScoped<IINNBL, INNBL>();
builder.Services.AddScoped<IEvidenceBL, EvidenceBL>();
builder.Services.AddScoped<IRoutesBL, RoutesBL>();
builder.Services.AddScoped<IStrengthsBL, StrengthsBL>();
builder.Services.AddScoped<IDispensingUnitBL, DispensingUnitBL>();
builder.Services.AddScoped<IFixedSettingsBL, FixedSettingsBL>();
builder.Services.AddScoped<IDoseUnitsBL, DoseUnitsBL>();
builder.Services.AddScoped<IFormTypeBL, FormTypeBL>();
builder.Services.AddScoped<IDosageFormBL, DosageFormBL>();
builder.Services.AddScoped<IFormulationBL, FormulationBL>();
builder.Services.AddScoped<IFormulationRoutesBL, FormulationRoutesBL>();
builder.Services.AddScoped<IProductBL, ProductBL>();
builder.Services.AddScoped<IProductRoutesBL, ProductRoutesBL>();
builder.Services.AddScoped<IDoseBL, DoseBL>();
builder.Services.AddScoped<IDosingRegimenBL, DosingRegimenBL>();
builder.Services.AddScoped<IRegimenBL, RegimenBL>();
builder.Services.AddScoped<IProtocolBL, ProtocolBL>();
builder.Services.AddScoped<IChapterBL, ChapterBL>();
builder.Services.AddScoped<IModalityBL, ModalityBL>();
builder.Services.AddScoped<IDispensingAbbreviationBL, DispensingAbbreviationBL>();
builder.Services.AddScoped<IMedPraxProductsBL, MedPraxProductsBL>();
builder.Services.AddScoped<INDOHProductsBL, NDOHProductsBL>();
builder.Services.AddScoped<IPrintServiceBL, PrintServiceBL>();
builder.Services.AddScoped<ITherapeuticBL, TherapeuticBL>();
builder.Services.AddScoped<IMappingDoseStrengthUnitBL, MappingDoseStrengthUnitBL>();
builder.Services.AddScoped<INotificationBL, NotificationBL>();
builder.Services.AddScoped<IPTCTypeBL, PTCTypeBL>();
builder.Services.AddScoped<IFormularyTypeBL, FormularyTypeBL>();
builder.Services.AddScoped<IPhaseTypeBL, PhaseTypeBL>();
builder.Services.AddScoped<IBlindingTypeBL, BlindingTypeBL>();
builder.Services.AddScoped<IEvidenceTypeBL, EvidenceTypeBL>();
builder.Services.AddScoped<IOutcomeTypeBL, OutcomeTypeBL>();
builder.Services.AddScoped<IFormularySchemeRuleBL, FormularySchemeRuleBL>();
builder.Services.AddScoped<IMedicalSchemeBL, MedicalSchemeBL>();
builder.Services.AddScoped<IMedicalSchemeBenefitPlanBL, MedicalSchemeBenefitPlanBL>();
builder.Services.AddScoped<ITariffBL, TariffBL>();
builder.Services.AddScoped<ITariffTypeBL, TariffTypeBL>();
builder.Services.AddScoped<IRegimenCostBL, RegimenCostBL>();
builder.Services.AddScoped<IICD10BL, ICD10BL>();
builder.Services.AddScoped<IOutOfStockReasonsBL, OutOfStockReasonsBL>();
builder.Services.AddScoped<IOutOfStockSourcesBL, OutOfStockSourcesBL>();
builder.Services.AddScoped<IStockStatusBL, StockStatusBL>();
builder.Services.AddScoped<IOutOfStockInternationalSignalsBL, OutOfStockInternationalSignalsBL>();
builder.Services.AddScoped<IOutOfStockRemedyBL, OutOfStockRemedyBL>();
builder.Services.AddScoped<ISupplierBL, SupplierBL>();
builder.Services.AddScoped<IAgencyBL, AgencyBL>();
builder.Services.AddScoped<INetworkMemberBL, NetworkMemberBL>();
builder.Services.AddScoped<INetworkPracticeBL, NetworkPracticeBL>();
builder.Services.AddScoped<IOrganisationBL, OrganisationBL>();
builder.Services.AddScoped<IGuidelineBL, GuidelineBL>();
builder.Services.AddScoped<ICancerGroupICD10Repository, CancerGroupICD10Repository>();
builder.Services.AddScoped<IQueryReportsBL, QueryReportsBL>();
builder.Services.AddFoolProof();

//localization
builder.Services.AddLocalization();

// Add services to the container.
//builder.Services.AddControllersWithViews();
builder.Services.AddControllersWithViews().AddJsonOptions(options => options.JsonSerializerOptions.PropertyNamingPolicy = null);

builder.Services.AddSignalR();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Home/Error");
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseSerilogRequestLogging();

var supportedCultures = new[]
{
 new CultureInfo("en-US")
};

app.UseRequestLocalization(new RequestLocalizationOptions
{
    DefaultRequestCulture = new RequestCulture("en-US"),
    // Formatting numbers, dates, etc.
    SupportedCultures = supportedCultures,
    // UI strings that we have localized.
    SupportedUICultures = supportedCultures
});

app.UseStaticFiles();

app.UseAuthentication();
app.UseRouting();

app.UseAuthorization();

app.MapControllerRoute(
    name: "default",
    pattern: "{controller=Home}/{action=Index}/{id?}");

app.MapHub<ChatHub>("/chatHub");

app.Run();
