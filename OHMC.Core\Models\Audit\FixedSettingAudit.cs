﻿using System.ComponentModel.DataAnnotations;

namespace OHMC.Core.Models
{
    public class FixedSettingAudit : BaseAudit
    {
        [Key]
        public int FixedSettingAuditId { get; set; }
        public int FixedSettingId { get; set; }
        public decimal BSA { get; set; } = 0;
        public decimal Weight { get; set; } = 0;
        public decimal? VATPercentage { get; set; }
        public int? TariffYearUpdate { get; set; }
    }
}
