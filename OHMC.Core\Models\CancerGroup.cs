﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace OHMC.Core.Models
{
    public class CancerGroup : ModelBase
    {
        [Key]
        public int Id { get; set; }
        [Required, <PERSON><PERSON><PERSON><PERSON>(20), Di<PERSON><PERSON><PERSON><PERSON>("Group Code")]
        public string GroupCode { get; set; } = string.Empty;
        [Required, <PERSON><PERSON><PERSON><PERSON>(100), Disp<PERSON><PERSON><PERSON>("Group Name")]
        public string GroupName { get; set; } = string.Empty;
        [ForeignKey("PTC"), Display<PERSON><PERSON>("PTC")]
        public int? PTCTypeId { get; set; }
        public PTCType PTCType { get; set; }
        [ForeignKey("FormularyType"), Di<PERSON><PERSON><PERSON><PERSON>("Formulary Type")]
        public int FormularyTypeId { get; set; }
        public FormularyType FormularyType { get; set; }
        public int? PrintOrder { get; set; }
    }
}
