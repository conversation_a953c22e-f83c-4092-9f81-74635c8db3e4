﻿namespace OHMC.Core.Common.DataArrays
{
    public class SimpleLookUpData
    {
        public partial class NetworkData
        {
            public static readonly IEnumerable<string> SpecialistList = new[] { "", "Clinical", "Haematology", "Oncology" };
            public static readonly IEnumerable<string> ProvinceList = new[] { "", "Eastern Cape", "Free State", "Gauteng", "KwaZulu-Natal", "Limpopo", "Mpumalanga", "Northern Cape", "North West", "Western Cape" };
            public static readonly IEnumerable<string> LevelOfCareList = new[] { "I", "II", "III", "IV", "V" };
            public static readonly IEnumerable<RestrictionLevel> RestrictionLevelList = new[] { 
                                                                                                new RestrictionLevel { Id = 1, Description = "2 - ADMIN" }, 
                                                                                                new RestrictionLevel { Id = 3, Description = "3 - OHMC" }, 
                                                                                                new RestrictionLevel { Id = 4, Description = "4 - PTC" } 
                                                                                                };
        }


        public class RestrictionLevel
        {
            public int Id { get; set; }
            public string Description { get; set; } = string.Empty;
        }
    }        
}
