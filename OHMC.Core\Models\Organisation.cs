﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace OHMC.Core.Models
{
    public class Organisation : ModelBase
    {
        [Key]
        public int Id { get; set; }
        [MaxLength(20)]
        public string Abbreviation { get; set; } = string.Empty;
        [MaxLength(100)]
        public string Description { get; set; } = string.Empty;
        [MaxLength(100), <PERSON>splayName("Web Site")]
        public string WebAddress { get; set; } = string.Empty;
        public bool Local { get; set; }
    }
}
