﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace OHMC.DataAccess.Migrations
{
    public partial class Guideline_AddReferenceDisplayNameNatureOfGuidelineNull : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "NatureOfGuideline",
                table: "GuidelinesAudit",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(200)",
                oldMaxLength: 200);

            migrationBuilder.AddColumn<string>(
                name: "GuidelineDisplayName",
                table: "GuidelinesAudit",
                type: "nvarchar(250)",
                maxLength: 250,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ReferenceEvidence",
                table: "GuidelinesAudit",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "NatureOfGuideline",
                table: "Guidelines",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(200)",
                oldMaxLength: 200);

            migrationBuilder.AddColumn<string>(
                name: "GuidelineDisplayName",
                table: "Guidelines",
                type: "nvarchar(250)",
                maxLength: 250,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ReferenceEvidence",
                table: "Guidelines",
                type: "nvarchar(max)",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "GuidelineDisplayName",
                table: "GuidelinesAudit");

            migrationBuilder.DropColumn(
                name: "ReferenceEvidence",
                table: "GuidelinesAudit");

            migrationBuilder.DropColumn(
                name: "GuidelineDisplayName",
                table: "Guidelines");

            migrationBuilder.DropColumn(
                name: "ReferenceEvidence",
                table: "Guidelines");

            migrationBuilder.AlterColumn<string>(
                name: "NatureOfGuideline",
                table: "GuidelinesAudit",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(200)",
                oldMaxLength: 200,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "NatureOfGuideline",
                table: "Guidelines",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(200)",
                oldMaxLength: 200,
                oldNullable: true);
        }
    }
}
