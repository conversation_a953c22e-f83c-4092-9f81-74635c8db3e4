﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace OHMC.Core.Models.Audit
{
    public class ProtocolUserReviewAudit : BaseAudit
    {
        [Key]
        public int ProtocolUserReviewAuditId { get; set; }
        public int ProtocolUserReviewId { get; set; }
        public int ProtocolId { get; set; }
        public int OrderNumber { get; set; }
        [MaxLength(20)]
        public string? UserAction { get; set; }
        [MaxLength(400)]
        public string? Comment { get; set; }
        public bool IsResolved { get; set; }
        [MaxLength(100)]
        public string? UserName { get; set; } = string.Empty;
    }
}
