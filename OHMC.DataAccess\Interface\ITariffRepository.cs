﻿using OHMC.Core.Models;

namespace OHMC.DataAccess.Interface
{
    public interface ITariffRepository
    {
        void Add(Tariff tariff);
        Task<bool> DoTariffsExistByCodeAsync(int tariffId, string code);
        Task<TariffPeriod> GetCurrentTarifPeriodAsync(int tariffId);
        Task<Tariff> GetTariffAsync(int id);
        Task<IEnumerable<Tariff>> GetTariffsAsync();
        void Update(Tariff tariff);
        Task<int> UpdateProfessionalFeesAsync();
    }
}