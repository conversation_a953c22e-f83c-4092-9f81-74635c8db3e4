﻿using OHMC.Core.Common.Response;
using OHMC.Core.Models;

namespace OHMC.DataAccess.Interface
{
    public interface IRegimenBL
    {
        Task<IEnumerable<Regimen>> GetPagedRegimensAsync(int pageNumber, int pageSize);
        Task<IEnumerable<Regimen>> GetRegimenAsync();
        Task<Regimen> GetRegimenAsync(int id);
        Task<Regimen> GetRegimenFullAsync(int id);
        Task<int> GetRegimenCountAsync();
        Task<int> SaveAsync();
        Task<IEnumerable<Regimen>> SearchRegimenAsync(string desc);
        Task<string> GetAllRegimeDosingRegimenUsedDescription(int regimeId);
        Task<int> UpSert(Regimen regimen, int userId);
        Task<int> UpSertRegimenDosingRegimen(RegimenDosingRegimen regimenDosingRegimen, int userId);
        Task<RegimenDosingRegimen> GetRegimenDosingRegimenAsync(int id);
        Task<int> RemoveRegimenDosingRegimenAsync(RegimenDosingRegimen regimenDosingRegimen);
        Task<CopyRegimenResponse> CopyRegimenAsync(int id, int userId);
        Task<RegimenStatus> GetLatestRegimenStatusAsync(int regimenId);
        Task<int> UpSertRegimenStatus(RegimenStatus regimentatus, int userId);
        //Task<string> GetNextRegimenCode(string prefix, int increment);
        Task<string> GenerateRegimenCode(string prefix);
        Task<bool> DoesCodeExists(string code, int regimenId);
    }
}