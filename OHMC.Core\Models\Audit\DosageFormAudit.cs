﻿using System.ComponentModel.DataAnnotations;

namespace OHMC.Core.Models
{
    public class DosageFormAudit : BaseAudit
    {
        [Key]
        public int DosageFormAuditId { get; set; }
        public int DosageFormId { get; set; }
        [MaxLength(30)]
        public string FormTypeName { get; set; } = string.Empty;
        [MaxLength(50)]
        public string FormDescription { get; set; } = string.Empty;
    }
}
