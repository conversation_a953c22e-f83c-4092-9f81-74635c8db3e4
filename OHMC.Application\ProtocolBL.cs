﻿using Microsoft.Extensions.Logging;
using OHMC.Core.Common.Const;
using OHMC.Core.Common.Response;
using OHMC.Core.Common.Utils;
using OHMC.Core.Models;
using OHMC.DataAccess.Interface;

namespace OHMC.Application
{
    public class ProtocolBL : IProtocolBL
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<ProtocolBL> _logger;
        private const int FILLER_COUNT = 10;
        public ProtocolBL(IUnitOfWork unitOfWork, ILogger<ProtocolBL> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<IEnumerable<Protocol>> GetProtocolsAsync()
        {
            return await _unitOfWork.ProtocolRepository.GetProtocolsAsync();
        }

        public async Task<IEnumerable<Protocol>> GetPagedProtocolsAsync(int pageNumber, int pageSize)
        {
            return await _unitOfWork.ProtocolRepository.GetPagedProtocolsAsync(pageNumber, pageSize);
        }

        public async Task<int> GetProtocolCountAsync()
        {
            return await _unitOfWork.ProtocolRepository.GetProtocolCountAsync();
        }

        public async Task<IEnumerable<Protocol>> SearchProtocolAsync(string desc)
        {
            return await _unitOfWork.ProtocolRepository.SearchProtocolsAsync(desc);
        }

        public async Task<IEnumerable<Protocol>> SearchProtocolByStatusAsync(string desc)
        {
            return await _unitOfWork.ProtocolRepository.SearchProtocolByStatusAsync(desc);
        }

        public async Task<IEnumerable<Protocol>> SearchProtocolsByCancerTypeGroupAsync(string cancerType, string cancerGroup)
        {
            return await _unitOfWork.ProtocolRepository.SearchProtocolsByCancerTypeGroupAsync(cancerType, cancerGroup);
        }

        public async Task<IEnumerable<Protocol>> SearchProtocolsByCancerTypeAsync(string cancerType)
        {
            return await _unitOfWork.ProtocolRepository.SearchProtocolsByCancerTypeAsync(cancerType);
        }

        public async Task<IEnumerable<Protocol>> SearchProtocolsByCancerGroupAsync(string cancerGroup)
        {
            return await _unitOfWork.ProtocolRepository.SearchProtocolsByCancerGroupAsync(cancerGroup);
        }

        public async Task<IEnumerable<Protocol>> SearchProtocolsByChapterAsync(string chapter)
        {
            return await _unitOfWork.ProtocolRepository.SearchProtocolsByChapterAsync(chapter);
        }

        public async Task<Protocol> GetProtocolAsync(int id)
        {
            return await _unitOfWork.ProtocolRepository.GetProtocolAsync(id);
        }

        public async Task<Protocol> GetProtocolFullAsync(int id)
        {
            return await _unitOfWork.ProtocolRepository.GetProtocolFullAsync(id);
        }

        public async Task<ProtocolStatus> GetLatestProtocolStatusAsync(int protocolId)
        {
            return await _unitOfWork.ProtocolStatusRepository.GetLatestProtocolStatusAsync(protocolId);
        }

        public async Task<IEnumerable<ProtocolEvidence>> GetProtocolEvidences(int protocolId)
        {
            return await _unitOfWork.ProtocolEvidenceRepository.GetProtocolEvidencesAsync(protocolId);
        }

        public async Task<IEnumerable<ProtocolICD10Code>> GetProtocolICD10s(int protocolId)
        {
            return await _unitOfWork.ProtocolICD10Repository.GetProtocolICD10CodeByProtocolAsync(protocolId);
        }

        public async Task<ProtocolICD10Code> GetProtocolICD10Async(int id)
        {
            return await _unitOfWork.ProtocolICD10Repository.GetProtocolICD10Async(id);
        }

        public async Task<string> GetProtocolICD10sDescription(int protocolId)
        {
            return await _unitOfWork.ProtocolICD10Repository.GetProtocolICD10sDescription(protocolId);
        }

        public async Task<IEnumerable<ProtocolICD10Code>> GetProtocolICD10CodeByProtocolAsync(int protocolId)
        {
            return await _unitOfWork.ProtocolICD10Repository.GetProtocolICD10CodeByProtocolAsync(protocolId);
        }

        public async Task<int> UpSert(Protocol protocol, int userId)
        {
            bool _addingProtocol = true;

            protocol.UpdatedBy = userId;
            protocol.UpdatedDate = DateTime.Now;

            if (protocol.Id > 0)
            {
                if (string.IsNullOrEmpty(protocol.Status))
                {
                    var latestProtocolStatus = await GetLatestProtocolStatusAsync(protocol.Id);
                    if (latestProtocolStatus != null)
                    {
                        protocol.Status = latestProtocolStatus.Status;
                    }
                    else
                    {
                        protocol.Status = StrConst.PROTOCOL_STATUS_PENDING;
                    }
                }

                _unitOfWork.ProtocolRepository.Update(protocol);
                _logger.LogInformation($"{StrConst.LOG_TYPE_UPDATE}: {protocol.ProtocolName}, USERID: {userId}");
                _addingProtocol = false;
            }
            else
            {
                protocol.CreateBy = userId;
                protocol.CreatedDate = DateTime.Now;
                protocol.Code = await GetNextProtocolCodeReference(protocol.ProtocolName.Substring(0, 1));
                protocol.OMCode = protocol.Code;
                protocol.Status = StrConst.PROTOCOL_STATUS_PENDING;

                _unitOfWork.ProtocolRepository.Add(protocol);
                _logger.LogInformation($"{StrConst.LOG_TYPE_ADDING}: {protocol.ProtocolName}, USERID: {userId}");
            }

            await SaveAsync();

            if (_addingProtocol)
            {
                await UpSertProtocolStatus(new ProtocolStatus()
                {
                    ProtocolId = protocol.Id,
                    Status = StrConst.PROTOCOL_STATUS_ACTIVE
                }, userId);
            }

            return protocol.Id;
        }

        public async Task<int> UpSertProtocolStatus(ProtocolStatus status, int userId)
        {
            status.UpdatedBy = userId;
            status.UpdatedDate = DateTime.Now;

            if (status.Id > 0)
            {
                _unitOfWork.ProtocolStatusRepository.Update(status);
            }
            else
            {
                status.CreateBy = userId;
                status.CreatedDate = DateTime.Now;

                _unitOfWork.ProtocolStatusRepository.Add(status);
            }

            return await SaveAsync();
        }

        public async Task<int> UpSertProtocolEvidence(ProtocolEvidence evidence, int userId)
        {
            evidence.UpdatedBy = userId;
            evidence.UpdatedDate = DateTime.Now;

            if (evidence.Id > 0)
            {
                _unitOfWork.ProtocolEvidenceRepository.Update(evidence);
            }
            else
            {
                evidence.CreateBy = userId;
                evidence.CreatedDate = DateTime.Now;
                evidence.ReferenceNumber = await _unitOfWork.ProtocolEvidenceRepository.GetMaxReferenceNumber() + 1;

                _unitOfWork.ProtocolEvidenceRepository.Add(evidence);
            }

            return await SaveAsync();
        }

        public async Task<int> UpSertProtocolICD10(ProtocolICD10Code icd10, int userId)
        {
            icd10.UpdatedBy = userId;
            icd10.UpdatedDate = DateTime.Now;

            if (icd10.Id > 0)
            {
                _unitOfWork.ProtocolICD10Repository.Update(icd10);
            }
            else
            {
                icd10.CreateBy = userId;
                icd10.CreatedDate = DateTime.Now;
                _unitOfWork.ProtocolICD10Repository.Add(icd10);
            }

            return await SaveAsync();
        }

        public async Task<int> UpSertProtocolUserReview(ProtocolUserReview review, int userId)
        {
            review.UpdatedBy = userId;
            review.UpdatedDate = DateTime.Now;

            if (review.Id > 0)
            {
                _unitOfWork.ProtocolUserReviewRepository.Update(review);
            }
            else
            {
                review.CreateBy = userId;
                review.CreatedDate = DateTime.Now;
                _unitOfWork.ProtocolUserReviewRepository.Add(review);
            }

            return await SaveAsync();
        }

        private async Task<string> GetNextProtocolCodeReference(string prefix)
        {
            return StringHelper.AddDefaultPrefix("OM" + prefix, ((await _unitOfWork.ProtocolRepository.GetNextProtocolCodeReference(prefix) + 1) * 20).ToString(), FILLER_COUNT);
        }

        public async Task<int> SaveAsync()
        {
            return await _unitOfWork.SaveAsync();
        }

        public async Task<IEnumerable<Protocol>> GetPagedProtocolItemsAsync(int pageNumber, int pageSize)
        {
            return await _unitOfWork.ProtocolRepository.GetPagedProtocolItemsAsync(pageNumber, pageSize);
        }

        public async Task<ProtocolEvidence> GetProtocolEvidenceAsync(int protocolEvidenceId)
        {
            return await _unitOfWork.ProtocolEvidenceRepository.GetProtocolEvidenceAsync(protocolEvidenceId);
        }

        public async Task<int> RemoveProtocolEvidenceAsync(ProtocolEvidence evidence)
        {
            _unitOfWork.ProtocolEvidenceRepository.RemoveProtocolEvidence(evidence);
            return await _unitOfWork.SaveAsync();
        }

        public async Task<bool> IsProtocolCompleteAsync(int protocolId)
        {
            var protocol = await _unitOfWork.ProtocolRepository.GetProtocolFullAsync(protocolId);

            var protolHasEvidence = (await _unitOfWork.ProtocolEvidenceRepository.GetProtocolEvidencesAsync(protocolId)).Any();

            return protolHasEvidence;
        }

        public async Task<bool> IsProtocolReferenced(int protocolId)
        {
            return (await _unitOfWork.ProtocolEvidenceRepository.GetProtocolEvidencesAsync(protocolId)).Any();
        }

        public async Task<CopyProtocolResponse> CopyProtocolAsync(int id, int userId)
        {            
                var protocol = await _unitOfWork.ProtocolRepository.GetProtocolAsync(id);
                var protocolEvidenceList = await _unitOfWork.ProtocolEvidenceRepository.GetProtocolEvidencesAsync(id);

                var newProtocol = new Protocol()
                {
                    ProtocolName = protocol.ProtocolName,
                    Chapter = protocol.Chapter,
                    CancerGroupName = protocol.CancerGroupName,
                    CancerType = protocol.CancerType,
                    LevelOfCare = protocol.LevelOfCare,
                    Code = await GetNextProtocolCodeReference(protocol.ProtocolName.Substring(0, 1)), 
                    OMCode = protocol.OMCode,
                    XCode = protocol.XCode,
                    EndComment = protocol.EndComment,
                    HTANumber = protocol.HTANumber,
                    IsEML = protocol.IsEML,
                    IsPMB = protocol.IsPMB,
                    IsTender = protocol.IsTender,
                    IsProtocolSupportedByHTA = protocol.IsProtocolSupportedByHTA,
                    PMBIssueDate = protocol.PMBIssueDate,
                    PMBNumber = protocol.PMBNumber,
                    RegimenId = protocol.RegimenId,
                    Restriction = protocol.Restriction,
                    RestrictionDescription = protocol.RestrictionDescription,
                    ReviewIndicator = protocol.ReviewIndicator,
                    ReviewIndicatorDescription = protocol.ReviewIndicatorDescription,
                    ReviewEndDate = protocol.ReviewEndDate,
                    ReviewStartDate = protocol.ReviewStartDate,
                    TenderStartDate = protocol.TenderStartDate,
                    TenderEndDate = protocol.TenderEndDate,
                    TenderNumber = protocol.TenderNumber,
                    TreatmentIntent = protocol.TreatmentIntent,
                    TreatmentOption = protocol.TreatmentOption,
                    TotalCostPerCourse = protocol.TotalCostPerCourse,
                    TotalCostPerCycle = protocol.TotalCostPerCycle,
                    FormularyTypeId = protocol.FormularyTypeId,
                    CancerTypeId = protocol.CancerTypeId,
                    CancerGroupId = protocol.CancerGroupId,
                    TreatmentOptionId = protocol.TreatmentOptionId,
                    PTCTypeId = protocol.PTCTypeId,
                    ChapterId = protocol.ChapterId,
                    CreateBy = userId,
                    CreatedDate = DateTime.Now,
                    UpdatedBy = userId,
                    UpdatedDate = DateTime.Now,
                    Deleted = false,
                };

                _unitOfWork.ProtocolRepository.Add(newProtocol);

            try
            {
                await SaveAsync();

                await UpSertProtocolStatus(new ProtocolStatus()
                {
                    ProtocolId = newProtocol.Id,
                    Status = StrConst.PROTOCOL_STATUS_ACTIVE
                }, userId);

                //Add List of Evidence
                foreach (var evidence in protocolEvidenceList)
                {
                    _unitOfWork.ProtocolEvidenceRepository.Add(new ProtocolEvidence()
                    {
                        ProtocolId = newProtocol.Id,
                        Blinding = evidence.Blinding,
                        CommentClinical = evidence.CommentClinical,
                        CommentCriticalAppraisal = evidence.CommentCriticalAppraisal,
                        Comparator = evidence.Comparator,
                        EvidenceDescription = evidence.EvidenceDescription,
                        EvidenceTrialName = evidence.EvidenceTrialName,
                        EvidencePublicationYear = evidence.EvidencePublicationYear,
                        EvidenceType = evidence.EvidenceType,
                        LevelOfEvidence = evidence.LevelOfEvidence,
                        ObservationPeriod = evidence.ObservationPeriod,
                        SampleSize = evidence.SampleSize,
                        Phase = evidence.Phase,
                        Outcome1 = evidence.Outcome1,
                        Outcome1Effect = evidence.Outcome1Effect,
                        Outcome2 = evidence.Outcome2,
                        Outcome2Effect = evidence.Outcome2Effect,
                        Outcome3 = evidence.Outcome3,
                        Outcome3Effect = evidence.Outcome3Effect,
                        Outcome4 = evidence.Outcome4,
                        Outcome4Effect = evidence.Outcome4Effect,
                        Outcome5 = evidence.Outcome5,
                        Outcome5Effect = evidence.Outcome5Effect,
                        CreateBy = userId,
                        CreatedDate = DateTime.Now,
                        UpdatedBy = userId,
                        UpdatedDate = DateTime.Now,
                        Deleted = false,
                    });
                }

                await _unitOfWork.SaveAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex.Message);

                return new CopyProtocolResponse()
                {
                    ResponseCode = -1,
                    ResponseMessage = $"{StrConst.ACTION_COPY_FAILED}. Protocol Code {protocol.Code}",
                    NewCode = string.Empty,
                    NewId = -1,
                };
            }

            return new CopyProtocolResponse()
            {
                ResponseCode = newProtocol.Id,
                ResponseMessage = $"{StrConst.ACTION_COPY_SUCCESSFUL} ",
                NewCode = newProtocol.Code,
                NewId = newProtocol.Id
            };
        }

        public Task<IEnumerable<ProtocolAudit>> GetProtocolAuditHistoryAsync(int protocolId)
        {
            return _unitOfWork.ProtocolRepository.GetProtocolAuditHistoryAsync(protocolId);
        }

        public async Task<GenerateRefNumbersResponse> GenerateRefNumbers()
        {
            var response = await _unitOfWork.ProtocolEvidenceRepository.GenerateRefNumbers();
            if (response > -1)
            {
                return new GenerateRefNumbersResponse()
                {
                    ResponseMessage = "Notification-Reference numbers generated.",
                    ResponseCode = response,
                };
            }

            return new GenerateRefNumbersResponse()
            {
                ResponseMessage = "Failed - Reference number generation.",
                ResponseCode = response, 
            };
        }

        public async Task<IEnumerable<ProtocolStatus>> GetProtocolStatusesAsync(int id)
        {
            return await _unitOfWork.ProtocolStatusRepository.GetProtocolStatusesAsync(id);
        }

        public async Task<IEnumerable<ProtocolUserReview>> GetProtocolUserReviews(int protocolId)
        {
            return await _unitOfWork.ProtocolUserReviewRepository
                .GetProtocolUserReviewsByProtocolAsync(protocolId);
        }   

        public async Task<ProtocolUserReview> GetProtocolUserReview(int id)
        {
            return await _unitOfWork
                .ProtocolUserReviewRepository.GetProtocolUserReviewAsync(id);
        }

        public async Task<int> UpSertProtocolGuideline(ProtocolGuideline guideline, int userId)
        {
            guideline.UpdatedBy = userId;
            guideline.UpdatedDate = DateTime.Now;

            if (guideline.Id > 0)
            {
                _unitOfWork.ProtocolGuidelineRepository.Update(guideline);
            }
            else
            {
                guideline.CreateBy = userId;
                guideline.CreatedDate = DateTime.Now;
                var checkGuideline = await _unitOfWork.ProtocolGuidelineRepository.GetProtocolGuidelineAsync(guideline.ProtocolId, guideline.GuidelineId);
                if (checkGuideline == null)
                {                    
                    _unitOfWork.ProtocolGuidelineRepository.Add(guideline);
                }
            }

            return await SaveAsync();
        }

        public async Task<string> GetProtocolGuidelinesDescription(int protocolId)
        {
            return await _unitOfWork.GuidelineRepository.GetGuidelineDescriptionsAsync(protocolId);
        }

        public async Task<ProtocolHeader> GetProtocolHeaderAsync(int id)
        {
            return await _unitOfWork.ProtocolHeaderRepository.GetProtocolHeaderAsync(id);
        }

        public async Task<IEnumerable<ProtocolHeader>> GetProtocolHeadersAsync()
        {
            return await _unitOfWork.ProtocolHeaderRepository.GetProtocolHeadersAsync();
        }

        public async Task<int> UpSertProtocolHeader(ProtocolHeader header, int userId)
        {
            header.UpdatedBy = userId;
            header.UpdatedDate = DateTime.Now;

            if (header.Id > 0)
            {
                _unitOfWork.ProtocolHeaderRepository.Update(header);
            }
            else
            {
                header.CreateBy = userId;
                header.CreatedDate = DateTime.Now;

                _unitOfWork.ProtocolHeaderRepository.Add(header);
            }

            return await SaveAsync();
        }

        public async Task<IEnumerable<Protocol>> GetProtocolPartsAsync(int id)
        {
            return await _unitOfWork.ProtocolRepository.GetProtocolPartsAsync(id);
        }

        public Task<int> RemoveProtocolICD10Async(ProtocolICD10Code icd10)
        {
            _unitOfWork.ProtocolICD10Repository.RemoveICD10(icd10);
            return _unitOfWork.SaveAsync();
        }

        public async Task<CancerGroupICD10sToPotocolResponse> AddCancerGroupICD10s(int protocolId, int cancerGroupId, int userId)
        {
            var cancerGroupICD10s = await _unitOfWork.CancerGroupICD10Repository.GetCancerGroupICD10sByGroup(cancerGroupId);
            if (cancerGroupICD10s.Any())
            {
                foreach (var cancerGroup in cancerGroupICD10s)
                {
                    var existsIcd10 = (await _unitOfWork.ProtocolICD10Repository.GetProtocolICD10CodeByProtocolAsync(protocolId)).Where(a => a.ICD10Code == cancerGroup.ICD10Code)
                        .FirstOrDefault();
                    if (existsIcd10 == null)
                    {
                        _unitOfWork.ProtocolICD10Repository.Add(new ProtocolICD10Code()
                        {
                            ProtocolId = protocolId,
                            ICD10Code = cancerGroup.ICD10Code,
                            CreateBy = userId,
                            CreatedDate = DateTime.Now,
                            UpdatedBy = userId,
                            UpdatedDate = DateTime.Now,
                            Deleted = false
                        });
                    }
                }

                await _unitOfWork.SaveAsync();

                return new CancerGroupICD10sToPotocolResponse()
                {
                    ResponseCode = 0,
                    ResponseMessage = $"Protocol ICD10 Codes updated",
                };
            }

            return new CancerGroupICD10sToPotocolResponse()
            {
                ResponseCode = -1,
                ResponseMessage = "No ICD10 codes found for the selected cancer group.",
            };
        }

        public async Task<ProtocolICD10Code> GetProtocolICD10Async(int protocolId, string icd10)
        {
            return await _unitOfWork.ProtocolICD10Repository.GetProtocolICD10Async(protocolId, icd10);
        }
    }
}
