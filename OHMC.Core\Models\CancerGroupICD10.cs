﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace OHMC.Core.Models
{
    public class CancerGroupICD10 : ModelBase
    {
        [Key]
        public int Id { get; set; }
        [Required, <PERSON>Key("CancerGroup")]
        public int CancerGroupId { get; set; }
        public CancerGroup? CancerGroup { get; set; }
        [Required, <PERSON><PERSON>ength(20)]
        public string ICD10Code { get; set; } = string.Empty;
    }
}
