using Newtonsoft.Json.Serialization;

namespace OHMC.Core.Common.Const;

public class StrConst
{
    public const string LASTFEEDBACK = "lastFeedback";

    public const string FIELDREQUIRED = "{0} is required";
    public const string USER_FUNCTION_EDIT = "EDIT";
    public const string USER_FUNCTION_CREATE = "CREATE";
    public const string USER_FUNCTION_ADD = "ADD";
    public const string USER_FUNCTION_VIEW = "VIEW";

    public const string PROTOCOL_STATUS_ACTIVE = "ACTIVE";
    public const string PROTOCOL_STATUS_PENDING = "PENDING";
    public const string PROTOCOL_STATUS_SUPERSEDED = "SUPERSEDED";
    public const string PROTOCOL_STATUS_REMOVED = "REMOVED";

    public const string REGIMEN_STATUS_ACTIVE = "ACTIVE";
    public const string REGIMEN_STATUS_PENDING = "PENDING";
    public const string REGIMEN_STATUS_SUPERSEDED = "SUPERSEDED";
    public const string REGIMEN_STATUS_REMOVED = "REMOVED";

    public const string LOG_TYPE_ADDING = "ADDING";
    public const string LOG_TYPE_UPDATE = "UPDATE";
    public const string LOG_TYPE_REMOVING = "REMOVING";

    public const string FILTER_MESSAGE = "Showing results for text:";
    public const string SEARCH_MESSAGE = "Showing results for text:";

    public const string AUDIT_UPDATE = "UPDATE";
    public const string AUDIT_DELETE = "DELETE";

    public const string REFERENCE_FILLER = "0";

    public const string MEDIKREDIT_FILLER_SPACE = " ";
    
    public const string ACTION_DELETE_SUCCESSFUL = "Deleted successfully";
    public const string ACTION_DELETE_FAILED = "Failed to delete";
    public const string ACTION_UPDATE_SUCCESSFUL = "Updated successfully";
    public const string ACTION_UPDATE_FAILED = "Failed to update";
    public const string ACTION_COPY_SUCCESSFUL = "Copy successful";
    public const string ACTION_COPY_FAILED = "Failed to copy";

    public const string DOCUMENT_TYPE_PACKAGE_INSERT = "Package Insert";
    public const string PRODUCT_STATUS_PERMANENT = "Permanent";

    public const string NOTIFICATION_STATUS_PENDING = "Pending";
    public const string NOTIFICATION_STATUS_SENT = "Sent";
    public const string NOTIFICATION_STATUS_FAILED = "Failed";
    public const string NOTIFICATION_STATUS_READ = "Read";

    public const string NOTIFICATION_TYPE_EMAIL = "Email";
    public const string NOTIFICATION_TYPE_SMS = "SMS";
    public const string NOTIFICATION_TYPE_ALL = "ALL";

    public const string MEDICINE_OUT_OF_STOCK = "Out of Stock";
}