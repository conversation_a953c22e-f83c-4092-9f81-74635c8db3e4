﻿using OHMC.Core.Models;

namespace OHMC.DataAccess.Interface
{
    public interface IProtocolICD10Repository
    {
        void Add(ProtocolICD10Code icd10);
        void RemoveICD10(ProtocolICD10Code icd10);
        Task<ProtocolICD10Code> GetProtocolICD10Async(int id);
        Task<IEnumerable<ProtocolICD10Code>> GetProtocolICD10CodeByProtocolAsync(int protocolId);
        Task<string> GetProtocolICD10sDescription(int protocolId);
        Task<ProtocolICD10Code> GetProtocolICD10Async(int protocolId, string icd10);
        void Update(ProtocolICD10Code icd10);
    }
}