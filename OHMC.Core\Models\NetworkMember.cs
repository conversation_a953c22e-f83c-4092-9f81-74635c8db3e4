﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace OHMC.Core.Models
{
    public class NetworkMember : Person
    {
        [Key]
        public int Id { get; set; }
        [MaxLength(100)]
        public string Practice { get; set; } = string.Empty;
        [MaxLength(20)]
        public string? PracticeNo { get; set; }
        [MaxLength(20)]
        public string? HPCSANumber { get; set; }
        [MaxLength(20)]
        public string? NetworkAgreementSigned { get; set; }
        [MaxLength(20)]
        public string? OHMCMembershipApplication { get; set; }
        [ForeignKey("NetworkPractice")]
        public int? NetworkPracticeId { get; set; }
        public NetworkPractice? NetworkPractice { get; set; }
    }
}
